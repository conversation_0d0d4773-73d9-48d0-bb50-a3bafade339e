name: PR Checks

on:
  pull_request:
    branches: [main, master]

env:
  NX_CLOUD_DISTRIBUTED_EXECUTION: false

jobs:
  main:
    name: Nx Affected
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        node-version: [20]

    steps:
      - name: Checkout [Pull Request]
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}
          token: ${{ secrets.GITHUB_TOKEN }}
          fetch-depth: 0

      - name: Derive appropriate SHAs for base and head for `nx affected` commands
        uses: nrwl/nx-set-shas@v4

      - name: Set up Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci --legacy-peer-deps

      - name: Initialize the Nx Cloud distributed CI run
        run: npx nx-cloud start-ci-run --stop-agents-after="build" --agent-count=3

      - name: Run Nx Affected Lint
        run: npx nx affected -t lint --parallel=3

      - name: Run Nx Affected Type Check
        run: npx nx affected -t typecheck --parallel=3

      - name: Run Nx Affected Tests
        run: npx nx affected -t test --parallel=3 --ci --code-coverage

      - name: Stop all running agents for this CI run
        if: ${{ always() }}
        run: npx nx-cloud stop-all-agents

  agents:
    name: Nx Cloud Agent ${{ matrix.agent }}
    runs-on: ubuntu-latest
    strategy:
      matrix:
        agent: [1, 2, 3]

    steps:
      - name: Checkout [Pull Request]
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}
          token: ${{ secrets.GITHUB_TOKEN }}
          fetch-depth: 0

      - name: Derive appropriate SHAs for base and head for `nx affected` commands
        uses: nrwl/nx-set-shas@v4

      - name: Set up Node.js 20
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'

      - name: Install dependencies
        run: npm ci --legacy-peer-deps

      - name: Start Nx Agent ${{ matrix.agent }}
        run: npx nx-cloud start-agent
        env:
          NX_AGENT_NAME: agent-${{ matrix.agent }}
