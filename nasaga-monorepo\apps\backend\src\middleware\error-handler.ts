import { Request, Response, NextFunction } from 'express';
import { ApiError, HttpStatusCode } from '@nasaga-monorepo/models';

/**
 * Custom error class for API errors
 */
export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;

  constructor(message: string, statusCode: number = HttpStatusCode.INTERNAL_SERVER_ERROR) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Error handler middleware
 */
export const errorHandler = (
  error: Error | AppError,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  let statusCode = HttpStatusCode.INTERNAL_SERVER_ERROR;
  let message = 'Internal Server Error';

  // Handle custom AppError
  if (error instanceof AppError) {
    statusCode = error.statusCode;
    message = error.message;
  }
  // Handle Firebase Auth errors
  else if (error.message.includes('auth/')) {
    statusCode = HttpStatusCode.UNAUTHORIZED;
    message = getFirebaseAuthErrorMessage(error.message);
  }
  // Handle Firestore errors
  else if (error.message.includes('firestore/')) {
    statusCode = HttpStatusCode.BAD_REQUEST;
    message = 'Database operation failed';
  }
  // Handle validation errors
  else if (error.name === 'ValidationError') {
    statusCode = HttpStatusCode.BAD_REQUEST;
    message = error.message;
  }
  // Handle JSON parsing errors
  else if (error instanceof SyntaxError && 'body' in error) {
    statusCode = HttpStatusCode.BAD_REQUEST;
    message = 'Invalid JSON format';
  }

  // Log error for debugging
  console.error('Error:', {
    message: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
  });

  // Create error response
  const errorResponse: ApiError = {
    success: false,
    error: message,
    timestamp: new Date().toISOString(),
  };

  // Add stack trace in development
  if (process.env['NODE_ENV'] === 'development') {
    errorResponse.details = {
      stack: error.stack,
      originalMessage: error.message,
    };
  }

  res.status(statusCode).json(errorResponse);
};

/**
 * Get user-friendly error messages for Firebase Auth errors
 */
function getFirebaseAuthErrorMessage(errorMessage: string): string {
  if (errorMessage.includes('auth/id-token-expired')) {
    return 'Authentication token has expired';
  }
  if (errorMessage.includes('auth/id-token-revoked')) {
    return 'Authentication token has been revoked';
  }
  if (errorMessage.includes('auth/invalid-id-token')) {
    return 'Invalid authentication token';
  }
  if (errorMessage.includes('auth/user-not-found')) {
    return 'User not found';
  }
  if (errorMessage.includes('auth/user-disabled')) {
    return 'User account has been disabled';
  }
  return 'Authentication failed';
}

/**
 * Async error wrapper to catch async errors in route handlers
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Not found error handler
 */
export const notFound = (req: Request, res: Response, next: NextFunction): void => {
  const error = new AppError(`Route ${req.originalUrl} not found`, HttpStatusCode.NOT_FOUND);
  next(error);
};
