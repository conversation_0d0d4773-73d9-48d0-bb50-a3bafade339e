{"name": "nasaga-backend-functions", "version": "1.0.0", "description": "Nasaga Real Estate Management Platform Backend API - Cloud Functions", "main": "main.js", "engines": {"node": "18"}, "dependencies": {"express": "^4.21.2", "cors": "^2.8.5", "helmet": "^8.1.0", "firebase-functions": "^6.3.0", "firebase-admin": "^13.4.0"}, "scripts": {"serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "private": true}