# **API Specification**

Based on the chosen API style from the Tech Stack (REST), I will now generate a preliminary OpenAPI 3.0 specification for the backend. This specification will outline the key endpoints, define request/response schemas based on the data models, and document authentication requirements.

## **REST API Specification**

`openapi: 3.0.0`  
`info:`  
  `title: Nasaga Real Estate Management Platform API`  
  `version: 1.0.0`  
  `description: API for custom backend logic and privileged operations not directly handled by Firebase SDKs, integrating with Firebase Firestore and Admin SDK.`  
`servers:`  
  `- url: https://api.nasaga.com/v1`  
    `description: Production API server`  
`paths:`  
  `/auth/register:`  
    `post:`  
      `summary: Register a new user`  
      `requestBody:`  
        `required: true`  
        `content:`  
          `application/json:`  
            `schema:`  
              `type: object`  
              `properties:`  
                `email:`  
                  `type: string`  
                  `format: email`  
                `password:`  
                  `type: string`  
                  `minLength: 6`  
                `displayName:`  
                  `type: string`  
                `role:`  
                  `type: string`  
                  `enum: [ 'agent', 'buyer' ] # Only agents and buyers can self-register`  
      `responses:`  
        `'201':`  
          `description: User registered successfully`  
          `content:`  
            `application/json:`  
              `schema:`  
                `type: object`  
                `properties:`  
                  `uid:`  
                    `type: string`  
                  `email:`  
                    `type: string`  
                  `role:`  
                    `type: string`  
        `'400':`  
          `description: Invalid input`  
        `'409':`  
          `description: User already exists`  
  `/properties:`  
    `post:`  
      `summary: Create a new property listing`  
      `security:`  
        `- firebaseAuth: []`  
      `requestBody:`  
        `required: true`  
        `content:`  
          `application/json:`  
            `schema:`  
              `type: object`  
              `properties:`  
                `title:`  
                  `type: string`  
                `description:`  
                  `type: string`  
                `address:`  
                  `type: string`  
                `city:`  
                  `type: string`  
                `state:`  
                  `type: string`  
                `zipCode:`  
                  `type: string`  
                `price:`  
                  `type: number`  
                  `format: float`  
                `propertyType:`  
                  `type: string`  
                `bedrooms:`  
                  `type: integer`  
                `bathrooms:`  
                  `type: integer`  
                `squareFootage:`  
                  `type: number`  
                  `format: float`  
                `imageUrls:`  
                  `type: array`  
                  `items:`  
                    `type: string`  
                  `description: URLs to images in Firebase Storage`  
                `documentUrls:`  
                  `type: array`  
                  `items:`  
                    `type: string`  
                  `description: URLs to documents in Firebase Storage`  
      `responses:`  
        `'201':`  
          `description: Property created successfully (status pending)`  
        `'401':`  
          `description: Unauthorized (only agents can create)`  
        `'400':`  
          `description: Invalid input`  
  `/properties/{propertyId}/status:`  
    `patch:`  
      `summary: Update property status (Admin/Super Admin)`  
      `parameters:`  
        `- in: path`  
          `name: propertyId`  
          `required: true`  
          `schema:`  
            `type: string`  
          `description: The ID of the property to update`  
      `security:`  
        `- firebaseAuth: []`  
      `requestBody:`  
        `required: true`  
        `content:`  
          `application/json:`  
            `schema:`  
              `type: object`  
              `properties:`  
                `status:`  
                  `type: string`  
                  `enum: [ 'approved', 'rejected', 'active', 'under offer', 'sold', 'delisted' ]`  
      `responses:`  
        `'200':`  
          `description: Property status updated`  
        `'401':`  
          `description: Unauthorized`  
        `'403':`  
          `description: Forbidden (only admin/superAdmin)`  
        `'404':`  
          `description: Property not found`  
  `/admin/users/{uid}/role:`  
    `patch:`  
      `summary: Update user role (Super Admin only)`  
      `parameters:`  
        `- in: path`  
          `name: uid`  
          `required: true`  
          `schema:`  
            `type: string`  
          `description: The UID of the user to update`  
      `security:`  
        `- firebaseAuth: []`  
      `requestBody:`  
        `required: true`  
        `content:`  
          `application/json:`  
            `schema:`  
              `type: object`  
              `properties:`  
                `role:`  
                  `type: string`  
                  `enum: [ 'admin', 'agent', 'buyer', 'superAdmin' ] # Super admin can assign any role`  
      `responses:`  
        `'200':`  
          `description: User role updated`  
        `'401':`  
          `description: Unauthorized`  
        `'403':`  
          `description: Forbidden (only superAdmin)`  
        `'404':`  
          `description: User not found`  
`components:`  
  `securitySchemes:`  
    `firebaseAuth:`  
      `type: http`  
      `scheme: bearer`  
      `bearerFormat: Firebase`  
      `description: Firebase ID Token for authentication.`
