import { initializeApp, getApps, cert, App } from 'firebase-admin/app';
import { getAuth, Auth } from 'firebase-admin/auth';
import { getFirestore, Firestore } from 'firebase-admin/firestore';
import { getStorage, Storage } from 'firebase-admin/storage';

// Firebase Admin configuration interface
export interface FirebaseAdminConfig {
  projectId: string;
  clientEmail: string;
  privateKey: string;
}

// Get Firebase Admin configuration from environment variables
const getFirebaseAdminConfig = (): FirebaseAdminConfig => {
  const config = {
    projectId: process.env['FIREBASE_ADMIN_PROJECT_ID'] || '',
    clientEmail: process.env['FIREBASE_ADMIN_CLIENT_EMAIL'] || '',
    privateKey: process.env['FIREBASE_ADMIN_PRIVATE_KEY'] || '',
  };

  // Validate that all required config values are present
  const missingKeys = Object.entries(config)
    .filter(([_, value]) => !value)
    .map(([key, _]) => key);

  if (missingKeys.length > 0) {
    throw new Error(
      `Missing Firebase Admin configuration: ${missingKeys.join(', ')}. ` +
      'Please check your environment variables.'
    );
  }

  return config;
};

// Initialize Firebase Admin app
let adminApp: App;
let adminAuth: Auth;
let adminFirestore: Firestore;
let adminStorage: Storage;

export const initializeFirebaseAdmin = (): {
  app: App;
  auth: Auth;
  firestore: Firestore;
  storage: Storage;
} => {
  try {
    // Check if Firebase Admin is already initialized
    if (getApps().length === 0) {
      const config = getFirebaseAdminConfig();
      
      // Initialize with service account credentials
      adminApp = initializeApp({
        credential: cert({
          projectId: config.projectId,
          clientEmail: config.clientEmail,
          privateKey: config.privateKey.replace(/\\n/g, '\n'),
        }),
        projectId: config.projectId,
        storageBucket: `${config.projectId}.appspot.com`,
      });
    } else {
      adminApp = getApps()[0];
    }

    // Initialize Firebase Admin services
    adminAuth = getAuth(adminApp);
    adminFirestore = getFirestore(adminApp);
    adminStorage = getStorage(adminApp);

    // Configure Firestore settings
    adminFirestore.settings({
      ignoreUndefinedProperties: true,
    });

    return { 
      app: adminApp, 
      auth: adminAuth, 
      firestore: adminFirestore, 
      storage: adminStorage 
    };
  } catch (error) {
    console.error('Failed to initialize Firebase Admin:', error);
    throw error;
  }
};

// Export initialized services
export const getFirebaseAdminServices = () => {
  if (!adminApp || !adminAuth || !adminFirestore || !adminStorage) {
    return initializeFirebaseAdmin();
  }
  return { 
    app: adminApp, 
    auth: adminAuth, 
    firestore: adminFirestore, 
    storage: adminStorage 
  };
};

// Export individual services for convenience
export const getFirebaseAdminAuth = () => getFirebaseAdminServices().auth;
export const getFirebaseAdminFirestore = () => getFirebaseAdminServices().firestore;
export const getFirebaseAdminStorage = () => getFirebaseAdminServices().storage;
export const getFirebaseAdminApp = () => getFirebaseAdminServices().app;

// Utility function to verify Firebase ID token
export const verifyIdToken = async (idToken: string) => {
  try {
    const auth = getFirebaseAdminAuth();
    const decodedToken = await auth.verifyIdToken(idToken);
    return decodedToken;
  } catch (error) {
    console.error('Error verifying ID token:', error);
    throw new Error('Invalid or expired token');
  }
};

// Utility function to set custom user claims (for roles)
export const setUserRole = async (uid: string, role: string) => {
  try {
    const auth = getFirebaseAdminAuth();
    await auth.setCustomUserClaims(uid, { role });
    return true;
  } catch (error) {
    console.error('Error setting user role:', error);
    throw new Error('Failed to set user role');
  }
};
