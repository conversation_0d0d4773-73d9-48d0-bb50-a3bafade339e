import React from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { loginSchema, LoginFormData } from '../schemas/auth-schemas';
import { InputField } from './input-field';
import { FormButton } from './form-button';

export interface LoginFormProps {
  onSubmit: (data: LoginFormData) => Promise<void>;
  onSignInWithGoogle?: () => Promise<void>;
  onForgotPassword?: () => void;
  onSignUp?: () => void;
  isLoading?: boolean;
  error?: string | null;
  variant?: 'default' | 'mobile';
}

/**
 * Login form component with validation using React Hook Form and Yup
 */
export const LoginForm: React.FC<LoginFormProps> = ({
  onSubmit,
  onSignInWithGoogle,
  onForgotPassword,
  onSignUp,
  isLoading = false,
  error = null,
  variant = 'default',
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<LoginFormData>({
    resolver: yupResolver(loginSchema),
    mode: 'onBlur',
  });

  const submitHandler: SubmitHandler<LoginFormData> = async (data) => {
    try {
      await onSubmit(data);
    } catch (err) {
      // Error handling is managed by parent component
      console.error('Login error:', err);
    }
  };

  const containerClasses = variant === 'mobile' 
    ? 'w-full max-w-md mx-auto px-6'
    : 'w-full max-w-md mx-auto';

  const formClasses = variant === 'mobile'
    ? 'space-y-6'
    : 'space-y-4';

  const titleClasses = variant === 'mobile'
    ? 'text-3xl font-bold text-center text-gray-900 mb-8'
    : 'text-2xl font-bold text-center text-gray-900 mb-6';

  return (
    <div className={containerClasses}>
      <div className="bg-white shadow-lg rounded-lg px-8 py-8">
        <h2 className={titleClasses}>Sign In</h2>
        
        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded" role="alert">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit(submitHandler)} className={formClasses}>
          <InputField
            id="email"
            label="Email Address"
            type="email"
            variant={variant}
            required
            autoComplete="email"
            error={errors.email?.message}
            {...register('email')}
          />

          <InputField
            id="password"
            label="Password"
            type="password"
            variant={variant}
            required
            autoComplete="current-password"
            error={errors.password?.message}
            {...register('password')}
          />

          <div className="flex items-center justify-between">
            {onForgotPassword && (
              <button
                type="button"
                onClick={onForgotPassword}
                className="text-sm text-blue-600 hover:text-blue-500 focus:outline-none focus:underline"
              >
                Forgot your password?
              </button>
            )}
          </div>

          <FormButton
            type="submit"
            isLoading={isLoading || isSubmitting}
            fullWidth
            size={variant === 'mobile' ? 'lg' : 'md'}
          >
            Sign In
          </FormButton>

          {onSignInWithGoogle && (
            <>
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300" />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-white text-gray-500">Or continue with</span>
                </div>
              </div>

              <FormButton
                type="button"
                variant="ghost"
                fullWidth
                size={variant === 'mobile' ? 'lg' : 'md'}
                onClick={onSignInWithGoogle}
                disabled={isLoading}
                className="border border-gray-300"
              >
                <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                  <path
                    fill="currentColor"
                    d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                  />
                  <path
                    fill="currentColor"
                    d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                  />
                  <path
                    fill="currentColor"
                    d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                  />
                  <path
                    fill="currentColor"
                    d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                  />
                </svg>
                Sign in with Google
              </FormButton>
            </>
          )}

          {onSignUp && (
            <div className="text-center">
              <span className="text-sm text-gray-600">
                Don't have an account?{' '}
                <button
                  type="button"
                  onClick={onSignUp}
                  className="text-blue-600 hover:text-blue-500 focus:outline-none focus:underline font-medium"
                >
                  Sign up
                </button>
              </span>
            </div>
          )}
        </form>
      </div>
    </div>
  );
};
