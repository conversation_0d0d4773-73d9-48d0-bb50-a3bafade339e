import { UserRole } from '@nasaga-monorepo/models';
import { SvgIconComponent } from '@mui/icons-material';

export interface NavigationItem {
  id: string;
  title: string;
  path: string;
  icon: SvgIconComponent;
  roles: UserRole[];
  children?: NavigationItem[];
  badge?: string | number;
  external?: boolean;
}

export interface NavigationSection {
  title: string;
  items: NavigationItem[];
}

export interface NavigationConfig {
  sections: NavigationSection[];
}

export interface SideNavProps {
  /**
   * The current user's role to filter navigation items
   */
  userRole: UserRole;
  /**
   * Current active path to highlight the current page
   */
  currentPath: string;
  /**
   * Navigation configuration
   */
  navigation: NavigationConfig;
  /**
   * Whether the sidebar is collapsed
   */
  collapsed?: boolean;
  /**
   * Callback when navigation item is clicked
   */
  onNavigate?: (path: string) => void;
  /**
   * Callback when collapse state changes
   */
  onCollapseChange?: (collapsed: boolean) => void;
  /**
   * Company/brand logo URL
   */
  logoUrl?: string;
  /**
   * Company/brand name
   */
  brandName?: string;
}
