import React, { useState } from 'react';
import {
  Box,
  <PERSON>ppB<PERSON>,
  <PERSON><PERSON><PERSON>,
  Typography,
  IconButton,
  Avatar,
  Menu,
  MenuItem,
  Divider,
  ListItemIcon,
  ListItemText,
  useTheme,
  styled,
} from '@mui/material';
import {
  AccountCircle as AccountCircleIcon,
  Settings as SettingsIcon,
  Logout as LogoutIcon,
  Notifications as NotificationsIcon,
  Badge,
} from '@mui/icons-material';
import { UserRole } from '@nasaga-monorepo/models';
import SideNav from '../navigation/SideNav';
import { NavigationConfig, getNavigationByRole } from '../navigation/configs';

const Main = styled('main', { shouldForwardProp: (prop) => prop !== 'open' })<{
  open?: boolean;
}>(({ theme, open }) => ({
  flexGrow: 1,
  padding: theme.spacing(3),
  transition: theme.transitions.create('margin', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  marginLeft: open ? 240 : theme.spacing(7),
}));

export interface DashboardLayoutProps {
  /**
   * The main content to display
   */
  children: React.ReactNode;
  /**
   * Current user information
   */
  user: {
    displayName: string;
    email: string;
    role: UserRole;
    profilePictureUrl?: string;
  };
  /**
   * Current page path for navigation highlighting
   */
  currentPath: string;
  /**
   * Custom navigation configuration (optional)
   */
  navigationConfig?: NavigationConfig;
  /**
   * Brand/company name
   */
  brandName?: string;
  /**
   * Logo URL
   */
  logoUrl?: string;
  /**
   * Page title
   */
  pageTitle?: string;
  /**
   * Notification count for badge
   */
  notificationCount?: number;
  /**
   * Callbacks
   */
  onNavigate?: (path: string) => void;
  onProfileClick?: () => void;
  onSettingsClick?: () => void;
  onLogout?: () => void;
  onNotificationsClick?: () => void;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  children,
  user,
  currentPath,
  navigationConfig,
  brandName = 'Nasaga',
  logoUrl,
  pageTitle,
  notificationCount = 0,
  onNavigate,
  onProfileClick,
  onSettingsClick,
  onLogout,
  onNotificationsClick,
}) => {
  const theme = useTheme();
  const [sideNavCollapsed, setSideNavCollapsed] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const navigation = navigationConfig || getNavigationByRole(user.role);

  const handleProfileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleProfileMenuClose = () => {
    setAnchorEl(null);
  };

  const handleProfileAction = (action: () => void) => {
    handleProfileMenuClose();
    action();
  };

  return (
    <Box sx={{ display: 'flex' }}>
      {/* App Bar */}
      <AppBar
        position="fixed"
        sx={{
          zIndex: theme.zIndex.drawer + 1,
          bgcolor: 'background.paper',
          color: 'text.primary',
        }}
        elevation={1}
      >
        <Toolbar>
          {/* Page Title */}
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            {pageTitle || 'Dashboard'}
          </Typography>

          {/* Notifications */}
          <IconButton
            size="large"
            color="inherit"
            onClick={onNotificationsClick}
            sx={{ mr: 1 }}
          >
            <Badge badgeContent={notificationCount} color="error">
              <NotificationsIcon />
            </Badge>
          </IconButton>

          {/* User Profile */}
          <IconButton
            size="large"
            edge="end"
            onClick={handleProfileMenuOpen}
            color="inherit"
          >
            {user.profilePictureUrl ? (
              <Avatar src={user.profilePictureUrl} alt={user.displayName} />
            ) : (
              <Avatar sx={{ bgcolor: theme.palette.primary.main }}>
                {user.displayName.charAt(0).toUpperCase()}
              </Avatar>
            )}
          </IconButton>

          {/* Profile Menu */}
          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleProfileMenuClose}
            onClick={handleProfileMenuClose}
            transformOrigin={{ horizontal: 'right', vertical: 'top' }}
            anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
          >
            <Box sx={{ px: 2, py: 1 }}>
              <Typography variant="subtitle1" fontWeight="bold">
                {user.displayName}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {user.email}
              </Typography>
              <Typography
                variant="caption"
                color="primary"
                sx={{ textTransform: 'capitalize' }}
              >
                {user.role}
              </Typography>
            </Box>
            <Divider />
            <MenuItem onClick={() => handleProfileAction(onProfileClick || (() => {}))}>
              <ListItemIcon>
                <AccountCircleIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>My Profile</ListItemText>
            </MenuItem>
            <MenuItem onClick={() => handleProfileAction(onSettingsClick || (() => {}))}>
              <ListItemIcon>
                <SettingsIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Settings</ListItemText>
            </MenuItem>
            <Divider />
            <MenuItem onClick={() => handleProfileAction(onLogout || (() => {}))}>
              <ListItemIcon>
                <LogoutIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Logout</ListItemText>
            </MenuItem>
          </Menu>
        </Toolbar>
      </AppBar>

      {/* Side Navigation */}
      <SideNav
        userRole={user.role}
        currentPath={currentPath}
        navigation={navigation}
        collapsed={sideNavCollapsed}
        onCollapseChange={setSideNavCollapsed}
        onNavigate={onNavigate}
        brandName={brandName}
        logoUrl={logoUrl}
      />

      {/* Main Content */}
      <Main open={!sideNavCollapsed}>
        <Toolbar />
        {children}
      </Main>
    </Box>
  );
};

export default DashboardLayout;
