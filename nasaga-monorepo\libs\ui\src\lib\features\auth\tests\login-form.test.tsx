import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { LoginForm, LoginFormProps } from '../components/login-form';
import { LoginFormData } from '../schemas/auth-schemas';

// Mock the auth hook
const mockOnSubmit = jest.fn();
const mockOnSignInWithGoogle = jest.fn();
const mockOnForgotPassword = jest.fn();
const mockOnSignUp = jest.fn();

const defaultProps: LoginFormProps = {
  onSubmit: mockOnSubmit,
  onSignInWithGoogle: mockOnSignInWithGoogle,
  onForgotPassword: mockOnForgotPassword,
  onSignUp: mockOnSignUp,
  isLoading: false,
  error: null,
  variant: 'default',
};

describe('LoginForm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders the login form correctly', () => {
    render(<LoginForm {...defaultProps} />);
    
    expect(screen.getByText('Sign In')).toBeInTheDocument();
    expect(screen.getByLabelText(/Email Address/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Password/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Sign In/i })).toBeInTheDocument();
  });

  it('renders all optional buttons when handlers are provided', () => {
    render(<LoginForm {...defaultProps} />);
    
    expect(screen.getByText('Forgot your password?')).toBeInTheDocument();
    expect(screen.getByText('Sign in with Google')).toBeInTheDocument();
    expect(screen.getByText('Sign up')).toBeInTheDocument();
  });

  it('hides optional buttons when handlers are not provided', () => {
    const props = {
      ...defaultProps,
      onSignInWithGoogle: undefined,
      onForgotPassword: undefined,
      onSignUp: undefined,
    };
    render(<LoginForm {...props} />);
    
    expect(screen.queryByText('Forgot your password?')).not.toBeInTheDocument();
    expect(screen.queryByText('Sign in with Google')).not.toBeInTheDocument();
    expect(screen.queryByText('Sign up')).not.toBeInTheDocument();
  });

  it('displays error message when error prop is provided', () => {
    const errorMessage = 'Invalid credentials';
    render(<LoginForm {...defaultProps} error={errorMessage} />);
    
    expect(screen.getByText(errorMessage)).toBeInTheDocument();
    expect(screen.getByRole('alert')).toBeInTheDocument();
  });

  it('shows loading state when isLoading is true', () => {
    render(<LoginForm {...defaultProps} isLoading={true} />);
    
    const submitButton = screen.getByRole('button', { name: /Processing.../i });
    expect(submitButton).toBeDisabled();
  });

  it('validates required fields and shows error messages', async () => {
    const user = userEvent.setup();
    render(<LoginForm {...defaultProps} />);
    
    const submitButton = screen.getByRole('button', { name: /Sign In/i });
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText('Email is required')).toBeInTheDocument();
      expect(screen.getByText('Password is required')).toBeInTheDocument();
    });
  });

  it('validates email format', async () => {
    const user = userEvent.setup();
    render(<LoginForm {...defaultProps} />);
    
    const emailInput = screen.getByLabelText(/Email Address/i);
    await user.type(emailInput, 'invalid-email');
    await user.tab(); // Trigger onBlur
    
    await waitFor(() => {
      expect(screen.getByText('Please enter a valid email address')).toBeInTheDocument();
    });
  });

  it('validates password minimum length', async () => {
    const user = userEvent.setup();
    render(<LoginForm {...defaultProps} />);
    
    const passwordInput = screen.getByLabelText(/Password/i);
    await user.type(passwordInput, '123');
    await user.tab(); // Trigger onBlur
    
    await waitFor(() => {
      expect(screen.getByText('Password must be at least 6 characters long')).toBeInTheDocument();
    });
  });

  it('calls onSubmit with form data when form is valid', async () => {
    const user = userEvent.setup();
    const mockSubmit = jest.fn().mockResolvedValue(undefined);
    render(<LoginForm {...defaultProps} onSubmit={mockSubmit} />);
    
    const emailInput = screen.getByLabelText(/Email Address/i);
    const passwordInput = screen.getByLabelText(/Password/i);
    const submitButton = screen.getByRole('button', { name: /Sign In/i });
    
    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'password123');
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(mockSubmit).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
      });
    });
  });

  it('calls onSignInWithGoogle when Google sign-in button is clicked', async () => {
    const user = userEvent.setup();
    render(<LoginForm {...defaultProps} />);
    
    const googleButton = screen.getByText('Sign in with Google');
    await user.click(googleButton);
    
    expect(mockOnSignInWithGoogle).toHaveBeenCalled();
  });

  it('calls onForgotPassword when forgot password link is clicked', async () => {
    const user = userEvent.setup();
    render(<LoginForm {...defaultProps} />);
    
    const forgotPasswordLink = screen.getByText('Forgot your password?');
    await user.click(forgotPasswordLink);
    
    expect(mockOnForgotPassword).toHaveBeenCalled();
  });

  it('calls onSignUp when sign up link is clicked', async () => {
    const user = userEvent.setup();
    render(<LoginForm {...defaultProps} />);
    
    const signUpLink = screen.getByText('Sign up');
    await user.click(signUpLink);
    
    expect(mockOnSignUp).toHaveBeenCalled();
  });

  it('applies mobile variant styles correctly', () => {
    render(<LoginForm {...defaultProps} variant="mobile" />);
    
    const container = screen.getByText('Sign In').closest('div');
    expect(container).toHaveClass('text-3xl');
  });

  it('handles form submission errors gracefully', async () => {
    const user = userEvent.setup();
    const mockSubmitWithError = jest.fn().mockRejectedValue(new Error('Network error'));
    
    // Mock console.error to avoid test output pollution
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    
    render(<LoginForm {...defaultProps} onSubmit={mockSubmitWithError} />);
    
    const emailInput = screen.getByLabelText(/Email Address/i);
    const passwordInput = screen.getByLabelText(/Password/i);
    const submitButton = screen.getByRole('button', { name: /Sign In/i });
    
    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'password123');
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(mockSubmitWithError).toHaveBeenCalled();
      expect(consoleSpy).toHaveBeenCalledWith('Login error:', expect.any(Error));
    });
    
    consoleSpy.mockRestore();
  });

  it('disables Google sign-in button when form is loading', () => {
    render(<LoginForm {...defaultProps} isLoading={true} />);
    
    const googleButton = screen.getByText('Sign in with Google');
    expect(googleButton).toBeDisabled();
  });

  it('has proper accessibility attributes', () => {
    render(<LoginForm {...defaultProps} error="Test error" />);
    
    const emailInput = screen.getByLabelText(/Email Address/i);
    const passwordInput = screen.getByLabelText(/Password/i);
    const errorDiv = screen.getByRole('alert');
    
    expect(emailInput).toHaveAttribute('type', 'email');
    expect(emailInput).toHaveAttribute('autoComplete', 'email');
    expect(passwordInput).toHaveAttribute('type', 'password');
    expect(passwordInput).toHaveAttribute('autoComplete', 'current-password');
    expect(errorDiv).toBeInTheDocument();
  });
});
