
import * as express from 'express';
import * as cors from 'cors';
import { requestLogger } from './middleware/request-logger';
import { authRouter } from './routes/auth';
import { adminRouter } from './routes/admin';
import { usersRouter } from './routes/users';
import { propertiesRouter } from './routes/properties';
import { errorHandler } from './middleware/error-handler';

const app = express();

app.use(cors({ origin: true }));
app.use(express.json());
app.use(requestLogger);

// Routes
app.use('/auth', authRouter);
app.use('/admin', adminRouter);
app.use('/users', usersRouter);
app.use('/properties', propertiesRouter);

app.use(errorHandler);

export default app;

