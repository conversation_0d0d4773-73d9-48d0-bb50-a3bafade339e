import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { RegistrationForm, RegistrationFormProps } from '../components/registration-form';

// Mock the auth hook
const mockOnSubmit = jest.fn();
const mockOnSignIn = jest.fn();

const defaultProps: RegistrationFormProps = {
  onSubmit: mockOnSubmit,
  onSignIn: mockOnSignIn,
  isLoading: false,
  error: null,
  variant: 'default',
};

describe('RegistrationForm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the registration form correctly', () => {
    render(<RegistrationForm {...defaultProps} />);
    
    expect(screen.getByText('Create an Account')).toBeInTheDocument();
    expect(screen.getByLabelText(/Full Name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Email Address/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Password/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Confirm Password/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/I am a.../i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Create Account/i })).toBeInTheDocument();
  });

  it('hides sign-in link when handler is not provided', () => {
    render(<RegistrationForm {...defaultProps} onSignIn={undefined} />);
    
    expect(screen.queryByText('Sign in')).not.toBeInTheDocument();
  });

  it('displays error message when error prop is provided', () => {
    const errorMessage = 'An account with this email already exists';
    render(<RegistrationForm {...defaultProps} error={errorMessage} />);
    
    expect(screen.getByText(errorMessage)).toBeInTheDocument();
    expect(screen.getByRole('alert')).toBeInTheDocument();
  });

  it('shows loading state when isLoading is true', () => {
    render(<RegistrationForm {...defaultProps} isLoading={true} />);
    
    const submitButton = screen.getByRole('button', { name: /Processing.../i });
    expect(submitButton).toBeDisabled();
  });

  it('validates required fields and shows error messages', async () => {
    const user = userEvent.setup();
    render(<RegistrationForm {...defaultProps} />);
    
    const submitButton = screen.getByRole('button', { name: /Create Account/i });
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText('Display name is required')).toBeInTheDocument();
      expect(screen.getByText('Email is required')).toBeInTheDocument();
      expect(screen.getByText('Password is required')).toBeInTheDocument();
      expect(screen.getByText('Please confirm your password')).toBeInTheDocument();
      expect(screen.getByText('Role is required')).toBeInTheDocument();
    });
  });

  it('validates password confirmation', async () => {
    const user = userEvent.setup();
    render(<RegistrationForm {...defaultProps} />);
    
    const passwordInput = screen.getByLabelText(/Password/i);
    const confirmPasswordInput = screen.getByLabelText(/Confirm Password/i);
    
    await user.type(passwordInput, 'Password123');
    await user.type(confirmPasswordInput, 'Password456');
    await user.tab();
    
    await waitFor(() => {
      expect(screen.getByText('Passwords must match')).toBeInTheDocument();
    });
  });

  it('calls onSubmit with form data when form is valid', async () => {
    const user = userEvent.setup();
    const mockSubmit = jest.fn().mockResolvedValue(undefined);
    render(<RegistrationForm {...defaultProps} onSubmit={mockSubmit} />);
    
    const nameInput = screen.getByLabelText(/Full Name/i);
    const emailInput = screen.getByLabelText(/Email Address/i);
    const passwordInput = screen.getByLabelText(/Password/i);
    const confirmPasswordInput = screen.getByLabelText(/Confirm Password/i);
    const roleSelect = screen.getByLabelText(/I am a.../i);
    const submitButton = screen.getByRole('button', { name: /Create Account/i });

    await user.type(nameInput, 'Test User');
    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'Password123');
    await user.type(confirmPasswordInput, 'Password123');
    await user.selectOptions(roleSelect, 'agent');
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(mockSubmit).toHaveBeenCalledWith({
        displayName: 'Test User',
        email: '<EMAIL>',
        password: 'Password123',
        confirmPassword: 'Password123',
        role: 'agent',
        phoneNumber: '',
      });
    });
  });

  it('calls onSignIn when sign in link is clicked', async () => {
    const user = userEvent.setup();
    render(<RegistrationForm {...defaultProps} />);
    
    const signInLink = screen.getByText('Sign in');
    await user.click(signInLink);
    
    expect(mockOnSignIn).toHaveBeenCalled();
  });
});
