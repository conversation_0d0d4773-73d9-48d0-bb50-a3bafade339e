{"15121233605351603716": {"apps/backend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/backend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/backend/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "11772722999478199989": {"apps/backend-e2e": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/backend-e2e"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/backend-e2e/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "2450041136631408503": {"apps/mobile-agent": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/mobile-agent"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/mobile-agent/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "5156437362467865142": {"apps/mobile-buyer": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/mobile-buyer"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/mobile-buyer/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "11102541813746780256": {"apps/web-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/web-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/web-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "9189198249358481330": {"apps/web-admin-e2e": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/web-admin-e2e"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/web-admin-e2e/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "6471253367754970209": {"apps/web-superadmin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/web-superadmin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/web-superadmin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "12095103065474185146": {"libs/auth": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "libs/auth"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/libs/auth/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "17671784849583154361": {"libs/models": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "libs/models"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/libs/models/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "6823749298103551206": {"libs/ui": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "libs/ui"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/libs/ui/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "7904736949839701122": {"libs/utils": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "libs/utils"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/libs/utils/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "17899166793407378526": {}, "13111802241687980416": {"apps/web-superadmin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/web-superadmin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/web-superadmin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "912639595459021144": {}, "6104197239905593612": {"apps/web-superadmin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/web-superadmin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/web-superadmin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "6895449298788548574": {}, "5522817620473968737": {"apps/web-superadmin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/web-superadmin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/web-superadmin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "32306260190801124": {}, "2233675520757042987": {"apps/web-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/web-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/web-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "8396597634661730238": {}, "9315015091829273927": {"apps/web-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/web-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/web-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "6273617837092939060": {}, "11149276580408202667": {"apps/web-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/web-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/web-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "8595675528022341209": {}, "6122527302806500101": {"apps/web-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/web-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/web-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "3584034378853556914": {}, "14152273056044847112": {"apps/web-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/web-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/web-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "9447552701390849969": {}}