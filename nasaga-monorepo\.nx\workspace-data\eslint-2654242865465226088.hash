{"24847561001087": {"apps/web-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/web-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/web-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "9189198249358481330": {"apps/web-admin-e2e": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/web-admin-e2e"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/web-admin-e2e/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "16249018127657740346": {}, "11232270722080170498": {"apps/web-superadmin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/web-superadmin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/web-superadmin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "15648620241007998843": {}, "12633216895652564919": {}, "4302477851295575531": {}, "10067534865740174105": {"apps/mobile-agent": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/mobile-agent"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/mobile-agent/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "13386776660782242533": {}, "14086086814409751226": {"apps/mobile-buyer": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/mobile-buyer"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/mobile-buyer/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "16670769064495628440": {}, "17195238205163953280": {"apps/backend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/backend"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/backend/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "11772722999478199989": {"apps/backend-e2e": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/backend-e2e"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/backend-e2e/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "10611181201080160958": {}, "12287225908448764746": {}, "16873806976726746139": {"libs/ui": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "libs/ui"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/libs/ui/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "15192957199370190804": {}, "14186728887436497046": {}, "16260403329722137461": {"libs/auth": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "libs/auth"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/libs/auth/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "779570941504109009": {}, "4263356027178148258": {}, "12870064435187200931": {"libs/models": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "libs/models"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/libs/models/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "12138349925796133770": {}, "5467372007880393509": {}, "12337877495388159875": {"libs/utils": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "libs/utils"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/libs/utils/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "12132877186601049920": {}, "4736895175685598587": {}, "12757716962621284269": {}, "11192346998311058837": {}, "9660021205912053656": {}, "17725718695839530771": {}, "7686769778918356705": {}, "7496384884354690842": {}, "5452158432491285798": {}, "16151244404978036210": {}, "798138859549386490": {}, "14978783466036703147": {}, "7884317500317930959": {}, "18055853222918385509": {}, "16031718162144053633": {}, "158683550811990515": {}}