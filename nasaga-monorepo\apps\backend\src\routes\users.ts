import { Router } from 'express';
import { authenticateToken, requireAdmin } from '../middleware/auth-middleware';
import { asyncHandler } from '../middleware/error-handler';
import { ApiSuccess } from '@nasaga-monorepo/models';

const router = Router();

/**
 * GET /api/users
 * Get all users (admin only)
 */
router.get('/', authenticateToken, requireAdmin, asyncHandler(async (req, res) => {
  // TODO: Implement user listing with pagination and filtering
  const response: ApiSuccess = {
    success: true,
    data: [],
    message: 'Users endpoint - coming soon',
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

/**
 * GET /api/users/:uid
 * Get user by UID (admin only or own profile)
 */
router.get('/:uid', authenticateToken, asyncHandler(async (req, res) => {
  // TODO: Implement user profile retrieval
  const response: ApiSuccess = {
    success: true,
    data: { uid: req.params.uid },
    message: 'User profile endpoint - coming soon',
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

export default router;
