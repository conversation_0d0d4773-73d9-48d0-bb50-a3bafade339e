# **Components**

## **Auth Service (Backend)**

* **Responsibility:** Handles all server-side authentication logic, including user registration, login (if not fully client-side via Firebase SDK), and token verification. It will interact with Firebase Authentication Admin SDK for privileged user management operations (e.g., setting custom claims for roles).  
* **Key Interfaces:**  
  * POST /auth/register: (as defined in API Spec) for new user registration.  
  * Middleware: For verifying Firebase ID tokens on protected routes.  
* **Dependencies:** Firebase Authentication Admin SDK, Firebase Firestore (for storing user roles).  
* **Technology Stack:** Node.js, Express, Firebase Admin SDK.

## **Property Management Service (Backend)**

* **Responsibility:** Manages all server-side operations related to property listings, including creation, retrieval (for filtered/searched lists), updates, and status changes. It will perform validation, interact with Firestore for data persistence, and integrate with Firebase Storage for media management. It will also be responsible for triggering notifications via FCM for status changes.  
* **Key Interfaces:**  
  * POST /properties: (as defined in API Spec) for agents to create new listings.  
  * PATCH /properties/{propertyId}/status: (as defined in API Spec) for Admins to approve/reject property status.  
  * Internal methods for retrieving filtered/paginated property lists.  
* **Dependencies:** Firebase Firestore, Firebase Storage Admin SDK, Firebase Cloud Messaging Admin SDK.  
* **Technology Stack:** Node.js, Express, Firebase Admin SDK.

## **Admin & Super Admin Web Dashboards (Frontend)**

* **Responsibility:** Provides the secure web-based user interface for Admin and Super Admin roles. This includes user account management, property listing approval/rejection workflows, and platform analytics display. It will interact with the Backend API for privileged operations and directly with Firebase SDKs for authentication and real-time data display.  
* **Key Interfaces:**  
  * Login pages for role-based access.  
  * User management interfaces.  
  * Property listing review and action interfaces.  
  * Integration with Backend API endpoints (e.g., /admin/users/{uid}/role, /properties/{propertyId}/status).  
  * Direct consumption of Firebase Firestore data for real-time listing views.  
* **Dependencies:** React, TypeScript, React Router, UI Component Library (to be decided, e.g., Material-UI or Ant Design), Firebase SDKs (Auth, Firestore), Backend API client (generated from OpenAPI spec or custom).  
* **Technology Stack:** React, TypeScript, Nx.

## **Agent Mobile App (Frontend)**

* **Responsibility:** Provides the secure mobile application interface for real estate agents. This includes property listing creation, editing, viewing, delisting, media uploads, and basic profile management. It will primarily interact with Firebase SDKs for authentication, Firestore data synchronization, and Storage for media. It will also receive push notifications via FCM for buyer inquiries.  
* **Key Interfaces:**  
  * Secure login and registration flow.  
  * Property listing forms (create/edit).  
  * Media upload interfaces.  
  * Dashboard/list view of agent's properties.  
  * Profile management screens.  
  * Receiving and displaying push notifications.  
* **Dependencies:** React Native, Expo, TypeScript, React Navigation, UI Component Library (to be decided, e.g., NativeBase, UI Kitten), Firebase SDKs (Auth, Firestore, Storage, FCM).  
* **Technology Stack:** React Native, Expo, TypeScript, Nx.

## **Buyer Mobile App (Frontend)**

* **Responsibility:** Provides the secure mobile application interface for prospective buyers. This includes browsing, searching, filtering, viewing detailed property information, favoriting properties, and receiving push notifications for updates on favorited listings. It will primarily interact with Firebase SDKs for authentication and Firestore data synchronization.  
* **Key Interfaces:**  
  * Secure login and registration flow.  
  * Property browsing and search interface.  
  * Filter options for property listings.  
  * Property detail pages.  
  * Favorited properties list.  
  * Receiving and displaying push notifications.  
* **Dependencies:** React Native, Expo, TypeScript, React Navigation, UI Component Library (to be decided, e.g., NativeBase, UI Kitten), Firebase SDKs (Auth, Firestore, FCM).  
* **Technology Stack:** React Native, Expo, TypeScript, Nx.

## **Component Diagrams**

`graph TD`  
    `subgraph Client Applications`  
        `AdminWeb[Admin Web (React)]`  
        `SuperAdminWeb[Super Admin Web (React)]`  
        `AgentMobile[Agent App (RN/Expo)]`  
        `BuyerMobile[Buyer App (RN/Expo)]`  
    `end`

    `subgraph Backend Services`  
        `BackendAPI[Node.js/Express API]`  
        `FirebaseAuth(Firebase Auth)`  
        `Firestore(Firebase Firestore)`  
        `FirebaseStorage(Firebase Storage)`  
        `FirebaseCM(Firebase Cloud Messaging)`  
    `end`

    `AdminWeb -- REST API --> BackendAPI`  
    `SuperAdminWeb -- REST API --> BackendAPI`  
    `AgentMobile -- REST API / Direct SDK --> BackendAPI`  
    `BuyerMobile -- REST API / Direct SDK --> BackendAPI`

    `AgentMobile -- Firebase SDKs --> FirebaseAuth`  
    `AgentMobile -- Firebase SDKs --> Firestore`  
    `AgentMobile -- Firebase SDKs --> FirebaseStorage`  
    `AgentMobile -- Firebase SDKs --> FirebaseCM`

    `BuyerMobile -- Firebase SDKs --> FirebaseAuth`  
    `BuyerMobile -- Firebase SDKs --> Firestore`  
    `BuyerMobile -- Firebase SDKs --> FirebaseCM`

    `BackendAPI -- Firebase Admin SDK --> FirebaseAuth`  
    `BackendAPI -- Firebase Admin SDK --> Firestore`  
    `BackendAPI -- Firebase Admin SDK --> FirebaseStorage`  
    `BackendAPI -- Firebase Admin SDK --> FirebaseCM`

    `style AdminWeb fill:#F0F8FF,stroke:#333,stroke-width:2px`  
    `style SuperAdminWeb fill:#F0F8FF,stroke:#333,stroke-width:2px`  
    `style AgentMobile fill:#E0FFFF,stroke:#333,stroke-width:2px`  
    `style BuyerMobile fill:#E0FFFF,stroke:#333,stroke-width:2px`  
    `style BackendAPI fill:#FFF0F5,stroke:#333,stroke-width:2px`  
    `style FirebaseAuth fill:#FFD700,stroke:#333,stroke-width:2px`  
    `style Firestore fill:#ADD8E6,stroke:#333,stroke-width:2px`  
    `style FirebaseStorage fill:#DAA520,stroke:#333,stroke-width:2px`  
    `style FirebaseCM fill:#FFB6C1,stroke:#333,stroke-width:2px`
