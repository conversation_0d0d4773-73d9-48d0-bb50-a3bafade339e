import { Timestamp } from 'firebase/firestore';

/**
 * Property status in the Nasaga Real Estate Management Platform
 */
export type PropertyStatus = 
  | 'pending'      // Newly created, awaiting admin approval
  | 'approved'     // Approved by admin, ready to be active
  | 'active'       // Live and visible to buyers
  | 'under offer'  // Under negotiation with a buyer
  | 'sold'         // Successfully sold
  | 'delisted';    // Removed from active listings

/**
 * Property interface representing a single property listing
 * This model stores all relevant details about a property, its current status,
 * and a reference to the agent who listed it.
 */
export interface Property {
  /** Unique identifier for the property */
  propertyId: string;
  
  /** Short, descriptive title of the property */
  title: string;
  
  /** Detailed description of the property */
  description: string;
  
  /** Full street address of the property */
  address: string;
  
  /** City where the property is located */
  city: string;
  
  /** State where the property is located */
  state: string;
  
  /** Postal code of the property */
  zipCode: string;
  
  /** Asking price of the property */
  price: number;
  
  /** Type of property (e.g., "House", "Apartment", "Land", "Commercial") */
  propertyType: string;
  
  /** Number of bedrooms */
  bedrooms: number;
  
  /** Number of bathrooms */
  bathrooms: number;
  
  /** Total area in square feet/meters (optional) */
  squareFootage?: number;
  
  /** Firebase UID of the agent who listed the property */
  agentUid: string;
  
  /** Current status of the property */
  status: PropertyStatus;
  
  /** Array of URLs to property images stored in Firebase Storage */
  imageUrls: string[];
  
  /** Array of URLs to associated documents (optional) */
  documentUrls?: string[];
  
  /** Timestamp of property listing creation */
  createdAt: Timestamp;
  
  /** Timestamp of last update */
  updatedAt: Timestamp;
  
  /** Firebase UID of the Admin who approved the listing (optional) */
  approvedByAdminUid?: string;
  
  /** Timestamp of approval (optional) */
  approvedAt?: Timestamp;
}

/**
 * Property creation data interface for new property listings
 */
export interface CreatePropertyData {
  title: string;
  description: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  price: number;
  propertyType: string;
  bedrooms: number;
  bathrooms: number;
  squareFootage?: number;
  imageUrls: string[];
  documentUrls?: string[];
}

/**
 * Property update data interface for updating existing properties
 */
export interface UpdatePropertyData {
  title?: string;
  description?: string;
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  price?: number;
  propertyType?: string;
  bedrooms?: number;
  bathrooms?: number;
  squareFootage?: number;
  imageUrls?: string[];
  documentUrls?: string[];
  status?: PropertyStatus;
}

/**
 * Property status update interface for admin actions
 */
export interface PropertyStatusUpdate {
  status: PropertyStatus;
  approvedByAdminUid?: string;
}

/**
 * Property search filters interface
 */
export interface PropertyFilters {
  city?: string;
  state?: string;
  propertyType?: string;
  minPrice?: number;
  maxPrice?: number;
  minBedrooms?: number;
  maxBedrooms?: number;
  minBathrooms?: number;
  maxBathrooms?: number;
  status?: PropertyStatus[];
  agentUid?: string;
}

/**
 * Property response interface for API responses
 */
export interface PropertyResponse {
  propertyId: string;
  title: string;
  description: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  price: number;
  propertyType: string;
  bedrooms: number;
  bathrooms: number;
  squareFootage?: number;
  agentUid: string;
  status: PropertyStatus;
  imageUrls: string[];
  documentUrls?: string[];
  createdAt: string; // ISO string format for API responses
  updatedAt: string; // ISO string format for API responses
  approvedByAdminUid?: string;
  approvedAt?: string; // ISO string format for API responses
}

/**
 * Property list response interface for paginated results
 */
export interface PropertyListResponse {
  properties: PropertyResponse[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

/**
 * Type guard to check if a property is visible to buyers
 */
export const isPropertyVisibleToBuyers = (property: Property): boolean => {
  return property.status === 'active' || property.status === 'under offer';
};

/**
 * Type guard to check if a property needs admin approval
 */
export const needsAdminApproval = (property: Property): boolean => {
  return property.status === 'pending';
};

/**
 * Type guard to check if a property is sold or delisted
 */
export const isPropertyUnavailable = (property: Property): boolean => {
  return property.status === 'sold' || property.status === 'delisted';
};
