# **Error Handling Strategy**

## **Error Flow**

`sequenceDiagram`  
    `actor User`  
    `participant ClientApp[Client App (Web/Mobile)]`  
    `participant APIClient[Frontend API Client]`  
    `participant BackendAPI[Backend API (Node.js/Express)]`  
    `participant BackendService[Backend Service Layer]`  
    `participant BackendModel[Backend Data Model (Firestore)]`  
    `participant ErrorMiddleware[Error Handling Middleware]`  
    `participant LoggingService[Logging Service]`

    `User->>ClientApp: 1. User Action (e.g., Submit Form)`  
    `ClientApp->>APIClient: 2. Initiates API Request`  
    `APIClient->>BackendAPI: 3. Sends HTTP Request`  
    `BackendAPI->>BackendService: 4. Delegates to Service`  
    `BackendService->>BackendModel: 5. Attempts Data Operation`  
    `BackendModel--xBackendService: 6. Throws Database Error (e.g., Validation, Permissions)`  
    `BackendService--xBackendAPI: 7. Throws Business Logic/Service Error`  
    `BackendAPI--xErrorMiddleware: 8. <PERSON> catches error, passes to Error Middleware`  
    `ErrorMiddleware->>LoggingService: 9. Logs Error Details (e.g., Google Cloud Logging)`  
    `LoggingService-->>ErrorMiddleware: 10. Log Acknowledged`  
    `ErrorMiddleware-->>BackendAPI: 11. Formats Error Response (e.g., ApiError interface)`  
    `BackendAPI--xAPIClient: 12. Sends Error HTTP Response (e.g., 400, 401, 403, 500)`  
    `APIClient--xClientApp: 13. Processes Error Response (e.g., global interceptor)`  
    `ClientApp->>User: 14. Displays User-Friendly Error Message`

## **Error Response Format**

`// packages/types/src/api.d.ts (or error.d.ts)`  
`interface ApiErrorDetail {`  
  `field?: string; // Optional: specific field that caused the error (for validation errors)`  
  `message: string; // Human-readable message for this specific detail`  
  `code?: string; // Optional: specific error code (e.g., 'INVALID_EMAIL', 'PROPERTY_NOT_FOUND')`  
`}`

`export interface ApiResponseError {`  
  `error: {`  
    `code: string; // A general error code (e.g., 'VALIDATION_ERROR', 'UNAUTHORIZED', 'SERVER_ERROR')`  
    `message: string; // A general human-readable message for the error type`  
    `details?: ApiErrorDetail[]; // Optional: array of specific error details (e.g., validation errors per field)`  
    `timestamp: string; // ISO 8601 timestamp of when the error occurred`  
    `requestId?: string; // Optional: Correlation ID for tracing requests through logs`  
  `};`  
`}`

## **Frontend Error Handling**

`// packages/utils/src/apiClient.ts (Response Interceptor - previously defined, here with error formatting)`  
`import axios, { AxiosError } from 'axios';`  
`import { getAuth } from 'firebase/auth';`  
`import { ApiResponseError } from '@nasaga/types'; // Import the defined error interface`

`const apiClient = axios.create({`  
  `baseURL: '[https://api.nasaga.com/v1](https://api.nasaga.com/v1)',`  
  `headers: { 'Content-Type': 'application/json' },`  
`});`

`apiClient.interceptors.request.use(async (config) => {`  
  `const auth = getAuth();`  
  `const user = auth.currentUser;`  
  `if (user) {`  
    `const token = await user.getIdToken();`  
    ``config.headers.Authorization = `Bearer ${token}`;``  
  `}`  
  `return config;`  
`});`

`apiClient.interceptors.response.use(`  
  `(response) => response,`  
  `(error: AxiosError<ApiResponseError>) => {`  
    `let errorMessage = 'An unexpected error occurred.';`  
    `let errorCode = 'UNKNOWN_ERROR';`  
    `let errorDetails: ApiErrorDetail[] | undefined;`

    `if (error.response && error.response.data && error.response.data.error) {`  
      `const apiError = error.response.data.error;`  
      `errorCode = apiError.code || errorCode;`  
      `errorMessage = apiError.message || errorMessage;`  
      `errorDetails = apiError.details;`

      `// Log specific backend errors for debugging`  
      ``console.error(`Backend Error [${errorCode}]: ${errorMessage}`, apiError.details);``

      `// Special handling for specific error codes if needed (e.g., unauthorized)`  
      `if (errorCode === 'UNAUTHORIZED') {`  
        `// Redirect to login or trigger global logout`  
        `// Example: logoutUser();`  
      `}`  
    `} else if (error.request) {`  
      `errorMessage = 'Network error: No response from server. Please check your internet connection.';`  
      `errorCode = 'NETWORK_ERROR';`  
      `console.error('Network Error:', error.request);`  
    `} else {`  
      ``errorMessage = `Request Setup Error: ${error.message}`;``  
      `errorCode = 'CLIENT_ERROR';`  
      `console.error('Client-side error:', error.message);`  
    `}`

    `// You can re-throw a custom error or just the AxiosError for components to catch`  
    `return Promise.reject(error); // Or new CustomAppError(errorMessage, errorCode, errorDetails);`  
  `}`  
`);`

`export default apiClient;`

`// apps/admin-web/src/utils/errorHandler.ts (Helper for displaying errors in UI)`  
`import { AxiosError } from 'axios';`  
`import { ApiResponseError, ApiErrorDetail } from '@nasaga/types';`

`export const getErrorMessage = (error: unknown): string => {`  
  `if (axios.isAxiosError(error) && error.response && error.response.data) {`  
    `const apiError = error.response.data as ApiResponseError;`  
    `if (apiError.error?.details && apiError.error.details.length > 0) {`  
      `// Concatenate detailed messages for user display`  
      `return apiError.error.details.map(d => d.message).join('; ') + '.';`  
    `}`  
    `return apiError.error?.message || 'An API error occurred.';`  
  `}`  
  `if (error instanceof Error) {`  
    `return error.message;`  
  `}`  
  `return 'An unknown error occurred.';`  
`};`

`// Example usage in a React component`  
`/*`  
`import React, { useState } from 'react';`  
`import { propertyService } from '../services/propertyService';`  
`import { getErrorMessage } from '../utils/errorHandler';`

`const CreatePropertyForm = () => {`  
  `const [error, setError] = useState<string | null>(null);`

  `const handleSubmit = async (formData: any) => {`  
    `try {`  
      `setError(null);`  
      `await propertyService.createProperty(formData);`  
      `// Success handling`  
    `} catch (err) {`  
      `setError(getErrorMessage(err));`  
    `}`  
  `};`

  `return (`  
    `<div>`  
      `{error && <div className="text-red-500">{error}</div>}`  
      `<form onSubmit={handleSubmit}>`  
        `// Form fields`  
      `</form>`  
    `</div>`  
  `);`  
`};`  
`*/`

## **Backend Error Handling**

`// apps/backend/src/middleware/error.middleware.ts`  
`import { Request, Response, NextFunction } from 'express';`  
`import { ApiResponseError, ApiErrorDetail } from '@nasaga/types'; // Import shared types`  
`import { logError } from '../utils/logger'; // Assuming a simple logger utility`

`// Define a custom error class for known application errors`  
`export class AppError extends Error {`  
  `public statusCode: number;`  
  `public code: string;`  
  `public details?: ApiErrorDetail[];`

  `constructor(message: string, statusCode: number = 500, code: string = 'SERVER_ERROR', details?: ApiErrorDetail[]) {`  
    `super(message);`  
    `this.name = this.constructor.name;`  
    `this.statusCode = statusCode;`  
    `this.code = code;`  
    `this.details = details;`  
    `Object.setPrototypeOf(this, AppError.prototype); // Proper inheritance for instanceof`  
  `}`  
`}`

`// Centralized error handling middleware`  
`export const globalErrorHandler = (err: any, req: Request, res: Response, next: NextFunction) => {`  
  `let statusCode = err.statusCode || 500;`  
  `let message = err.message || 'An unexpected server error occurred.';`  
  `let code = err.code || 'SERVER_ERROR';`  
  `let details = err.details as ApiErrorDetail[] | undefined;`

  `// Handle specific known error types`  
  `if (err.name === 'ValidationError') { // Example for input validation errors (e.g., Joi/Zod)`  
    `statusCode = 400;`  
    `code = 'VALIDATION_ERROR';`  
    `message = 'Validation failed.';`  
    `details = err.details?.map((detail: any) => ({`  
      `field: detail.context?.key || detail.path?.join('.'),`  
      `message: detail.message,`  
      `code: detail.type,`  
    `})) || [{ message: err.message }];`  
  `} else if (err.name === 'FirebaseError') {`  
    `// Handle Firebase specific errors (e.g., Firestore permission denied)`  
    `statusCode = 500; // Default, might be more specific like 403 or 404`  
    ``code = `FIREBASE_${err.code?.toUpperCase() || 'ERROR'}`;``  
    ``message = `Firebase operation failed: ${err.message}`;``  
  `} else if (err.message.includes('Unauthorized')) { // Simple check for auth errors`  
    `statusCode = 401;`  
    `code = 'UNAUTHORIZED';`  
    `message = 'Authentication required.';`  
  `} else if (err.message.includes('Forbidden')) {`  
    `statusCode = 403;`  
    `code = 'FORBIDDEN';`  
    `message = 'You do not have permission to perform this action.';`  
  `} else if (err instanceof AppError) {`  
    `// Custom AppError instances`  
    `statusCode = err.statusCode;`  
    `code = err.code;`  
    `message = err.message;`  
    `details = err.details;`  
  `}`

  `// Log the error (excluding sensitive details in production logs if not needed)`  
  ``logError(`[${req.method}] ${req.originalUrl} - Status: ${statusCode} - Error: ${message}`, err.stack, req.body);``

  `const errorResponse: ApiResponseError = {`  
    `error: {`  
      `code,`  
      `message,`  
      `details,`  
      `timestamp: new Date().toISOString(),`  
      `requestId: (req as any).id, // Assuming a request ID middleware is used`  
    `},`  
  `};`

  `res.status(statusCode).json(errorResponse);`  
`};`

`/*`  
`// apps/backend/src/app.ts (Integration into Express app)`  
`import express from 'express';`  
`import { globalErrorHandler } from './middleware/error.middleware';`  
`import authRoutes from './routes/auth.routes';`  
`import propertyRoutes from './routes/properties.routes';`

`const app = express();`  
`app.use(express.json());`

`// Routes`  
`app.use('/auth', authRoutes);`  
`app.use('/properties', propertyRoutes);`

`// Catch-all for unhandled routes`  
`app.use((req, res, next) => {`  
  `const error = new AppError('Not Found', 404, 'NOT_FOUND');`  
  `next(error);`  
`});`

`// Global error handler MUST be the last middleware`  
`app.use(globalErrorHandler);`

`export default app;`  
`*/`
