# **Database Schema**

This section translates our conceptual data models into concrete database schemas, specifically for Firebase Firestore. It outlines the collection structures, document examples, and how relationships are managed in a NoSQL environment.

## **Firestore Collections Overview**

Our primary data will be organized into the following top-level Firestore collections:

* **users**: Stores user profiles and role information.  
* **properties**: Stores all property listings.

## **Document Structures**

### **users Collection**

* **Collection Path**: users/{uid}  
* **Purpose**: Each document in this collection represents a single user, indexed by their Firebase Authentication UID. It stores their core profile information and, critically, their assigned role.  
* **Example Document (users/someFirebaseUid123)**:  
  `{`  
    `"uid": "someFirebaseUid123",`  
    `"email": "<EMAIL>",`  
    `"displayName": "<PERSON>",`  
    `"role": "agent",`  
    `"phoneNumber": "+2348012345678",`  
    `"profilePictureUrl": "https://firebasestorage.googleapis.com/v0/b/nasaga.appspot.com/o/profile_pictures%2Fjohn_doe.jpg",`  
    `"createdAt": "2025-07-29T10:00:00Z",`  
    `"updatedAt": "2025-07-29T15:30:00Z"`  
  `}`

* **Indexing Considerations**:  
  * An index on role would be beneficial for queries filtering users by their role (e.g., "get all agents").  
  * Compound indexes might be needed for combined queries (e.g., "get users by role and last updated date").

### **properties Collection**

* **Collection Path**: properties/{propertyId}  
* **Purpose**: Each document represents a single property listing. propertyId will typically be the Firestore auto-generated document ID for simplicity, or a custom unique ID if required.  
* **Example Document (properties/autoGeneratedPropertyId456)**:  
  `{`  
    `"propertyId": "autoGeneratedPropertyId456",`  
    `"title": "Spacious 4-Bedroom Family Home",`  
    `"description": "Beautiful home with a large garden...",`  
    `"address": "123 Main St",`  
    `"city": "Lekki",`  
    `"state": "Lagos",`  
    `"zipCode": "101233",`  
    `"price": 75000000,`  
    `"propertyType": "House",`  
    `"bedrooms": 4,`  
    `"bathrooms": 3,`  
    `"squareFootage": 2500,`  
    `"agentUid": "someFirebaseUid123", # Refers to a user in the 'users' collection with role 'agent'`  
    `"status": "active",`  
    `"imageUrls": [`  
      `"https://firebasestorage.googleapis.com/v0/b/nasaga.appspot.com/o/properties%2Fimg1.jpg",`  
      `"https://firebasestorage.googleapis.com/v0/b/nasaga.appspot.com/o/properties%2Fimg2.jpg"`  
    `],`  
    `"documentUrls": [],`  
    `"createdAt": "2025-07-29T11:00:00Z",`  
    `"updatedAt": "2025-07-29T16:00:00Z",`  
    `"approvedByAdminUid": "adminFirebaseUid789", # Refers to a user in the 'users' collection with role 'admin'`  
    `"approvedAt": "2025-07-29T15:00:00Z"`  
  `}`

* **Indexing Considerations**:  
  * Indexes on status, agentUid, propertyType, price (range queries), city, and state will be crucial for efficient searching and filtering of properties.  
  * Compound indexes will be needed for common combined queries (e.g., "properties by city and status").

## **Relationships in NoSQL (Denormalization & References)**

In Firestore, relationships are primarily handled through references (storing the ID of related documents) and selective denormalization (duplicating essential related data to avoid extra reads).

* **User-Property Relationship**: The properties collection stores agentUid and approvedByAdminUid. These fields are direct references to documents in the users collection. When displaying property details, a client-side or backend lookup can fetch the associated agent/admin's displayName or other required details. For frequently displayed agent names (e.g., on a property card list), some level of denormalization (e.g., also storing agentDisplayName directly in the property document) could be considered to reduce reads, but for this MVP, direct referencing is sufficient.  
* **Favorited Properties**: Each User (Buyer) document could have a subcollection (e.g., users/{buyerUid}/favoritedProperties) storing references or simplified copies of favorited property IDs. This allows quick retrieval of a buyer's favorites without scanning all properties.

## **Security Rules Considerations**

It's critical to enforce data access and modification rules directly in Firebase Firestore Security Rules, complementing any backend API validations.

* **users collection**:  
  * Only authenticated users can read their own profile.  
  * Only superAdmin or admin roles can create or modify users documents (especially role).  
* **properties collection**:  
  * Only agent roles can create property documents.  
  * Only the agentUid associated with a property or admin/superAdmin roles can modify a property document.  
  * Only admin/superAdmin roles can change the status of a property.  
  * All authenticated users (buyer, agent, admin, superAdmin) can read approved or active properties.
