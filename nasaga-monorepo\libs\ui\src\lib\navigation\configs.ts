import {
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  Business as BusinessIcon,
  Home as HomeIcon,
  Assessment as AssessmentIcon,
  Settings as SettingsIcon,
  AccountCircle as AccountCircleIcon,
  Notifications as NotificationsIcon,
  Security as SecurityIcon,
  AdminPanelSettings as AdminPanelSettingsIcon,
} from '@mui/icons-material';
import { NavigationConfig } from './types';

/**
 * Super Admin Navigation Configuration
 * Full access to all system features
 */
export const superAdminNavigation: NavigationConfig = {
  sections: [
    {
      title: 'Core',
      items: [
        {
          id: 'dashboard',
          title: 'Dashboard',
          path: '/dashboard',
          icon: DashboardIcon,
          roles: ['superAdmin', 'admin'],
        },
        {
          id: 'users',
          title: 'User Management',
          path: '/dashboard/users',
          icon: PeopleIcon,
          roles: ['superAdmin'],
        },
        {
          id: 'properties',
          title: 'Properties',
          path: '/dashboard/properties',
          icon: HomeIcon,
          roles: ['superAdmin', 'admin'],
        },
        {
          id: 'agents',
          title: 'Agent Management',
          path: '/dashboard/agents',
          icon: BusinessIcon,
          roles: ['superAdmin', 'admin'],
        },
      ],
    },
    {
      title: 'Analytics',
      items: [
        {
          id: 'reports',
          title: 'Reports',
          path: '/dashboard/reports',
          icon: AssessmentIcon,
          roles: ['superAdmin', 'admin'],
        },
        {
          id: 'analytics',
          title: 'Analytics',
          path: '/dashboard/analytics',
          icon: AssessmentIcon,
          roles: ['superAdmin', 'admin'],
        },
      ],
    },
    {
      title: 'System',
      items: [
        {
          id: 'settings',
          title: 'System Settings',
          path: '/dashboard/settings',
          icon: SettingsIcon,
          roles: ['superAdmin'],
        },
        {
          id: 'security',
          title: 'Security',
          path: '/dashboard/security',
          icon: SecurityIcon,
          roles: ['superAdmin'],
        },
        {
          id: 'admin-tools',
          title: 'Admin Tools',
          path: '/dashboard/admin-tools',
          icon: AdminPanelSettingsIcon,
          roles: ['superAdmin'],
        },
      ],
    },
  ],
};

/**
 * Admin Navigation Configuration
 * Access to property and agent management
 */
export const adminNavigation: NavigationConfig = {
  sections: [
    {
      title: 'Core',
      items: [
        {
          id: 'dashboard',
          title: 'Dashboard',
          path: '/dashboard',
          icon: DashboardIcon,
          roles: ['admin', 'superAdmin'],
        },
        {
          id: 'properties',
          title: 'Properties',
          path: '/dashboard/properties',
          icon: HomeIcon,
          roles: ['admin', 'superAdmin'],
        },
        {
          id: 'agents',
          title: 'Agents',
          path: '/dashboard/agents',
          icon: BusinessIcon,
          roles: ['admin', 'superAdmin'],
        },
        {
          id: 'buyers',
          title: 'Buyers',
          path: '/dashboard/buyers',
          icon: PeopleIcon,
          roles: ['admin', 'superAdmin'],
        },
      ],
    },
    {
      title: 'Reports',
      items: [
        {
          id: 'property-reports',
          title: 'Property Reports',
          path: '/dashboard/property-reports',
          icon: AssessmentIcon,
          roles: ['admin', 'superAdmin'],
        },
        {
          id: 'agent-performance',
          title: 'Agent Performance',
          path: '/dashboard/agent-performance',
          icon: AssessmentIcon,
          roles: ['admin', 'superAdmin'],
        },
      ],
    },
    {
      title: 'Account',
      items: [
        {
          id: 'profile',
          title: 'My Profile',
          path: '/dashboard/profile',
          icon: AccountCircleIcon,
          roles: ['admin', 'agent', 'buyer', 'superAdmin'],
        },
        {
          id: 'notifications',
          title: 'Notifications',
          path: '/dashboard/notifications',
          icon: NotificationsIcon,
          roles: ['admin', 'agent', 'buyer', 'superAdmin'],
        },
      ],
    },
  ],
};

/**
 * Agent Navigation Configuration
 * Access to property listings and client management
 */
export const agentNavigation: NavigationConfig = {
  sections: [
    {
      title: 'Core',
      items: [
        {
          id: 'dashboard',
          title: 'Dashboard',
          path: '/dashboard',
          icon: DashboardIcon,
          roles: ['agent', 'admin', 'superAdmin'],
        },
        {
          id: 'my-properties',
          title: 'My Properties',
          path: '/dashboard/my-properties',
          icon: HomeIcon,
          roles: ['agent'],
        },
        {
          id: 'clients',
          title: 'My Clients',
          path: '/dashboard/clients',
          icon: PeopleIcon,
          roles: ['agent'],
        },
        {
          id: 'add-property',
          title: 'Add Property',
          path: '/dashboard/add-property',
          icon: BusinessIcon,
          roles: ['agent'],
        },
      ],
    },
    {
      title: 'Performance',
      items: [
        {
          id: 'my-performance',
          title: 'My Performance',
          path: '/dashboard/my-performance',
          icon: AssessmentIcon,
          roles: ['agent'],
        },
      ],
    },
    {
      title: 'Account',
      items: [
        {
          id: 'profile',
          title: 'My Profile',
          path: '/dashboard/profile',
          icon: AccountCircleIcon,
          roles: ['admin', 'agent', 'buyer', 'superAdmin'],
        },
        {
          id: 'notifications',
          title: 'Notifications',
          path: '/dashboard/notifications',
          icon: NotificationsIcon,
          roles: ['admin', 'agent', 'buyer', 'superAdmin'],
        },
      ],
    },
  ],
};

/**
 * Buyer Navigation Configuration
 * Access to property browsing and favorites
 */
export const buyerNavigation: NavigationConfig = {
  sections: [
    {
      title: 'Browse',
      items: [
        {
          id: 'dashboard',
          title: 'Dashboard',
          path: '/dashboard',
          icon: DashboardIcon,
          roles: ['buyer', 'agent', 'admin', 'superAdmin'],
        },
        {
          id: 'browse-properties',
          title: 'Browse Properties',
          path: '/dashboard/browse',
          icon: HomeIcon,
          roles: ['buyer'],
        },
        {
          id: 'favorites',
          title: 'My Favorites',
          path: '/dashboard/favorites',
          icon: HomeIcon,
          roles: ['buyer'],
        },
        {
          id: 'my-inquiries',
          title: 'My Inquiries',
          path: '/dashboard/inquiries',
          icon: BusinessIcon,
          roles: ['buyer'],
        },
      ],
    },
    {
      title: 'Account',
      items: [
        {
          id: 'profile',
          title: 'My Profile',
          path: '/dashboard/profile',
          icon: AccountCircleIcon,
          roles: ['admin', 'agent', 'buyer', 'superAdmin'],
        },
        {
          id: 'notifications',
          title: 'Notifications',
          path: '/dashboard/notifications',
          icon: NotificationsIcon,
          roles: ['admin', 'agent', 'buyer', 'superAdmin'],
        },
      ],
    },
  ],
};

/**
 * Get navigation configuration based on user role
 */
export const getNavigationByRole = (role: string): NavigationConfig => {
  switch (role) {
    case 'superAdmin':
      return superAdminNavigation;
    case 'admin':
      return adminNavigation;
    case 'agent':
      return agentNavigation;
    case 'buyer':
      return buyerNavigation;
    default:
      return buyerNavigation; // Default to most restrictive
  }
};
