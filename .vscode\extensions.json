{"recommendations": ["esbenp.prettier-vscode", "dbaeumer.vscode-eslint", "ms-vscode.vscode-typescript-next", "bradlc.vscode-tailwindcss", "ms-vscode.vscode-react-native", "formulahendry.auto-rename-tag", "christian-kohler.path-intellisense", "ms-vscode.vscode-json", "editorconfig.editorconfig", "gruntfuggly.todo-tree", "ms-vscode.vscode-eslint", "yzhang.markdown-all-in-one"], "unwantedRecommendations": ["ms-vscode.vscode-typescript", "hookyqr.beautify"]}