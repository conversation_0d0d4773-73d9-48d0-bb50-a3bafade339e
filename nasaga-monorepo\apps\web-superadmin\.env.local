# Firebase Configuration for Web Super Admin App
VITE_FIREBASE_API_KEY=your_api_key_here
VITE_FIREBASE_AUTH_DOMAIN=nasaga-real-estate.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=nasaga-real-estate
VITE_FIREBASE_STORAGE_BUCKET=nasaga-real-estate.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id_here
VITE_FIREBASE_APP_ID=your_app_id_here
VITE_FIREBASE_MEASUREMENT_ID=your_measurement_id_here
VITE_FIREBASE_VAPID_KEY=your_vapid_key_here

# Environment
VITE_NODE_ENV=development
VITE_API_URL=http://localhost:5001
