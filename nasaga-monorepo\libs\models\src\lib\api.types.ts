/**
 * Common API response interfaces for the Nasaga Real Estate Management Platform
 */

/**
 * Standard API response wrapper
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
}

/**
 * API error response interface
 */
export interface ApiError {
  success: false;
  error: string;
  message?: string;
  code?: string;
  details?: any;
  timestamp: string;
}

/**
 * API success response interface
 */
export interface ApiSuccess<T = any> {
  success: true;
  data: T;
  message?: string;
  timestamp: string;
}

/**
 * Pagination metadata interface
 */
export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * Paginated API response interface
 */
export interface PaginatedApiResponse<T = any> extends ApiSuccess<T[]> {
  pagination: PaginationMeta;
}

/**
 * Authentication token response interface
 */
export interface AuthTokenResponse {
  token: string;
  refreshToken?: string;
  expiresIn: number;
  user: {
    uid: string;
    email: string;
    displayName: string;
    role: string;
  };
}

/**
 * File upload response interface
 */
export interface FileUploadResponse {
  url: string;
  filename: string;
  size: number;
  contentType: string;
  uploadedAt: string;
}

/**
 * Batch operation response interface
 */
export interface BatchOperationResponse {
  successful: number;
  failed: number;
  errors?: Array<{
    index: number;
    error: string;
  }>;
}

/**
 * Health check response interface
 */
export interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  version: string;
  services: {
    database: 'connected' | 'disconnected';
    storage: 'connected' | 'disconnected';
    auth: 'connected' | 'disconnected';
  };
}

/**
 * HTTP status codes enum
 */
export enum HttpStatusCode {
  OK = 200,
  CREATED = 201,
  NO_CONTENT = 204,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  CONFLICT = 409,
  UNPROCESSABLE_ENTITY = 422,
  INTERNAL_SERVER_ERROR = 500,
  SERVICE_UNAVAILABLE = 503,
}

/**
 * API endpoint paths enum
 */
export enum ApiEndpoints {
  // Authentication
  AUTH_REGISTER = '/auth/register',
  AUTH_LOGIN = '/auth/login',
  AUTH_LOGOUT = '/auth/logout',
  AUTH_REFRESH = '/auth/refresh',
  AUTH_PROFILE = '/auth/profile',
  
  // Users
  USERS = '/users',
  USER_BY_ID = '/users/:id',
  USER_ROLE = '/users/:id/role',
  
  // Properties
  PROPERTIES = '/properties',
  PROPERTY_BY_ID = '/properties/:id',
  PROPERTY_STATUS = '/properties/:id/status',
  PROPERTY_IMAGES = '/properties/:id/images',
  
  // Admin
  ADMIN_USERS = '/admin/users',
  ADMIN_USER_ROLE = '/admin/users/:id/role',
  ADMIN_PROPERTIES = '/admin/properties',
  ADMIN_ANALYTICS = '/admin/analytics',
  
  // Health
  HEALTH = '/health',
}

/**
 * Request validation error interface
 */
export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

/**
 * Validation error response interface
 */
export interface ValidationErrorResponse extends ApiError {
  errors: ValidationError[];
}
