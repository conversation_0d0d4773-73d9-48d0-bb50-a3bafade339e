{"extends": "../../tsconfig.base.json", "compilerOptions": {"outDir": "dist", "types": ["node", "express"], "rootDir": "src", "tsBuildInfoFile": "dist/tsconfig.app.tsbuildinfo"}, "include": ["src/**/*.ts"], "exclude": ["out-tsc", "dist", "jest.config.ts", "src/**/*.spec.ts", "src/**/*.test.ts", "eslint.config.js", "eslint.config.cjs", "eslint.config.mjs"], "references": [{"path": "../../libs/models/tsconfig.lib.json"}, {"path": "../../libs/utils/tsconfig.lib.json"}]}