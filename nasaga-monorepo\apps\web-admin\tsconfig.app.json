{"extends": "../../tsconfig.base.json", "compilerOptions": {"outDir": "dist", "tsBuildInfoFile": "dist/tsconfig.app.tsbuildinfo", "jsx": "react-jsx", "lib": ["dom"], "types": ["node", "@nx/react/typings/cssmodule.d.ts", "@nx/react/typings/image.d.ts", "vite/client"], "rootDir": "src", "module": "esnext", "moduleResolution": "bundler"}, "exclude": ["out-tsc", "dist", "src/**/*.spec.ts", "src/**/*.test.ts", "src/**/*.spec.tsx", "src/**/*.test.tsx", "src/**/*.spec.js", "src/**/*.test.js", "src/**/*.spec.jsx", "src/**/*.test.jsx", "jest.config.ts", "eslint.config.js", "eslint.config.cjs", "eslint.config.mjs"], "include": ["src/**/*.js", "src/**/*.jsx", "src/**/*.ts", "src/**/*.tsx"], "references": [{"path": "../../libs/utils/tsconfig.lib.json"}, {"path": "../../libs/ui/tsconfig.lib.json"}, {"path": "../../libs/models/tsconfig.lib.json"}, {"path": "../../libs/auth/tsconfig.lib.json"}]}