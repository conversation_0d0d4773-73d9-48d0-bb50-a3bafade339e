#!/bin/bash

# Nasaga Backend Cloud Functions Deployment Script

set -e

echo "🚀 Starting Nasaga Backend deployment to Cloud Functions..."

# Build the backend application
echo "📦 Building backend application..."
npx nx build backend

# Navigate to the dist directory
cd dist/apps/backend

# Copy the functions package.json to the dist directory
echo "📄 Setting up package.json for Cloud Functions..."
cp ../../../apps/backend/functions-package.json ./package.json

# Install production dependencies
echo "📥 Installing production dependencies..."
npm install --production

# Deploy to Firebase Cloud Functions
echo "🔥 Deploying to Firebase Cloud Functions..."
cd ../../../
firebase deploy --only functions

echo "✅ Deployment completed successfully!"
echo "🌐 Your API is now available at: https://us-central1-[your-project-id].cloudfunctions.net/api"
