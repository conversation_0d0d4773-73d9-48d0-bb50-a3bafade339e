# **Development Workflow**

## **Local Development Setup**

### **Prerequisites**

Before starting, ensure the following are installed:  
`# Node.js (LTS version, e.g., v20.x, as per Tech Stack)`  
`# npm (comes with Node.js) or pnpm (recommended for monorepos with Nx)`  
`# Git`  
`# Android Studio (for Android development and emulator on Windows/Linux/macOS)`  
`# Watchman (for React Native file watching on macOS/Linux - not strictly needed on Windows but good practice for project consistency)`  
`# Java Development Kit (JDK) - specific version depends on Android SDK needs`

`# Note for iOS development on Windows:`  
`# Xcode is NOT required for Windows users due to the use of Expo.`  
`# For building iOS applications, Expo Application Services (EAS Build) will be utilized for cloud builds.`  
`# Testing iOS apps during development can be done on a physical iOS device using the Expo Go app.`

### **Initial Setup**

Once prerequisites are met, clone the repository and install dependencies:  
`# 1. Clone the monorepo`  
`git clone [repository-url] nasaga-monorepo`  
`cd nasaga-monorepo`

`# 2. Install pnpm (if not already installed)`  
`npm install -g pnpm`

`# 3. Configure pnpm for hoisted node_modules (crucial for React Native/Expo in monorepo)`  
`# Add 'node-linker=hoisted' to .npmrc file at the project root`  
`echo "node-linker=hoisted" >> .npmrc`

`# 4. Install monorepo dependencies using pnpm`  
`pnpm install`

`# 5. Link local Expo CLI (if not globally available or needed for Nx integration)`  
`# pnpm add -D expo-cli # Or global install if preferred`

### **Development Commands**

Nx simplifies running commands across multiple applications in the monorepo. Here are common commands:  
`# Start all services (Backend API, Admin Web, Super Admin Web, Agent Mobile, Buyer Mobile)`  
`# This will typically require separate terminal tabs or a more advanced orchestration script`  
`# For web apps:`  
`npx nx serve admin-web`  
`npx nx serve super-admin-web`  
``# For mobile apps (requires Metro Bundler to start, often handled by `expo start`):``  
`npx nx start agent-mobile --web # For web preview of mobile app`  
`npx nx start buyer-mobile --web # For web preview of mobile app`  
`# For physical device/emulator testing, run 'npx nx start <app-name>' and then`  
`# use the Expo Go app on your device/emulator to scan the QR code.`

`# For backend:`  
`npx nx serve backend`

`# Start frontend only (example for Admin Web)`  
`npx nx serve admin-web`

`# Start backend only`  
`npx nx serve backend`

`# Run tests for all affected projects (after changes)`  
`npx nx affected --target=test --parallel`

`# Run tests for a specific project (e.g., agent-mobile)`  
`npx nx test agent-mobile`

`# Run E2E tests for a specific web app (e.g., admin-web)`  
`npx nx e2e admin-web-e2e`

`# Build a specific project for production (e.g., admin-web)`  
`npx nx build admin-web`

`# Build all affected projects for production`  
`npx nx affected --target=build --parallel`

## **Environment Configuration**

Environment variables will be managed using .env files within each application's root (or globally at the monorepo root for shared variables) and accessed through a consistent configuration loading mechanism. Sensitive variables will be securely handled in deployment environments.

### **Required Environment Variables**

`# Shared (could be in .env at root or in specific app .env files)`  
`# For Firebase Client SDKs (can often be loaded from firebase.json/app.json by Expo/Firebase itself)`  
`# This is mainly for illustration if manual env setup is needed.`  
`# REACT_APP_FIREBASE_API_KEY="YOUR_FIREBASE_API_KEY"`  
`# REACT_APP_FIREBASE_AUTH_DOMAIN="YOUR_FIREBASE_AUTH_DOMAIN"`  
`# REACT_APP_FIREBASE_PROJECT_ID="YOUR_FIREBASE_PROJECT_ID"`  
`# ...etc.`

`# Frontend (.env.local for web apps, or app.config.js for Expo)`  
`# apps/admin-web/.env.local`  
`REACT_APP_BACKEND_API_URL="http://localhost:3000/v1" # Local backend API URL`

`# Backend (.env for Node.js/Express app)`  
`# apps/backend/.env`  
`FIREBASE_PRIVATE_KEY_ID="YOUR_FIREBASE_PRIVATE_KEY_ID" # For Firebase Admin SDK`  
`FIREBASE_PRIVATE_KEY="YOUR_FIREBASE_PRIVATE_KEY" # Replace \n with actual newlines or load from file`  
`FIREBASE_CLIENT_EMAIL="YOUR_FIREBASE_CLIENT_EMAIL"`  
`FIREBASE_PROJECT_ID="YOUR_FIREBASE_PROJECT_ID"`  
`NODE_ENV="development"`  
`PORT="3000"`
