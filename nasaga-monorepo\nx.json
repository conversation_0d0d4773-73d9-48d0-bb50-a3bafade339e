{"$schema": "./node_modules/nx/schemas/nx-schema.json", "defaultBase": "master", "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/eslint.config.mjs", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/src/test-setup.[jt]s", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/test-setup.[jt]s"], "sharedGlobals": []}, "plugins": [{"plugin": "@nx/js/typescript", "options": {"typecheck": {"targetName": "typecheck"}, "build": {"targetName": "build", "configName": "tsconfig.lib.json", "buildDepsName": "build-deps", "watchDepsName": "watch-deps"}}}, {"plugin": "@nx/react/router-plugin", "options": {"buildTargetName": "build", "devTargetName": "dev", "startTargetName": "start", "watchDepsTargetName": "watch-deps", "buildDepsTargetName": "build-deps", "typecheckTargetName": "typecheck"}}, {"plugin": "@nx/eslint/plugin", "options": {"targetName": "lint"}}, {"plugin": "@nx/vite/plugin", "options": {"buildTargetName": "build", "testTargetName": "test", "serveTargetName": "serve", "devTargetName": "dev", "previewTargetName": "preview", "serveStaticTargetName": "serve-static", "typecheckTargetName": "typecheck", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}, {"plugin": "@nx/playwright/plugin", "options": {"targetName": "e2e"}}, {"plugin": "@nx/jest/plugin", "options": {"targetName": "test"}, "exclude": ["apps/backend-e2e/**/*"]}, {"plugin": "@nx/expo/plugin", "options": {"startTargetName": "start", "buildTargetName": "build", "prebuildTargetName": "prebuild", "serveTargetName": "serve", "installTargetName": "install", "exportTargetName": "export", "submitTargetName": "submit", "runIosTargetName": "run-ios", "runAndroidTargetName": "run-android", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}, {"plugin": "@nx/webpack/plugin", "options": {"buildTargetName": "build", "serveTargetName": "serve", "previewTargetName": "preview", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}], "targetDefaults": {"build": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"], "outputs": ["{projectRoot}/dist"]}, "test": {"cache": true, "dependsOn": ["^build"], "inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "outputs": ["{workspaceRoot}/coverage/{projectRoot}"]}, "lint": {"cache": true, "inputs": ["default", "{workspaceRoot}/.eslintrc.json", "{workspaceRoot}/.eslintignore"]}, "typecheck": {"cache": true, "inputs": ["default", "^production"]}, "e2e": {"cache": true, "dependsOn": ["build"]}, "@nx/js:swc": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}}, "generators": {"@nx/react": {"application": {"babel": true, "style": "tailwind", "linter": "eslint", "bundler": "vite"}, "component": {"style": "tailwind"}, "library": {"style": "tailwind", "linter": "eslint", "unitTestRunner": "jest"}}}}