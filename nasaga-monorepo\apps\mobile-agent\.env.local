# Firebase Configuration for Mobile Agent App
EXPO_PUBLIC_FIREBASE_API_KEY=your_api_key_here
EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=nasaga-real-estate.firebaseapp.com
EXPO_PUBLIC_FIREBASE_PROJECT_ID=nasaga-real-estate
EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=nasaga-real-estate.appspot.com
EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id_here
EXPO_PUBLIC_FIREBASE_APP_ID=your_app_id_here
EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID=your_measurement_id_here

# Environment
EXPO_PUBLIC_NODE_ENV=development
EXPO_PUBLIC_API_URL=http://localhost:3000
