# **Data Models**

This section defines the core data models/entities that will be shared between frontend and backend. We'll outline each model's purpose, key attributes, and relationships, and provide a TypeScript interface for consistency across the full stack.

## **User**

**Purpose:** Represents a user within the Nasaga Real Estate Management Platform. This model will store user authentication details (linked to Firebase Auth UID), role, and basic profile information.  
**Key Attributes:**

* uid: string \- Unique Firebase Authentication user ID.  
* email: string \- User's email address.  
* displayName: string \- User's display name.  
* role: UserRole \- User's assigned role (Super Admin, Admin, Agent, Buyer).  
* phoneNumber: string (optional) \- User's phone number.  
* profilePictureUrl: string (optional) \- URL to user's profile picture in Firebase Storage.  
* createdAt: Timestamp \- Timestamp of user creation.  
* updatedAt: Timestamp \- Timestamp of last update.

**TypeScript Interface**  
`// packages/types/src/user.d.ts`  
`export type UserRole = 'superAdmin' | 'admin' | 'agent' | 'buyer';`

`export interface User {`  
  `uid: string;`  
  `email: string;`  
  `displayName: string;`  
  `role: UserRole;`  
  `phoneNumber?: string;`  
  `profilePictureUrl?: string;`  
  `createdAt: firebase.firestore.Timestamp;`  
  `updatedAt: firebase.firestore.Timestamp;`  
`}`

**Relationships:**

* User (Agent role) can create multiple Property listings.  
* User (Buyer role) can favorite multiple Property listings.  
* User (Admin/Super Admin roles) manages User accounts and Property listings.

## **Property**

**Purpose:** Represents a single property listing available on the Nasaga Real Estate Management Platform. This model will store all relevant details about a property, its current status, and a reference to the agent who listed it.  
**Key Attributes:**

* propertyId: string \- Unique identifier for the property.  
* title: string \- Short, descriptive title of the property (e.g., "Spacious Family Home in Lekki").  
* description: string \- Detailed description of the property.  
* address: string \- Full street address of the property.  
* city: string \- City where the property is located.  
* state: string \- State where the property is located.  
* zipCode: string \- Postal code of the property.  
* price: number \- Asking price of the property.  
* propertyType: string \- Type of property (e.g., "House", "Apartment", "Land", "Commercial").  
* bedrooms: number \- Number of bedrooms.  
* bathrooms: number \- Number of bathrooms.  
* squareFootage: number (optional) \- Total area in square feet/meters.  
* agentUid: string \- Firebase UID of the agent who listed the property.  
* status: PropertyStatus \- Current status of the property (e.g., "pending", "approved", "active", "under offer", "sold", "delisted").  
* imageUrls: string\[\] \- Array of URLs to property images stored in Firebase Storage.  
* documentUrls: string\[\] (optional) \- Array of URLs to associated documents (e.g., floor plans) in Firebase Storage.  
* createdAt: Timestamp \- Timestamp of property listing creation.  
* updatedAt: Timestamp \- Timestamp of last update.  
* approvedByAdminUid: string (optional) \- Firebase UID of the Admin who approved the listing.  
* approvedAt: Timestamp (optional) \- Timestamp of approval.

**TypeScript Interface**  
`// packages/types/src/property.d.ts`  
`export type PropertyStatus = 'pending' | 'approved' | 'active' | 'under offer' | 'sold' | 'delisted';`

`export interface Property {`  
  `propertyId: string;`  
  `title: string;`  
  `description: string;`  
  `address: string;`  
  `city: string;`  
  `state: string;`  
  `zipCode: string;`  
  `price: number;`  
  `propertyType: string;`  
  `bedrooms: number;`  
  `bathrooms: number;`  
  `squareFootage?: number;`  
  `agentUid: string; // Foreign key to User.uid`  
  `status: PropertyStatus;`  
  `imageUrls: string[];`  
  `documentUrls?: string[];`  
  `createdAt: firebase.firestore.Timestamp;`  
  `updatedAt: firebase.firestore.Timestamp;`  
  `approvedByAdminUid?: string; // Foreign key to User.uid`  
  `approvedAt?: firebase.firestore.Timestamp;`  
`}`

**Relationships:**

* Property is listed by one User (Agent).  
* Property can be favorited by multiple User (Buyer) accounts.  
* Property is managed (approved/rejected) by one User (Admin/Super Admin).
