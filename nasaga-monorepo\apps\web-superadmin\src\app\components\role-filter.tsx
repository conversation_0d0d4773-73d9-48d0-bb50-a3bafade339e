import React from 'react';
import { UserRole } from '@nasaga-monorepo/models';

interface RoleFilterProps {
  selectedRole: UserRole | 'all';
  onRoleChange: (role: UserRole | 'all') => void;
}

export const RoleFilter: React.FC<RoleFilterProps> = ({ selectedRole, onRoleChange }) => {
  const roles: Array<{ value: UserRole | 'all'; label: string; color: string }> = [
    { value: 'all', label: 'All Users', color: 'bg-gray-100 text-gray-800' },
    { value: 'superAdmin', label: 'Super Admin', color: 'bg-red-100 text-red-800' },
    { value: 'admin', label: 'Admin', color: 'bg-purple-100 text-purple-800' },
    { value: 'agent', label: 'Agent', color: 'bg-blue-100 text-blue-800' },
    { value: 'buyer', label: 'Buyer', color: 'bg-green-100 text-green-800' },
  ];

  return (
    <div className="flex flex-wrap gap-2">
      {roles.map((role) => (
        <button
          key={role.value}
          onClick={() => onRoleChange(role.value)}
          className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
            selectedRole === role.value
              ? `${role.color} ring-2 ring-offset-2 ring-indigo-500`
              : `${role.color} hover:opacity-80`
          }`}
        >
          {role.label}
        </button>
      ))}
    </div>
  );
};

export default RoleFilter;
