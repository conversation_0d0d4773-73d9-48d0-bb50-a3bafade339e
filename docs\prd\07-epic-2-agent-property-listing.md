# Epic 2: Agent Property Listing & Management

**Epic Goal**: Enable real estate agents to fully manage their property listings through a dedicated, intuitive mobile application, including creation, detailed editing, media uploads, and status updates, directly integrated with the platform's core data.

## Story 2.1 Agent Mobile App Setup & Profile Management

**As an** Agent,  
**I want** to securely log in to the Agent Mobile App and manage my profile information,  
**so that** I can personalize my experience and prepare to list properties.

### Acceptance Criteria

1.  **1**: The Agent Mobile App is successfully built with React Native/Expo and connects to Firebase.  
2.  **2**: A secure login and registration flow (using Firebase Auth) is implemented for agents.  
3.  **3**: Agents can view and edit their profile details (e.g., name, contact info, profile picture) within the app, stored in Firestore.  
4.  **4**: Profile picture uploads to Firebase Storage are supported.  
5.  **5**: Agent app access is restricted to authenticated users with the 'agent' role.

## Story 2.2 Create New Property Listing

**As an** Agent,  
**I want** to create a new property listing with essential details via the mobile app,  
**so that** I can quickly add properties to the platform.

### Acceptance Criteria

1.  **1**: Agents can navigate to a "Create New Property" form in the Agent App.  
2.  **2**: The form allows input for essential property fields: title, description, address, price, property type, number of bedrooms/bathrooms, and agent contact.  
3.  **3**: Agents can upload multiple images for a property listing to Firebase Storage.  
4.  **4**: Upon submission, the property data is saved to Firestore with a 'pending' status, linked to the agent's UID.  
5.  **5**: Required fields are validated client-side before submission.

## Story 2.3 View, Edit, and Delist Existing Properties

**As an** Agent,  
**I want** to view, edit, or delist my existing property listings via the mobile app,  
**so that** I can keep my property portfolio accurate and up-to-date.

### Acceptance Criteria

1.  **1**: Agents can view a list of all properties they have created.  
2.  **2**: Agents can select an individual property to view its full details.  
3.  **3**: Agents can edit any field of an existing property listing, including adding/removing images.  
4.  **4**: Agents can change a property's status (e.g., 'active', 'under offer', 'sold', 'delisted').  
5.  **5**: Changes made by agents are reflected in Firestore and propagate to relevant dashboards/apps (e.g., Buyer App, Admin Dashboard) in real-time.
