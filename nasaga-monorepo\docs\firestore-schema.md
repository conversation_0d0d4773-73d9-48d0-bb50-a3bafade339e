# Firestore Schema & Security Rules v1

## Overview

This document outlines the Firestore database schema and security rules v1 for the Nasaga Real Estate Management Platform. The schema is designed to support role-based access control with strict security boundaries.

## Collections

### 1. Users Collection (`/users/{uid}`)

**Schema:**
```typescript
interface User {
  uid: string;                    // Firebase Auth UID (document ID)
  email: string;                  // User email
  displayName: string;            // User display name
  role: 'superAdmin' | 'admin' | 'agent' | 'buyer';
  phoneNumber?: string;           // Optional phone number
  profilePictureUrl?: string;     // Optional profile picture URL
  createdAt: Timestamp;           // Creation timestamp
  updatedAt: Timestamp;           // Last update timestamp
}
```

**Security Rules:**
- **Read/Write**: Only the document owner (`uid` matches `request.auth.uid`)
- **Role Immutability**: The `role` field is immutable except when modified by `superAdmin`
- **Self-Service**: Users can manage their own profiles but cannot escalate privileges

### 2. Properties Collection (`/properties/{propertyId}`)

**Schema:**
```typescript
interface Property {
  propertyId: string;             // Unique property identifier (document ID)
  title: string;                  // Property title
  description: string;            // Property description
  address: string;                // Full address
  city: string;                   // City
  state: string;                  // State
  zipCode: string;                // ZIP code
  price: number;                  // Property price
  propertyType: string;           // Type (House, Apartment, etc.)
  bedrooms: number;               // Number of bedrooms
  bathrooms: number;              // Number of bathrooms
  squareFootage?: number;         // Optional square footage
  agentUid: string;               // Agent who created the listing
  status: PropertyStatus;         // Current status
  imageUrls: string[];            // Array of image URLs
  documentUrls?: string[];        // Optional document URLs
  createdAt: Timestamp;           // Creation timestamp
  updatedAt: Timestamp;           // Last update timestamp  
  approvedByAdminUid?: string;    // Admin who approved (optional)
  approvedAt?: Timestamp;         // Approval timestamp (optional)
}

type PropertyStatus = 'pending' | 'approved' | 'active' | 'under offer' | 'sold' | 'delisted';
```

**Security Rules:**
- **Create**: Only `agents` can create properties with `status: 'pending'`
- **Read**: 
  - Pending properties (`status: 'pending'`) can only be read by `admins` and `superAdmins`
  - All other status properties are publicly readable
- **Update**: 
  - Agents can update their own properties but cannot change status from `pending`
  - Only `admins`/`superAdmins` can set status to values other than `pending`
- **Delete**: Only `admins`/`superAdmins` can delete properties

## Role Hierarchy

```
superAdmin > admin > agent/buyer
```

**Role Definitions:**
- **superAdmin**: Full system access, can modify user roles
- **admin**: Can manage properties and users (except role changes)  
- **agent**: Can create and manage their own property listings
- **buyer**: Can view approved/active properties

## Property Status Flow

```
pending → approved → active → under offer → sold
              ↓
           delisted
```

**Status Control:**
- `agents` can only create with `status: 'pending'`
- Only `admins`/`superAdmins` can transition from `pending` to other statuses
- This ensures quality control and prevents agents from bypassing approval processes

## Security Principles

1. **Least Privilege**: Users only have access to resources they own or are authorized to access
2. **Role Immutability**: Prevents privilege escalation by regular users
3. **Admin Approval**: Properties require admin approval before becoming public
4. **Data Isolation**: Each user can only access their own user document
5. **Audit Trail**: All property status changes require admin involvement

## Indexes

The following composite indexes are configured for efficient querying:

### Properties Collection
- `status` + `createdAt` (DESC) - For admin property management
- `agentUid` + `status` + `createdAt` (DESC) - For agent property management

### Users Collection  
- `role` + `createdAt` (DESC) - For admin user management

## Deployment

The Firestore rules are automatically deployed via CI/CD pipeline:

```yaml
- name: Deploy Firestore Rules
  run: |
    npm install -g firebase-tools
    firebase deploy --only firestore:rules --project ${{ secrets.FIREBASE_PROJECT_ID }} --token ${{ secrets.FIREBASE_TOKEN }}
```

**Files:**
- `firestore.rules` - Security rules
- `firestore.indexes.json` - Database indexes
- `firebase.json` - Firebase configuration

## Testing

Test the security rules using Firebase Emulator Suite:

```bash
firebase emulators:start --only firestore
```

Use the Firebase console to test various access scenarios and ensure rules work as expected.
