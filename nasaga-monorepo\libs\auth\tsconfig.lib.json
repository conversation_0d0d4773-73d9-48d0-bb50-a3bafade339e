{"extends": "../../tsconfig.base.json", "compilerOptions": {"baseUrl": ".", "rootDir": "src", "outDir": "dist", "tsBuildInfoFile": "dist/tsconfig.lib.tsbuildinfo", "emitDeclarationOnly": true, "forceConsistentCasingInFileNames": true, "types": ["node"]}, "include": ["src/**/*.ts"], "references": [{"path": "../utils/tsconfig.lib.json"}, {"path": "../models/tsconfig.lib.json"}], "exclude": ["jest.config.ts", "src/**/*.spec.ts", "src/**/*.test.ts"]}