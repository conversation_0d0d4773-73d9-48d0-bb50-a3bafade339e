{"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"9396960111258140657": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "15834015817885073645": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "9396960111258140657": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "15834015817885073645": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "9396960111258140657": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "15834015817885073645": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "9396960111258140657": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "15834015817885073645": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "9396960111258140657": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "15834015817885073645": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "9396960111258140657": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "15834015817885073645": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "9396960111258140657": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "15834015817885073645": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "9396960111258140657": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "15834015817885073645": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "9396960111258140657": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "15834015817885073645": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "9396960111258140657": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "15834015817885073645": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "9396960111258140657": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "15834015817885073645": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "9396960111258140657": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "15834015817885073645": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "9396960111258140657": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "15834015817885073645": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "9396960111258140657": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "15834015817885073645": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "9396960111258140657": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "15834015817885073645": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "9396960111258140657": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "15834015817885073645": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "9396960111258140657": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "15834015817885073645": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "9396960111258140657": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "15834015817885073645": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "9396960111258140657": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "15834015817885073645": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "9396960111258140657": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "15834015817885073645": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "9396960111258140657": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "15834015817885073645": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}}