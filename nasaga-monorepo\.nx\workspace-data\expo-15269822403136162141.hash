{"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {"targetsCache": {}, "12575916436523558992": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}}, "12575916436523558992": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "1964902575417912170": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "12575916436523558992": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "1964902575417912170": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "12624334065551596653": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "11765009374799587863": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "12624334065551596653": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "11765009374799587863": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "12199975659036303274": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "4954941707698324232": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "12199975659036303274": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "4954941707698324232": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "9301777214783199405": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "16715018985227202817": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "9301777214783199405": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "16715018985227202817": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "9314672425873920864": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "5637849567693056392": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "9314672425873920864": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "5637849567693056392": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "2589782477168487748": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "9240537797205116954": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "2589782477168487748": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "9240537797205116954": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "2589782477168487748": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "9240537797205116954": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "2589782477168487748": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "9240537797205116954": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "2589782477168487748": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "9240537797205116954": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "2589782477168487748": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "9240537797205116954": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "2589782477168487748": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "9240537797205116954": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "2589782477168487748": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "9240537797205116954": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "2589782477168487748": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "9240537797205116954": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "2589782477168487748": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "9240537797205116954": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "2589782477168487748": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "9240537797205116954": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}, "2589782477168487748": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-agent"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-agent", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-agent/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-agent"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-agent --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-agent"}}, "9240537797205116954": {"start": {"executor": "@nx/expo:start", "continuous": true}, "serve": {"command": "expo start --web", "continuous": true, "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}}, "run-ios": {"command": "expo run:ios", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "run-android": {"command": "expo run:android", "continuous": true, "options": {"cwd": "apps/mobile-buyer"}}, "export": {"command": "expo export", "options": {"cwd": "apps/mobile-buyer", "args": ["--clear"]}, "cache": true, "dependsOn": ["^export"], "inputs": ["default", "^production", {"externalDependencies": ["expo"]}], "outputs": ["{workspaceRoot}/apps/mobile-buyer/dist"]}, "install": {"executor": "@nx/expo:install"}, "prebuild": {"executor": "@nx/expo:prebuild"}, "build": {"executor": "@nx/expo:build"}, "submit": {"command": "eas submit", "options": {"cwd": "apps/mobile-buyer"}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/mobile-buyer --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/mobile-buyer"}}}}