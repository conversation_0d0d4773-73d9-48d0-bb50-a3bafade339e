@echo off

REM Nasaga Backend Cloud Functions Deployment Script for Windows

echo 🚀 Starting Nasaga Backend deployment to Cloud Functions...

REM Build the backend application
echo 📦 Building backend application...
call npx nx build backend
if %errorlevel% neq 0 goto :error

REM Navigate to the dist directory
cd dist\apps\backend

REM Copy the functions package.json to the dist directory
echo 📄 Setting up package.json for Cloud Functions...
copy ..\..\..\apps\backend\functions-package.json .\package.json
if %errorlevel% neq 0 goto :error

REM Install production dependencies
echo 📥 Installing production dependencies...
call npm install --production
if %errorlevel% neq 0 goto :error

REM Deploy to Firebase Cloud Functions
echo 🔥 Deploying to Firebase Cloud Functions...
cd ..\..\..
call firebase deploy --only functions
if %errorlevel% neq 0 goto :error

echo ✅ Deployment completed successfully!
echo 🌐 Your API is now available at: https://us-central1-[your-project-id].cloudfunctions.net/api
goto :end

:error
echo ❌ Deployment failed!
exit /b 1

:end
