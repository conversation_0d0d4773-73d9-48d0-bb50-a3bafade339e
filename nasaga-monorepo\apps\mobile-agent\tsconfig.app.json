{"extends": "../../tsconfig.base.json", "compilerOptions": {"outDir": "dist", "types": ["node"], "rootDir": "src", "jsx": "react-jsx", "module": "esnext", "moduleResolution": "bundler", "noUnusedLocals": false, "tsBuildInfoFile": "dist/tsconfig.app.tsbuildinfo"}, "files": ["../../node_modules/@nx/expo/typings/svg.d.ts"], "exclude": ["out-tsc", "dist", "**/*.test.ts", "**/*.spec.ts", "**/*.test.tsx", "**/*.spec.tsx", "**/*.test.js", "**/*.spec.js", "**/*.test.jsx", "**/*.spec.jsx", "src/test-setup.ts", "jest.config.ts", "src/**/*.spec.ts", "src/**/*.test.ts", "eslint.config.js", "eslint.config.cjs", "eslint.config.mjs"], "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"], "references": [{"path": "../../libs/ui/tsconfig.lib.json"}, {"path": "../../libs/auth/tsconfig.lib.json"}]}