import React from 'react';
import { useAuth } from '@nasaga-monorepo/auth';
import { LoginForm } from '../components/login-form';
import { LoginFormData } from '../schemas/auth-schemas';

export interface LoginPageProps {
  onSignUp?: () => void;
  onForgotPassword?: () => void;
  onLoginSuccess?: () => void;
  variant?: 'default' | 'mobile';
}

/**
 * Login page component that integrates with the auth context
 */
export const LoginPage: React.FC<LoginPageProps> = ({
  onSignUp,
  onForgotPassword,
  onLoginSuccess,
  variant = 'default',
}) => {
  const { signIn, signInWithGoogle, loading, error, clearError } = useAuth();

  const handleSubmit = async (data: LoginFormData): Promise<void> => {
    clearError();
    try {
      await signIn(data);
      onLoginSuccess?.();
    } catch (err) {
      // Error is already handled by the auth context
      throw err;
    }
  };

  const handleGoogleSignIn = async (): Promise<void> => {
    clearError();
    try {
      await signInWithGoogle();
      onLoginSuccess?.();
    } catch (err) {
      // Error is already handled by the auth context
      throw err;
    }
  };

  const containerClasses = variant === 'mobile'
    ? 'min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8'
    : 'min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8';

  return (
    <div className={containerClasses}>
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
            <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z" />
            </svg>
          </div>
        </div>
        <h1 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Nasaga Real Estate
        </h1>
        <p className="mt-2 text-center text-sm text-gray-600">
          Your trusted real estate platform
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <LoginForm
          onSubmit={handleSubmit}
          onSignInWithGoogle={handleGoogleSignIn}
          onForgotPassword={onForgotPassword}
          onSignUp={onSignUp}
          isLoading={loading}
          error={error}
          variant={variant}
        />
      </div>
    </div>
  );
};
