import { initializeApp, getApps, FirebaseApp } from 'firebase/app';
import { getAuth, Auth, connectAuthEmulator } from 'firebase/auth';
import { getFirestore, Firestore, connectFirestoreEmulator } from 'firebase/firestore';
import { getStorage, FirebaseStorage, connectStorageEmulator } from 'firebase/storage';
import { getMessaging, Messaging, getToken, onMessage, isSupported } from 'firebase/messaging';
import { getFunctions, Functions, connectFunctionsEmulator } from 'firebase/functions';

// Firebase configuration interface
export interface FirebaseConfig {
  apiKey: string;
  authDomain: string;
  projectId: string;
  storageBucket: string;
  messagingSenderId: string;
  appId: string;
}

// Get environment variable with fallback support for different bundlers
const getEnvVar = (key: string): string => {
  // Support for Vite (VITE_), Next.js (NEXT_PUBLIC_), and Expo (EXPO_PUBLIC_)
  return process.env[`NEXT_PUBLIC_${key}`] || 
         process.env[`VITE_${key}`] || 
         process.env[`EXPO_PUBLIC_${key}`] || 
         '';
};

// Get Firebase configuration from environment variables
const getFirebaseConfig = (): FirebaseConfig => {
  const config = {
    apiKey: getEnvVar('FIREBASE_API_KEY'),
    authDomain: getEnvVar('FIREBASE_AUTH_DOMAIN'),
    projectId: getEnvVar('FIREBASE_PROJECT_ID'),
    storageBucket: getEnvVar('FIREBASE_STORAGE_BUCKET'),
    messagingSenderId: getEnvVar('FIREBASE_MESSAGING_SENDER_ID'),
    appId: getEnvVar('FIREBASE_APP_ID'),
  };

  // Validate that all required config values are present
  const missingKeys = Object.entries(config)
    .filter(([_, value]) => !value)
    .map(([key, _]) => key);

  if (missingKeys.length > 0) {
    throw new Error(
      `Missing Firebase configuration: ${missingKeys.join(', ')}. ` +
      'Please check your environment variables.'
    );
  }

  return config;
};

// Initialize Firebase app
let app: FirebaseApp;
let auth: Auth;
let firestore: Firestore;
let storage: FirebaseStorage;
let messaging: Messaging | null = null;
let functions: Functions;

export const initializeFirebase = (): {
  app: FirebaseApp;
  auth: Auth;
  firestore: Firestore;
  storage: FirebaseStorage;
  messaging: Messaging | null;
  functions: Functions;
} => {
  try {
    // Check if Firebase is already initialized
    if (getApps().length === 0) {
      const config = getFirebaseConfig();
      app = initializeApp(config);
    } else {
      app = getApps()[0];
    }

    // Initialize Firebase services
    auth = getAuth(app);
    firestore = getFirestore(app);
    storage = getStorage(app);
    functions = getFunctions(app);
    
    // Initialize messaging (only in browser environment)
    if (typeof window !== 'undefined') {
      isSupported().then((supported) => {
        if (supported) {
          messaging = getMessaging(app);
        }
      }).catch((error) => {
        console.warn('Firebase messaging not supported:', error);
      });
    }

    // Connect to emulators in development
    if (process.env['NODE_ENV'] === 'development') {
      const authEmulatorHost = process.env['FIREBASE_AUTH_EMULATOR_HOST'];
      const firestoreEmulatorHost = process.env['FIRESTORE_EMULATOR_HOST'];
      const storageEmulatorHost = process.env['FIREBASE_STORAGE_EMULATOR_HOST'];
      const functionsEmulatorHost = process.env['FIREBASE_FUNCTIONS_EMULATOR_HOST'];

      // Connect to Auth emulator
      if (authEmulatorHost && !auth.config.emulator) {
        const [host, port] = authEmulatorHost.split(':');
        connectAuthEmulator(auth, `http://${host}:${port}`, { disableWarnings: true });
      }

      // Connect to Firestore emulator
      if (firestoreEmulatorHost && !firestore._delegate._databaseId.projectId.includes('demo-')) {
        const [host, port] = firestoreEmulatorHost.split(':');
        connectFirestoreEmulator(firestore, host, parseInt(port));
      }

      // Connect to Storage emulator
      if (storageEmulatorHost && !storage._location.bucket.includes('demo-')) {
        const [host, port] = storageEmulatorHost.split(':');
        connectStorageEmulator(storage, host, parseInt(port));
      }

      // Connect to Functions emulator
      if (functionsEmulatorHost) {
        const [host, port] = functionsEmulatorHost.split(':');
        connectFunctionsEmulator(functions, host, parseInt(port));
      }
    }

    return { app, auth, firestore, storage, messaging, functions };
  } catch (error) {
    console.error('Failed to initialize Firebase:', error);
    throw error;
  }
};

// Export initialized services
export const getFirebaseServices = () => {
  if (!app || !auth || !firestore || !storage || !functions) {
    return initializeFirebase();
  }
  return { app, auth, firestore, storage, messaging, functions };
};

// Export individual services for convenience
export const getFirebaseAuth = () => getFirebaseServices().auth;
export const getFirebaseFirestore = () => getFirebaseServices().firestore;
export const getFirebaseStorage = () => getFirebaseServices().storage;
export const getFirebaseApp = () => getFirebaseServices().app;
export const getFirebaseFunctions = () => getFirebaseServices().functions;
export const getFirebaseMessaging = () => getFirebaseServices().messaging;

// Messaging utilities
export const requestNotificationPermission = async (): Promise<string | null> => {
  if (!messaging) return null;
  
  try {
    const permission = await Notification.requestPermission();
    if (permission === 'granted') {
      const token = await getToken(messaging, {
        vapidKey: getEnvVar('FIREBASE_VAPID_KEY')
      });
      return token;
    }
    return null;
  } catch (error) {
    console.error('Error getting notification permission:', error);
    return null;
  }
};

export const onMessageListener = () => {
  if (!messaging) return Promise.reject('Messaging not supported');
  
  return new Promise((resolve) => {
    onMessage(messaging!, (payload) => {
      resolve(payload);
    });
  });
};
