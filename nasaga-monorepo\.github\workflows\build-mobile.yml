name: Build Mobile Apps

on:
  push:
    branches: [main, master]
    paths:
      - 'apps/mobile-agent/**'
      - 'apps/mobile-buyer/**'
      - 'libs/**'
  workflow_dispatch:

env:
  NX_CLOUD_DISTRIBUTED_EXECUTION: false

jobs:
  build:
    name: Build Mobile Apps
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Derive appropriate SHAs for base and head for `nx affected` commands
        uses: nrwl/nx-set-shas@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'

      - name: Set up Expo CLI
        uses: expo/expo-github-action@v8
        with:
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}

      - name: Install dependencies
        run: npm ci --legacy-peer-deps

      - name: Check if mobile-agent is affected
        id: check_mobile_agent
        run: |
          if npx nx show projects --affected --type=app | grep -q "mobile-agent"; then
            echo "affected=true" >> $GITHUB_OUTPUT
          else
            echo "affected=false" >> $GITHUB_OUTPUT
          fi

      - name: Check if mobile-buyer is affected
        id: check_mobile_buyer
        run: |
          if npx nx show projects --affected --type=app | grep -q "mobile-buyer"; then
            echo "affected=true" >> $GITHUB_OUTPUT
          else
            echo "affected=false" >> $GITHUB_OUTPUT
          fi

      - name: Build mobile-agent for preview
        if: steps.check_mobile_agent.outputs.affected == 'true'
        working-directory: apps/mobile-agent
        run: |
          eas build --platform all --profile preview --non-interactive --no-wait
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}

      - name: Build mobile-buyer for preview
        if: steps.check_mobile_buyer.outputs.affected == 'true'
        working-directory: apps/mobile-buyer
        run: |
          eas build --platform all --profile preview --non-interactive --no-wait
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}

  build-production:
    name: Build Mobile Apps (Production)
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'
    environment: production

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Derive appropriate SHAs for base and head for `nx affected` commands
        uses: nrwl/nx-set-shas@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'

      - name: Set up Expo CLI
        uses: expo/expo-github-action@v8
        with:
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}

      - name: Install dependencies
        run: npm ci --legacy-peer-deps

      - name: Build mobile-agent for production
        working-directory: apps/mobile-agent
        run: |
          eas build --platform all --profile production --non-interactive --no-wait
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}

      - name: Build mobile-buyer for production
        working-directory: apps/mobile-buyer
        run: |
          eas build --platform all --profile production --non-interactive --no-wait
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}
