# **Nasaga Real Estate Management Platform Fullstack Architecture Document**

## **Introduction**

This document outlines the complete fullstack architecture for Nasaga Real Estate Management Platform, including backend systems, frontend implementation, and their integration. It serves as the single source of truth for AI-driven development, ensuring consistency across the entire technology stack. This unified approach combines what would traditionally be separate backend and frontend architecture documents, streamlining the development process for modern fullstack applications where these concerns are increasingly intertwined.

### **Starter Template or Existing Project**

**Decision**: N/A \- Greenfield project utilizing **Nx** as the monorepo tool. Nx's generators will be used to scaffold the various applications (Admin Web, Super Admin Web, Agent Mobile, Buyer Mobile, Backend API) and shared libraries within the workspace, as recommended by the "Research Report: Optimal Monorepo Tooling and Setup for Nasaga Real Estate Management Platform."

### **Change Log**

| Date | Version | Description | Author |
| :---- | :---- | :---- | :---- |
| 2025-07-29 | 1.0 | Initial architecture drafting | Winston (Architect) |

## **High Level Architecture**

### **Technical Summary**

The Nasaga Real Estate Management Platform will employ a hybrid full-stack architecture leveraging **Firebase services (Firestore, Authentication, Storage, Cloud Messaging)** for core BaaS functionalities and a **Node.js/Express backend API** for custom business logic and integrations. All applications (Admin Web, Super Admin Web, Agent Mobile, Buyer Mobile, Backend API) will reside within a **monorepo managed by Nx**, enabling extensive code sharing and optimized CI/CD. Frontend applications will be built with **React (web) and React Native with Expo (mobile)**, ensuring a consistent and performant user experience across platforms. This architecture prioritizes scalability, real-time data flow, and maintainability to support growth and efficient development.

### **Platform and Infrastructure Choice**

* **Platform:** Google Cloud Platform (GCP) via Firebase  
* **Key Services:**  
  * Firebase Authentication: For secure, role-based user authentication.  
  * Firebase Firestore: For flexible, scalable, real-time NoSQL database for structured data.  
  * Firebase Storage: For secure and scalable storage of property images and documents.  
  * Firebase Cloud Messaging (FCM): For real-time push notifications.  
  * Google Cloud Functions (Implicit with Firebase functions): For server-side backend logic and custom API endpoints.  
* **Deployment Host and Regions:** Firebase (automatic hosting and scaling), specific regions can be configured within Firebase/GCP settings as needed for data locality and performance, but default Firebase deployment regions will be used initially.

### **Repository Structure**

As established in the PRD and reinforced by the research report, a monorepo managed by Nx is the chosen structure.

* **Structure:** Monorepo  
* **Monorepo Tool:** Nx  
* **Package Organization:** The monorepo will follow a standard Nx structure to maximize code sharing and maintain clear separation of concerns.  
  * apps/: Will contain individual deployable applications.  
    * admin-web/: React web application for Admin dashboard.  
    * super-admin-web/: React web application for Super Admin dashboard.  
    * agent-mobile/: React Native with Expo mobile application for Agents.  
    * buyer-mobile/: React Native with Expo mobile application for Buyers.  
    * backend/: Node.js/Express application for custom API endpoints and server-side logic.  
  * packages/: Will contain shared libraries.  
    * ui/: Reusable UI components compatible with both React and React Native (using react-native-web).  
    * utils/: Shared utility functions (e.g., API clients, formatters, Firebase SDK wrappers).  
    * types/: Shared TypeScript types and interfaces (e.g., User, Property interfaces).

### **High Level Architecture Diagram**

`graph TD`  
    `actor User`  
    `User -- Web Browser --> AdminWeb[Admin Web Dashboard (React/TS)]`  
    `User -- Web Browser --> SuperAdminWeb[Super Admin Web Dashboard (React/TS)]`  
    `User -- Mobile Device --> AgentMobile[Agent Mobile App (RN/Expo)]`  
    `User -- Mobile Device --> BuyerMobile[Buyer Mobile App (RN/Expo)]`

    `AdminWeb --> BackendAPI`  
    `SuperAdminWeb --> BackendAPI`  
    `AgentMobile --> BackendAPI`  
    `BuyerMobile --> BackendAPI`

    `BackendAPI[Backend API (Node.js/Express)] -- Admin SDK --> FirebaseAuth(Firebase Authentication)`  
    `BackendAPI -- Admin SDK --> Firestore(Firebase Firestore DB)`  
    `BackendAPI -- Admin SDK --> FirebaseStorage(Firebase Storage)`  
    `BackendAPI -- Admin SDK --> FirebaseCM(Firebase Cloud Messaging)`

    `FirebaseAuth -- SDK --> AdminWeb`  
    `FirebaseAuth -- SDK --> SuperAdminWeb`  
    `FirebaseAuth -- SDK --> AgentMobile`  
    `FirebaseAuth -- SDK --> BuyerMobile`

    `Firestore -- SDK --> AgentMobile`  
    `Firestore -- SDK --> BuyerMobile`  
    `FirebaseStorage -- SDK --> AgentMobile`

    `FirebaseCM -- SDK --> AgentMobile`  
    `FirebaseCM -- SDK --> BuyerMobile`

    `subgraph Firebase Ecosystem`  
        `FirebaseAuth`  
        `Firestore`  
        `FirebaseStorage`  
        `FirebaseCM`  
    `end`

    `subgraph Nasaga Platform Applications`  
        `AdminWeb`  
        `SuperAdminWeb`  
        `AgentMobile`  
        `BuyerMobile`  
        `BackendAPI`  
    `end`

    `NasagaPlatformApplications --> FirebaseEcosystem`

### **Architectural Patterns**

Here are the key high-level patterns that will guide the architecture. For each, I'm providing a recommendation with rationale, aligning with your PRD and the monorepo tooling research.

1. **Overall Architectural Style:** **Hybrid Serverless & Backend-for-Frontend (BFF)**  
   * *Rationale:* Leveraging Firebase services (Authentication, Firestore, Storage, Cloud Messaging) provides a serverless backend-as-a-service foundation, minimizing operational overhead and ensuring scalability. The Node.js/Express API will act as a Backend-for-Frontend (BFF) layer where needed, encapsulating complex logic, performing data transformations, and integrating with Firebase Admin SDK for privileged operations, tailored to the specific needs of the web dashboards and mobile apps. This aligns with the PRD's "hybrid approach" for service architecture.  
2. **Code Organization Pattern:** **Monorepo with Domain/Feature Slicing**  
   * *Rationale:* As specified in the PRD, an Nx monorepo will house all applications and shared libraries. Within this, code will be organized by domain or feature (e.g., users, properties, auth) using Nx's library system to enforce modularity, promote code sharing, and enable clear ownership. This supports maintainability and developer experience.  
3. **Data Persistence Pattern:** **Document-Oriented Database (Firebase Firestore)**  
   * *Rationale:* Firebase Firestore is chosen for its real-time synchronization capabilities, scalability, and seamless integration with Firebase services. Its document-oriented nature is flexible for evolving property and user data structures.  
4. **Communication Patterns:** **Real-time Database Sync (Firestore) & RESTful API**  
   * *Rationale:* For real-time updates (e.g., property status changes, notifications), direct integration with Firestore's real-time capabilities will be utilized where appropriate. For custom business logic, secure operations (e.g., user role management by admins), and complex queries not suitable for direct client-to-Firestore access, a **RESTful API** exposed by the Node.js/Express backend will serve as the primary communication method.

## **Tech Stack**

This is the DEFINITIVE technology selection section. All development must use these exact versions.

### **Cloud Infrastructure**

* **Provider:** Google Cloud Platform (GCP) via Firebase  
* **Key Services:** Firebase Authentication, Firebase Firestore, Firebase Storage, Firebase Cloud Messaging (FCM), Google Cloud Functions  
* **Deployment Host and Regions:** Firebase (automatic hosting and scaling), specific regions can be configured.

### **Technology Stack Table**

| Category | Technology | Version | Purpose | Rationale |
| :---- | :---- | :---- | :---- | :---- |
| Frontend Language | TypeScript | Latest Stable (e.g., 5.x) | Primary development language for type safety and maintainability. | Modern, robust for building complex user interfaces, type safety for maintainability. |
| Frontend Framework (Web) | React | Latest Stable (e.g., 18.x) | Core library for building Admin and Super Admin web dashboards. | Modern, robust for building complex user interfaces, type safety for maintainability. |
| UI Component Library (Web) | N/A (to be decided, possibly Tailwind CSS or Material-UI) | N/A | To accelerate UI development and ensure consistency for web apps. | To be selected based on design preference and developer experience. |
| State Management (Web) | React Context API / Zustand (or similar lightweight) | N/A | For managing local and global state in web applications. | React Context for simpler global state, lightweight libraries for more complex scenarios. |
| Frontend Framework (Mobile) | React Native with Expo | Latest Stable SDK (e.g., 50.x) | For building cross-platform Agent and Buyer mobile apps from a single codebase. | Enables cross-platform mobile development from a single codebase, leveraging existing React knowledge. Expo simplifies the native build process. |
| UI Component Library (Mobile) | N/A (to be decided, Expo components, NativeBase, UI Kitten) | N/A | For building native-like UI components for mobile apps. | To be selected based on native UI needs and developer experience. |
| State Management (Mobile) | React Context API / Zustand (or similar lightweight) | N/A | For managing local and global state in mobile applications. | React Context for simpler global state, lightweight libraries for more complex scenarios. |
| Backend Language | TypeScript | Latest Stable (e.g., 5.x) | Primary development language for backend logic. | Consistency with frontend, strong typing. |
| Backend Runtime | Node.js | Latest LTS (e.g., 20.x) | JavaScript runtime for the backend API. | Popular, performant, and integrates well with Firebase Admin SDK for server-side logic and custom API endpoints. |
| Backend Framework | Express | Latest Stable (e.g., 4.x) | Minimalist web framework for backend API. | Popular, performant, and integrates well with Firebase Admin SDK for server-side logic and custom API endpoints. |
| API Style | REST | 1.0 | Standard API communication for custom backend logic not directly handled by Firebase SDKs. | Widely adopted, flexible for various client applications. |
| Database | Firebase Firestore | N/A | Scalable, real-time NoSQL database for structured data. | Offers real-time synchronization and scales with demand, crucial for live property updates. |
| Cache | N/A (Firestore caching / application-level) | N/A | Firestore has offline persistence. Application-level caching can be implemented for specific needs. | Firestore provides some built-in caching; specific caching layers will be implemented as performance needs arise. |
| File Storage | Firebase Storage | N/A | Secure and scalable file management for property images and documents. | Provides secure and scalable file management for property media. |
| Authentication | Firebase Authentication | N/A | Secure, role-based user authentication for all user types. | Providing seamless onboarding and secure, role-based access. |
| Frontend Testing | Jest / React Testing Library | Latest Stable | Unit and integration testing for React web and React Native components. | Standard for React ecosystems, provides robust testing utilities. |
| Backend Testing | Jest / Supertest | Latest Stable | Unit and integration testing for Node.js/Express backend. | Standard for Node.js testing, Supertest for API integration. |
| E2E Testing | Cypress / Playwright (with Nx integration) | Latest Stable | End-to-End testing for critical user flows across web and mobile. | Comprehensive testing strategy for secure, scalable, and maintainable codebase. |
| Build Tool | Nx | Latest Stable (e.g., 18.x) | Monorepo management, build optimization, task execution. | Comprehensive support for React, React Native with Expo, and Node.js/Express; computation caching, distributed task execution, code generation. |
| Bundler | Webpack (for web) / Metro (for React Native) | N/A | Bundling JavaScript applications. | Standard tools for React/React Native ecosystems. |
| IaC Tool | N/A (Firebase CLI / Google Cloud CLI for manual setup, or Terraform if more complex infrastructure is needed later) | N/A | For managing cloud resources. | Initial setup via Firebase CLI; scalable IaC solution will be introduced if non-Firebase GCP resources become significant. |
| CI/CD | GitHub Actions (with Nx Cloud integration) | N/A | Automated build, test, and deployment pipelines. | Nx's affected commands and distributed task execution optimize CI/CD. |
| Monitoring | Firebase Performance Monitoring, Google Cloud Monitoring | N/A | For application performance and infrastructure health. | Integrated with Firebase for mobile and web performance, GCP for broader service monitoring. |
| Logging | Google Cloud Logging (with Firebase Logging) | N/A | Centralized logging for all application components. | Integrated with GCP ecosystem for comprehensive log management. |
| CSS Framework | Tailwind CSS | Latest Stable | Utility-first CSS framework for rapid and consistent styling. | Highly customizable, efficient for responsive design. |

## **Data Models**

This section defines the core data models/entities that will be shared between frontend and backend. We'll outline each model's purpose, key attributes, and relationships, and provide a TypeScript interface for consistency across the full stack.

### **User**

**Purpose:** Represents a user within the Nasaga Real Estate Management Platform. This model will store user authentication details (linked to Firebase Auth UID), role, and basic profile information.  
**Key Attributes:**

* uid: string \- Unique Firebase Authentication user ID.  
* email: string \- User's email address.  
* displayName: string \- User's display name.  
* role: UserRole \- User's assigned role (Super Admin, Admin, Agent, Buyer).  
* phoneNumber: string (optional) \- User's phone number.  
* profilePictureUrl: string (optional) \- URL to user's profile picture in Firebase Storage.  
* createdAt: Timestamp \- Timestamp of user creation.  
* updatedAt: Timestamp \- Timestamp of last update.

**TypeScript Interface**  
`// packages/types/src/user.d.ts`  
`export type UserRole = 'superAdmin' | 'admin' | 'agent' | 'buyer';`

`export interface User {`  
  `uid: string;`  
  `email: string;`  
  `displayName: string;`  
  `role: UserRole;`  
  `phoneNumber?: string;`  
  `profilePictureUrl?: string;`  
  `createdAt: firebase.firestore.Timestamp;`  
  `updatedAt: firebase.firestore.Timestamp;`  
`}`

**Relationships:**

* User (Agent role) can create multiple Property listings.  
* User (Buyer role) can favorite multiple Property listings.  
* User (Admin/Super Admin roles) manages User accounts and Property listings.

### **Property**

**Purpose:** Represents a single property listing available on the Nasaga Real Estate Management Platform. This model will store all relevant details about a property, its current status, and a reference to the agent who listed it.  
**Key Attributes:**

* propertyId: string \- Unique identifier for the property.  
* title: string \- Short, descriptive title of the property (e.g., "Spacious Family Home in Lekki").  
* description: string \- Detailed description of the property.  
* address: string \- Full street address of the property.  
* city: string \- City where the property is located.  
* state: string \- State where the property is located.  
* zipCode: string \- Postal code of the property.  
* price: number \- Asking price of the property.  
* propertyType: string \- Type of property (e.g., "House", "Apartment", "Land", "Commercial").  
* bedrooms: number \- Number of bedrooms.  
* bathrooms: number \- Number of bathrooms.  
* squareFootage: number (optional) \- Total area in square feet/meters.  
* agentUid: string \- Firebase UID of the agent who listed the property.  
* status: PropertyStatus \- Current status of the property (e.g., "pending", "approved", "active", "under offer", "sold", "delisted").  
* imageUrls: string\[\] \- Array of URLs to property images stored in Firebase Storage.  
* documentUrls: string\[\] (optional) \- Array of URLs to associated documents (e.g., floor plans) in Firebase Storage.  
* createdAt: Timestamp \- Timestamp of property listing creation.  
* updatedAt: Timestamp \- Timestamp of last update.  
* approvedByAdminUid: string (optional) \- Firebase UID of the Admin who approved the listing.  
* approvedAt: Timestamp (optional) \- Timestamp of approval.

**TypeScript Interface**  
`// packages/types/src/property.d.ts`  
`export type PropertyStatus = 'pending' | 'approved' | 'active' | 'under offer' | 'sold' | 'delisted';`

`export interface Property {`  
  `propertyId: string;`  
  `title: string;`  
  `description: string;`  
  `address: string;`  
  `city: string;`  
  `state: string;`  
  `zipCode: string;`  
  `price: number;`  
  `propertyType: string;`  
  `bedrooms: number;`  
  `bathrooms: number;`  
  `squareFootage?: number;`  
  `agentUid: string; // Foreign key to User.uid`  
  `status: PropertyStatus;`  
  `imageUrls: string[];`  
  `documentUrls?: string[];`  
  `createdAt: firebase.firestore.Timestamp;`  
  `updatedAt: firebase.firestore.Timestamp;`  
  `approvedByAdminUid?: string; // Foreign key to User.uid`  
  `approvedAt?: firebase.firestore.Timestamp;`  
`}`

**Relationships:**

* Property is listed by one User (Agent).  
* Property can be favorited by multiple User (Buyer) accounts.  
* Property is managed (approved/rejected) by one User (Admin/Super Admin).

## **API Specification**

Based on the chosen API style from the Tech Stack (REST), I will now generate a preliminary OpenAPI 3.0 specification for the backend. This specification will outline the key endpoints, define request/response schemas based on the data models, and document authentication requirements.

### **REST API Specification**

`openapi: 3.0.0`  
`info:`  
  `title: Nasaga Real Estate Management Platform API`  
  `version: 1.0.0`  
  `description: API for custom backend logic and privileged operations not directly handled by Firebase SDKs, integrating with Firebase Firestore and Admin SDK.`  
`servers:`  
  `- url: https://api.nasaga.com/v1`  
    `description: Production API server`  
`paths:`  
  `/auth/register:`  
    `post:`  
      `summary: Register a new user`  
      `requestBody:`  
        `required: true`  
        `content:`  
          `application/json:`  
            `schema:`  
              `type: object`  
              `properties:`  
                `email:`  
                  `type: string`  
                  `format: email`  
                `password:`  
                  `type: string`  
                  `minLength: 6`  
                `displayName:`  
                  `type: string`  
                `role:`  
                  `type: string`  
                  `enum: [ 'agent', 'buyer' ] # Only agents and buyers can self-register`  
      `responses:`  
        `'201':`  
          `description: User registered successfully`  
          `content:`  
            `application/json:`  
              `schema:`  
                `type: object`  
                `properties:`  
                  `uid:`  
                    `type: string`  
                  `email:`  
                    `type: string`  
                  `role:`  
                    `type: string`  
        `'400':`  
          `description: Invalid input`  
        `'409':`  
          `description: User already exists`  
  `/properties:`  
    `post:`  
      `summary: Create a new property listing`  
      `security:`  
        `- firebaseAuth: []`  
      `requestBody:`  
        `required: true`  
        `content:`  
          `application/json:`  
            `schema:`  
              `type: object`  
              `properties:`  
                `title:`  
                  `type: string`  
                `description:`  
                  `type: string`  
                `address:`  
                  `type: string`  
                `city:`  
                  `type: string`  
                `state:`  
                  `type: string`  
                `zipCode:`  
                  `type: string`  
                `price:`  
                  `type: number`  
                  `format: float`  
                `propertyType:`  
                  `type: string`  
                `bedrooms:`  
                  `type: integer`  
                `bathrooms:`  
                  `type: integer`  
                `squareFootage:`  
                  `type: number`  
                  `format: float`  
                `imageUrls:`  
                  `type: array`  
                  `items:`  
                    `type: string`  
                  `description: URLs to images in Firebase Storage`  
                `documentUrls:`  
                  `type: array`  
                  `items:`  
                    `type: string`  
                  `description: URLs to documents in Firebase Storage`  
      `responses:`  
        `'201':`  
          `description: Property created successfully (status pending)`  
        `'401':`  
          `description: Unauthorized (only agents can create)`  
        `'400':`  
          `description: Invalid input`  
  `/properties/{propertyId}/status:`  
    `patch:`  
      `summary: Update property status (Admin/Super Admin)`  
      `parameters:`  
        `- in: path`  
          `name: propertyId`  
          `required: true`  
          `schema:`  
            `type: string`  
          `description: The ID of the property to update`  
      `security:`  
        `- firebaseAuth: []`  
      `requestBody:`  
        `required: true`  
        `content:`  
          `application/json:`  
            `schema:`  
              `type: object`  
              `properties:`  
                `status:`  
                  `type: string`  
                  `enum: [ 'approved', 'rejected', 'active', 'under offer', 'sold', 'delisted' ]`  
      `responses:`  
        `'200':`  
          `description: Property status updated`  
        `'401':`  
          `description: Unauthorized`  
        `'403':`  
          `description: Forbidden (only admin/superAdmin)`  
        `'404':`  
          `description: Property not found`  
  `/admin/users/{uid}/role:`  
    `patch:`  
      `summary: Update user role (Super Admin only)`  
      `parameters:`  
        `- in: path`  
          `name: uid`  
          `required: true`  
          `schema:`  
            `type: string`  
          `description: The UID of the user to update`  
      `security:`  
        `- firebaseAuth: []`  
      `requestBody:`  
        `required: true`  
        `content:`  
          `application/json:`  
            `schema:`  
              `type: object`  
              `properties:`  
                `role:`  
                  `type: string`  
                  `enum: [ 'admin', 'agent', 'buyer', 'superAdmin' ] # Super admin can assign any role`  
      `responses:`  
        `'200':`  
          `description: User role updated`  
        `'401':`  
          `description: Unauthorized`  
        `'403':`  
          `description: Forbidden (only superAdmin)`  
        `'404':`  
          `description: User not found`  
`components:`  
  `securitySchemes:`  
    `firebaseAuth:`  
      `type: http`  
      `scheme: bearer`  
      `bearerFormat: Firebase`  
      `description: Firebase ID Token for authentication.`

## **Components**

### **Auth Service (Backend)**

* **Responsibility:** Handles all server-side authentication logic, including user registration, login (if not fully client-side via Firebase SDK), and token verification. It will interact with Firebase Authentication Admin SDK for privileged user management operations (e.g., setting custom claims for roles).  
* **Key Interfaces:**  
  * POST /auth/register: (as defined in API Spec) for new user registration.  
  * Middleware: For verifying Firebase ID tokens on protected routes.  
* **Dependencies:** Firebase Authentication Admin SDK, Firebase Firestore (for storing user roles).  
* **Technology Stack:** Node.js, Express, Firebase Admin SDK.

### **Property Management Service (Backend)**

* **Responsibility:** Manages all server-side operations related to property listings, including creation, retrieval (for filtered/searched lists), updates, and status changes. It will perform validation, interact with Firestore for data persistence, and integrate with Firebase Storage for media management. It will also be responsible for triggering notifications via FCM for status changes.  
* **Key Interfaces:**  
  * POST /properties: (as defined in API Spec) for agents to create new listings.  
  * PATCH /properties/{propertyId}/status: (as defined in API Spec) for Admins to approve/reject property status.  
  * Internal methods for retrieving filtered/paginated property lists.  
* **Dependencies:** Firebase Firestore, Firebase Storage Admin SDK, Firebase Cloud Messaging Admin SDK.  
* **Technology Stack:** Node.js, Express, Firebase Admin SDK.

### **Admin & Super Admin Web Dashboards (Frontend)**

* **Responsibility:** Provides the secure web-based user interface for Admin and Super Admin roles. This includes user account management, property listing approval/rejection workflows, and platform analytics display. It will interact with the Backend API for privileged operations and directly with Firebase SDKs for authentication and real-time data display.  
* **Key Interfaces:**  
  * Login pages for role-based access.  
  * User management interfaces.  
  * Property listing review and action interfaces.  
  * Integration with Backend API endpoints (e.g., /admin/users/{uid}/role, /properties/{propertyId}/status).  
  * Direct consumption of Firebase Firestore data for real-time listing views.  
* **Dependencies:** React, TypeScript, React Router, UI Component Library (to be decided, e.g., Material-UI or Ant Design), Firebase SDKs (Auth, Firestore), Backend API client (generated from OpenAPI spec or custom).  
* **Technology Stack:** React, TypeScript, Nx.

### **Agent Mobile App (Frontend)**

* **Responsibility:** Provides the secure mobile application interface for real estate agents. This includes property listing creation, editing, viewing, delisting, media uploads, and basic profile management. It will primarily interact with Firebase SDKs for authentication, Firestore data synchronization, and Storage for media. It will also receive push notifications via FCM for buyer inquiries.  
* **Key Interfaces:**  
  * Secure login and registration flow.  
  * Property listing forms (create/edit).  
  * Media upload interfaces.  
  * Dashboard/list view of agent's properties.  
  * Profile management screens.  
  * Receiving and displaying push notifications.  
* **Dependencies:** React Native, Expo, TypeScript, React Navigation, UI Component Library (to be decided, e.g., NativeBase, UI Kitten), Firebase SDKs (Auth, Firestore, Storage, FCM).  
* **Technology Stack:** React Native, Expo, TypeScript, Nx.

### **Buyer Mobile App (Frontend)**

* **Responsibility:** Provides the secure mobile application interface for prospective buyers. This includes browsing, searching, filtering, viewing detailed property information, favoriting properties, and receiving push notifications for updates on favorited listings. It will primarily interact with Firebase SDKs for authentication and Firestore data synchronization.  
* **Key Interfaces:**  
  * Secure login and registration flow.  
  * Property browsing and search interface.  
  * Filter options for property listings.  
  * Property detail pages.  
  * Favorited properties list.  
  * Receiving and displaying push notifications.  
* **Dependencies:** React Native, Expo, TypeScript, React Navigation, UI Component Library (to be decided, e.g., NativeBase, UI Kitten), Firebase SDKs (Auth, Firestore, FCM).  
* **Technology Stack:** React Native, Expo, TypeScript, Nx.

### **Component Diagrams**

`graph TD`  
    `subgraph Client Applications`  
        `AdminWeb[Admin Web (React)]`  
        `SuperAdminWeb[Super Admin Web (React)]`  
        `AgentMobile[Agent App (RN/Expo)]`  
        `BuyerMobile[Buyer App (RN/Expo)]`  
    `end`

    `subgraph Backend Services`  
        `BackendAPI[Node.js/Express API]`  
        `FirebaseAuth(Firebase Auth)`  
        `Firestore(Firebase Firestore)`  
        `FirebaseStorage(Firebase Storage)`  
        `FirebaseCM(Firebase Cloud Messaging)`  
    `end`

    `AdminWeb -- REST API --> BackendAPI`  
    `SuperAdminWeb -- REST API --> BackendAPI`  
    `AgentMobile -- REST API / Direct SDK --> BackendAPI`  
    `BuyerMobile -- REST API / Direct SDK --> BackendAPI`

    `AgentMobile -- Firebase SDKs --> FirebaseAuth`  
    `AgentMobile -- Firebase SDKs --> Firestore`  
    `AgentMobile -- Firebase SDKs --> FirebaseStorage`  
    `AgentMobile -- Firebase SDKs --> FirebaseCM`

    `BuyerMobile -- Firebase SDKs --> FirebaseAuth`  
    `BuyerMobile -- Firebase SDKs --> Firestore`  
    `BuyerMobile -- Firebase SDKs --> FirebaseCM`

    `BackendAPI -- Firebase Admin SDK --> FirebaseAuth`  
    `BackendAPI -- Firebase Admin SDK --> Firestore`  
    `BackendAPI -- Firebase Admin SDK --> FirebaseStorage`  
    `BackendAPI -- Firebase Admin SDK --> FirebaseCM`

    `style AdminWeb fill:#F0F8FF,stroke:#333,stroke-width:2px`  
    `style SuperAdminWeb fill:#F0F8FF,stroke:#333,stroke-width:2px`  
    `style AgentMobile fill:#E0FFFF,stroke:#333,stroke-width:2px`  
    `style BuyerMobile fill:#E0FFFF,stroke:#333,stroke-width:2px`  
    `style BackendAPI fill:#FFF0F5,stroke:#333,stroke-width:2px`  
    `style FirebaseAuth fill:#FFD700,stroke:#333,stroke-width:2px`  
    `style Firestore fill:#ADD8E6,stroke:#333,stroke-width:2px`  
    `style FirebaseStorage fill:#DAA520,stroke:#333,stroke-width:2px`  
    `style FirebaseCM fill:#FFB6C1,stroke:#333,stroke-width:2px`

## **External APIs**

At this time, no additional external API integrations are required beyond the core Firebase services (Authentication, Firestore, Storage, Cloud Messaging) which are integral to the platform's architecture and are directly handled by Firebase SDKs and the Firebase Admin SDK on the backend.  
Should future requirements necessitate integration with third-party services (e.g., mapping services, payment gateways beyond basic Firebase-supported integrations, external analytics platforms), they will be documented here.

## **Core Workflows**

### **User Registration and Role Assignment Workflow**

`sequenceDiagram`  
    `actor User`  
    `participant ClientApp[Client App (Web/Mobile)]`  
    `participant FirebaseAuthClient[Firebase Auth SDK (Client)]`  
    `participant BackendAPI[Backend API (Node.js/Express)]`  
    `participant FirebaseAuthAdmin[Firebase Auth Admin SDK (Backend)]`  
    `participant Firestore[Firebase Firestore]`

    `User->>ClientApp: 1. Initiates Registration (email, password, role)`  
    `ClientApp->>FirebaseAuthClient: 2. Calls createUserWithEmailAndPassword()`  
    `FirebaseAuthClient-->>FirebaseAuthClient: 3. Handles user creation internally`  
    `FirebaseAuthClient-->>ClientApp: 4. Returns user credentials (uid, idToken)`  
    `ClientApp->>BackendAPI: 5. Sends POST /auth/register with idToken and desired role (e.g., 'agent', 'buyer')`  
    `Note over ClientApp,BackendAPI: Client-side registration is limited to 'agent'/'buyer' roles.`  
    `BackendAPI->>FirebaseAuthAdmin: 6. Verifies idToken & extracts UID`  
    `FirebaseAuthAdmin-->>BackendAPI: 7. Token Validated (returns decoded token)`  
    `BackendAPI->>Firestore: 8. Creates new User document in 'users' collection with UID and role`  
    `Firestore-->>BackendAPI: 9. User document created`  
    `BackendAPI-->>ClientApp: 10. Returns success response`  
    `ClientApp->>User: 11. Registration successful, User logged in.`

### **Property Listing Creation & Approval Workflow**

`sequenceDiagram`  
    `actor Agent`  
    `participant AgentMobileApp[Agent Mobile App (RN/Expo)]`  
    `participant FirebaseSDKs[Firebase SDKs (Client)]`  
    `participant BackendAPI[Backend API (Node.js/Express)]`  
    `participant FirebaseAuthAdmin[Firebase Auth Admin SDK (Backend)]`  
    `participant Firestore[Firebase Firestore]`  
    `participant FirebaseStorage[Firebase Storage]`  
    `actor Admin`  
    `participant AdminWebDashboard[Admin Web Dashboard (React)]`

    `Agent->>AgentMobileApp: 1. Initiates 'Create New Property'`  
    `AgentMobileApp->>Agent: 2. Presents Property Form`  
    `Agent->>AgentMobileApp: 3. Enters property details & selects images`  
    `AgentMobileApp->>FirebaseSDKs: 4. Uploads images to Firebase Storage`  
    `FirebaseSDKs-->>FirebaseStorage: 5. Stores images`  
    `FirebaseStorage-->>FirebaseSDKs: 6. Returns image URLs`  
    `AgentMobileApp->>FirebaseSDKs: 7. Saves property data (including image URLs) to Firestore 'properties' collection with status 'pending'`  
    `FirebaseSDKs-->>Firestore: 8. Writes property document`  
    `Firestore-->>FirebaseSDKs: 9. Property document created`  
    `AgentMobileApp-->>Agent: 10. Confirmation: Property submitted for review`

    `Note over Admin,AdminWebDashboard: Admin logs in and views pending listings`  
    `AdminWebDashboard->>Firestore: 11. Real-time query for properties with status 'pending'`  
    `Firestore-->>AdminWebDashboard: 12. Streams pending properties list`  
    `Admin->>AdminWebDashboard: 13. Selects property for review & changes status to 'approved' or 'rejected'`  
    `AdminWebDashboard->>BackendAPI: 14. Calls PATCH /properties/{propertyId}/status (with Admin Firebase ID Token)`  
    `BackendAPI->>FirebaseAuthAdmin: 15. Verifies Admin's ID Token & Role`  
    `FirebaseAuthAdmin-->>BackendAPI: 16. Token Validated (returns decoded token)`  
    `BackendAPI->>Firestore: 17. Updates property document status & adds approvedByAdminUid, approvedAt`  
    `Firestore-->>BackendAPI: 18. Property status updated`  
    `BackendAPI-->>AdminWebDashboard: 19. Returns success response`  
    `AdminWebDashboard-->>Admin: 20. Confirmation: Property status updated`

    `Note over Firestore,AgentMobileApp: Real-time update propagates to Buyer App and Agent's own view`  
    `Firestore->>BuyerMobileApp: 21. Real-time update to Buyer App for favorited properties`  
    `Firestore->>AgentMobileApp: 22. Real-time update to Agent App for their own property status`

## **Database Schema**

This section translates our conceptual data models into concrete database schemas, specifically for Firebase Firestore. It outlines the collection structures, document examples, and how relationships are managed in a NoSQL environment.

### **Firestore Collections Overview**

Our primary data will be organized into the following top-level Firestore collections:

* **users**: Stores user profiles and role information.  
* **properties**: Stores all property listings.

### **Document Structures**

#### **users Collection**

* **Collection Path**: users/{uid}  
* **Purpose**: Each document in this collection represents a single user, indexed by their Firebase Authentication UID. It stores their core profile information and, critically, their assigned role.  
* **Example Document (users/someFirebaseUid123)**:  
  `{`  
    `"uid": "someFirebaseUid123",`  
    `"email": "<EMAIL>",`  
    `"displayName": "John Doe",`  
    `"role": "agent",`  
    `"phoneNumber": "+2348012345678",`  
    `"profilePictureUrl": "https://firebasestorage.googleapis.com/v0/b/nasaga.appspot.com/o/profile_pictures%2Fjohn_doe.jpg",`  
    `"createdAt": "2025-07-29T10:00:00Z",`  
    `"updatedAt": "2025-07-29T15:30:00Z"`  
  `}`

* **Indexing Considerations**:  
  * An index on role would be beneficial for queries filtering users by their role (e.g., "get all agents").  
  * Compound indexes might be needed for combined queries (e.g., "get users by role and last updated date").

#### **properties Collection**

* **Collection Path**: properties/{propertyId}  
* **Purpose**: Each document represents a single property listing. propertyId will typically be the Firestore auto-generated document ID for simplicity, or a custom unique ID if required.  
* **Example Document (properties/autoGeneratedPropertyId456)**:  
  `{`  
    `"propertyId": "autoGeneratedPropertyId456",`  
    `"title": "Spacious 4-Bedroom Family Home",`  
    `"description": "Beautiful home with a large garden...",`  
    `"address": "123 Main St",`  
    `"city": "Lekki",`  
    `"state": "Lagos",`  
    `"zipCode": "101233",`  
    `"price": 75000000,`  
    `"propertyType": "House",`  
    `"bedrooms": 4,`  
    `"bathrooms": 3,`  
    `"squareFootage": 2500,`  
    `"agentUid": "someFirebaseUid123", # Refers to a user in the 'users' collection with role 'agent'`  
    `"status": "active",`  
    `"imageUrls": [`  
      `"https://firebasestorage.googleapis.com/v0/b/nasaga.appspot.com/o/properties%2Fimg1.jpg",`  
      `"https://firebasestorage.googleapis.com/v0/b/nasaga.appspot.com/o/properties%2Fimg2.jpg"`  
    `],`  
    `"documentUrls": [],`  
    `"createdAt": "2025-07-29T11:00:00Z",`  
    `"updatedAt": "2025-07-29T16:00:00Z",`  
    `"approvedByAdminUid": "adminFirebaseUid789", # Refers to a user in the 'users' collection with role 'admin'`  
    `"approvedAt": "2025-07-29T15:00:00Z"`  
  `}`

* **Indexing Considerations**:  
  * Indexes on status, agentUid, propertyType, price (range queries), city, and state will be crucial for efficient searching and filtering of properties.  
  * Compound indexes will be needed for common combined queries (e.g., "properties by city and status").

### **Relationships in NoSQL (Denormalization & References)**

In Firestore, relationships are primarily handled through references (storing the ID of related documents) and selective denormalization (duplicating essential related data to avoid extra reads).

* **User-Property Relationship**: The properties collection stores agentUid and approvedByAdminUid. These fields are direct references to documents in the users collection. When displaying property details, a client-side or backend lookup can fetch the associated agent/admin's displayName or other required details. For frequently displayed agent names (e.g., on a property card list), some level of denormalization (e.g., also storing agentDisplayName directly in the property document) could be considered to reduce reads, but for this MVP, direct referencing is sufficient.  
* **Favorited Properties**: Each User (Buyer) document could have a subcollection (e.g., users/{buyerUid}/favoritedProperties) storing references or simplified copies of favorited property IDs. This allows quick retrieval of a buyer's favorites without scanning all properties.

### **Security Rules Considerations**

It's critical to enforce data access and modification rules directly in Firebase Firestore Security Rules, complementing any backend API validations.

* **users collection**:  
  * Only authenticated users can read their own profile.  
  * Only superAdmin or admin roles can create or modify users documents (especially role).  
* **properties collection**:  
  * Only agent roles can create property documents.  
  * Only the agentUid associated with a property or admin/superAdmin roles can modify a property document.  
  * Only admin/superAdmin roles can change the status of a property.  
  * All authenticated users (buyer, agent, admin, superAdmin) can read approved or active properties.

## **Frontend Architecture**

### **Component Architecture**

#### **Component Organization**

`apps/`  
`├── admin-web/`  
`│   └── src/`  
`│       ├── components/     # Application-specific UI components`  
`│       ├── pages/          # Page-level components/routes`  
`│       ├── layouts/        # Common layouts (e.g., dashboard layout)`  
`│       ├── services/       # API client services specific to admin-web`  
`│       ├── contexts/       # React Context providers for local state`  
`│       └── utils/          # Admin-web specific utilities`  
`├── super-admin-web/`  
`│   └── src/`  
`│       ├── components/     # Application-specific UI components`  
`│       ├── pages/          # Page-level components/routes`  
`│       ├── layouts/        # Common layouts`  
`│       ├── services/       # API client services specific to super-admin-web`  
`│       ├── contexts/       # React Context providers`  
`│       └── utils/          # Super-admin-web specific utilities`  
`├── agent-mobile/`  
`│   └── src/`  
`│       ├── components/     # Application-specific UI components`  
`│       ├── screens/        # Screen-level components/routes`  
`│       ├── navigation/     # React Navigation setup`  
`│       ├── services/       # API client services specific to agent-mobile`  
`│       ├── hooks/          # Custom React Native hooks`  
`│       ├── contexts/       # React Context providers`  
`│       └── utils/          # Agent-mobile specific utilities`  
`├── buyer-mobile/`  
`│   └── src/`  
`│       ├── components/     # Application-specific UI components`  
`│       ├── screens/        # Screen-level components/routes`  
`│       ├── navigation/     # React Navigation setup`  
`│       ├── services/       # API client services specific to buyer-mobile`  
`│       ├── hooks/          # Custom React Native hooks`  
`│       ├── contexts/       # React Context providers`  
`│       └── utils/          # Buyer-mobile specific utilities`  
`packages/`  
`└── ui/                     # Shared UI components`  
    `└── src/`  
        `├── common/         # Generic UI components (buttons, inputs)`  
        `├── forms/          # Reusable form components`  
        `├── navigation/     # Shared navigation elements`  
        `├── layouts/        # Shared layout patterns`  
        `└── theme/          # Shared styling variables/theme setup`

#### **Component Template**

`// For React Web components (e.g., packages/ui/src/common/Button/Button.tsx)`  
`import React from 'react';`

`interface ButtonProps {`  
  `children: React.ReactNode;`  
  `onClick: () => void;`  
  `variant?: 'primary' | 'secondary' | 'outline';`  
  `disabled?: boolean;`  
`}`

`const Button: React.FC<ButtonProps> = ({ children, onClick, variant = 'primary', disabled = false }) => {`  
  `const baseStyles = 'px-4 py-2 rounded-md font-semibold focus:outline-none focus:ring-2 focus:ring-opacity-75';`  
  `let variantStyles = '';`

  `switch (variant) {`  
    `case 'primary':`  
      `variantStyles = 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500';`  
      `break;`  
    `case 'secondary':`  
      `variantStyles = 'bg-gray-200 text-gray-800 hover:bg-gray-300 focus:ring-gray-400';`  
      `break;`  
    `case 'outline':`  
      `variantStyles = 'border border-blue-600 text-blue-600 hover:bg-blue-50 focus:ring-blue-500';`  
      `break;`  
  `}`

  `return (`  
    `<button`  
      ``className={`${baseStyles} ${variantStyles} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}``  
      `onClick={onClick}`  
      `disabled={disabled}`  
    `>`  
      `{children}`  
    `</button>`  
  `);`  
`};`

`export default Button;`

`// For React Native components (e.g., packages/ui/src/common/Button/Button.native.tsx)`  
`import React from 'react';`  
`import { TouchableOpacity, Text, StyleSheet, ViewStyle, TextStyle } from 'react-native';`

`interface ButtonProps {`  
  `children: string; // Text content for native button`  
  `onPress: () => void;`  
  `variant?: 'primary' | 'secondary' | 'outline';`  
  `disabled?: boolean;`  
  `style?: ViewStyle;`  
  `textStyle?: TextStyle;`  
`}`

`const Button: React.FC<ButtonProps> = ({ children, onPress, variant = 'primary', disabled = false, style, textStyle }) => {`  
  `const getVariantStyles = (): ViewStyle => {`  
    `switch (variant) {`  
      `case 'primary': return styles.primaryButton;`  
      `case 'secondary': return styles.secondaryButton;`  
      `case 'outline': return styles.outlineButton;`  
      `default: return styles.primaryButton;`  
    `}`  
  `};`

  `const getTextVariantStyles = (): TextStyle => {`  
    `switch (variant) {`  
      `case 'primary': return styles.primaryText;`  
      `case 'secondary': return styles.secondaryText;`  
      `case 'outline': return styles.outlineText;`  
      `default: return styles.primaryText;`  
    `}`  
  `};`

  `return (`  
    `<TouchableOpacity`  
      `style={[styles.baseButton, getVariantStyles(), disabled && styles.disabledButton, style]}`  
      `onPress={onPress}`  
      `disabled={disabled}`  
    `>`  
      `<Text style={[styles.baseText, getTextVariantStyles(), textStyle]}>{children}</Text>`  
    `</TouchableOpacity>`  
  `);`  
`};`

`const styles = StyleSheet.create({`  
  `baseButton: {`  
    `paddingVertical: 12,`  
    `paddingHorizontal: 20,`  
    `borderRadius: 8,`  
    `alignItems: 'center',`  
    `justifyContent: 'center',`  
  `},`  
  `baseText: {`  
    `fontWeight: '600',`  
  `},`  
  `primaryButton: {`  
    `backgroundColor: '#3B82F6', // blue-600`  
  `},`  
  `primaryText: {`  
    `color: '#FFFFFF',`  
  `},`  
  `secondaryButton: {`  
    `backgroundColor: '#E5E7EB', // gray-200`  
  `},`  
  `secondaryText: {`  
    `color: '#374151', // gray-800`  
  `},`  
  `outlineButton: {`  
    `borderColor: '#3B82F6',`  
    `borderWidth: 1,`  
  `},`  
  `outlineText: {`  
    `color: '#3B82F6',`  
  `},`  
  `disabledButton: {`  
    `opacity: 0.5,`  
  `},`  
`});`

`export default Button;`

#### **Naming Conventions**

* **Components**: PascalCase for component files and names (e.g., UserProfile.tsx, Button.tsx).  
* **Hooks**: camelCase prefixed with use for custom hooks (e.g., useAuth.ts, usePropertyForm.ts).  
* **Pages/Screens**: PascalCase for page/screen components (e.g., LoginPage.tsx, HomeScreen.tsx).  
* **Services**: camelCase ending with Service for API client services (e.g., userService.ts, propertyService.ts).  
* **State Management**: Files related to state management (e.g., Redux slices, Zustand stores) will follow a consistent pattern within their respective directories (e.g., userStore.ts, propertySlice.ts).  
* **Styles**: If using CSS modules, kebab-case for file names (e.g., button.module.css). If using Tailwind CSS, utility classes are directly in JSX/TSX.  
* **Types/Interfaces**: PascalCase for TypeScript interfaces (e.g., IUser, IProperty or User, Property if not prefixed). Shared types will reside in packages/types.

### **State Management Architecture**

#### **Store Structure**

`// Shared types for state, located in packages/types`  
`// packages/types/src/state.d.ts`  
`export interface AuthState {`  
  `isAuthenticated: boolean;`  
  `user: User | null;`  
  `loading: boolean;`  
  `error: string | null;`  
`}`

`export interface PropertiesState {`  
  `properties: Property[];`  
  `loading: boolean;`  
  `error: string | null;`  
  `filters: any; // Define more specific filter interface later`  
`}`

`// Example for a Zustand store within an app (e.g., apps/admin-web/src/stores/useAuthStore.ts)`  
`// Or for React Context (e.g., apps/admin-web/src/contexts/AuthContext.tsx)`

`// apps/web-app-name/src/stores/authStore.ts (using Zustand)`  
`import { create } from 'zustand';`  
`import { AuthState } from '@nasaga/types'; // Import shared type`

`interface AuthStore extends AuthState {`  
  `login: (user: User) => void;`  
  `logout: () => void;`  
  `setLoading: (loading: boolean) => void;`  
  `setError: (error: string | null) => void;`  
`}`

`export const useAuthStore = create<AuthStore>((set) => ({`  
  `isAuthenticated: false,`  
  `user: null,`  
  `loading: false,`  
  `error: null,`  
  `login: (user) => set({ isAuthenticated: true, user, loading: false, error: null }),`  
  `logout: () => set({ isAuthenticated: false, user: null }),`  
  `setLoading: (loading) => set({ loading }),`  
  `setError: (error) => set({ error, loading: false }),`  
`}));`

`// apps/mobile-app-name/src/stores/propertiesStore.ts (using Zustand or similar)`  
`import { create } from 'zustand';`  
`import { Property } from '@nasaga/types'; // Import shared type`

`interface PropertiesStore {`  
  `properties: Property[];`  
  `loading: boolean;`  
  `error: string | null;`  
  `setProperties: (properties: Property[]) => void;`  
  `addProperty: (property: Property) => void;`  
  `updateProperty: (property: Property) => void;`  
  `removeProperty: (propertyId: string) => void;`  
  `setLoading: (loading: boolean) => void;`  
  `setError: (error: string | null) => void;`  
`}`

`export const usePropertiesStore = create<PropertiesStore>((set) => ({`  
  `properties: [],`  
  `loading: false,`  
  `error: null,`  
  `setProperties: (properties) => set({ properties }),`  
  `addProperty: (property) => set((state) => ({ properties: [...state.properties, property] })),`  
  `updateProperty: (updatedProperty) => set((state) => ({`  
    `properties: state.properties.map((p) => p.propertyId === updatedProperty.propertyId ? updatedProperty : p),`  
  `})),`  
  `removeProperty: (propertyId) => set((state) => ({`  
    `properties: state.properties.filter((p) => p.propertyId !== propertyId),`  
  `})),`  
  `setLoading: (loading) => set({ loading }),`  
  `setError: (error) => set({ error }),`  
`}));`

#### **State Management Patterns**

* **Global Application State (Auth, Theme)**: For truly global, less frequently updated state like authentication status or theme preferences, **React Context API** will be used. This is suitable for data consumed by many components but updated by few.  
* **Domain/Feature-Specific State**: For managing the state of specific domains (e.g., properties, users within an Admin panel) or features, **Zustand** is recommended. Its hook-based API provides a clean way to manage complex state, derived state, and asynchronous operations, promoting modularity.  
* **Component Local State**: useState and useReducer hooks will be used for state confined to a single component or a small, isolated subtree.  
* **Data Fetching State**: For managing the lifecycle of data fetching (loading, error, data), libraries like **React Query (TanStack Query)** could be considered for advanced caching and synchronization patterns, especially for frequently changing data or complex invalidation scenarios. For an MVP, basic useEffect with local state or Zustand actions will suffice.

### **API Integration**

#### **API Client Setup**

`// packages/utils/src/apiClient.ts (Shared API client for web and mobile)`  
`import axios from 'axios';`  
`import { getAuth } from 'firebase/auth';`

`const apiClient = axios.create({`  
  `baseURL: 'https://api.nasaga.com/v1', // Base URL for the Node.js/Express API`  
  `headers: {`  
    `'Content-Type': 'application/json',`  
  `},`  
`});`

`// Request interceptor to add Firebase ID token for authenticated requests`  
`apiClient.interceptors.request.use(async (config) => {`  
  `const auth = getAuth();`  
  `const user = auth.currentUser;`  
  `if (user) {`  
    `const token = await user.getIdToken();`  
    ``config.headers.Authorization = `Bearer ${token}`;``  
  `}`  
  `return config;`  
`}, (error) => {`  
  `return Promise.reject(error);`  
`});`

`// Response interceptor for global error handling`  
`apiClient.interceptors.response.use(`  
  `(response) => response,`  
  `(error) => {`  
    `// Example of handling common API errors`  
    `if (error.response) {`  
      `if (error.response.status === 401) {`  
        `// Handle unauthorized: e.g., redirect to login`  
        `console.error('Unauthorized access. Please log in.');`  
        `// Optionally, trigger a global logout action`  
      `} else if (error.response.status === 403) {`  
        `console.error('Forbidden: You do not have permission to perform this action.');`  
      `} else {`  
        ``console.error(`API Error: ${error.response.status} - ${error.response.data.message || error.message}`);``  
      `}`  
    `} else if (error.request) {`  
      `console.error('No response received from API. Network error or server down.');`  
    `} else {`  
      `console.error('Error setting up API request:', error.message);`  
    `}`  
    `return Promise.reject(error);`  
  `}`  
`);`

`export default apiClient;`

#### **Service Example**

`// apps/admin-web/src/services/userService.ts (Example for web app)`  
`import apiClient from '@nasaga/utils/apiClient';`  
`import { User, UserRole } from '@nasaga/types';`

`export const userService = {`  
  `// Example for Super Admin to update user role`  
  `updateUserRole: async (uid: string, role: UserRole): Promise<User> => {`  
    `try {`  
      ``const response = await apiClient.patch(`/admin/users/${uid}/role`, { role });``  
      `return response.data;`  
    `} catch (error) {`  
      `// Specific error handling for user role update`  
      `console.error('Failed to update user role:', error);`  
      `throw error;`  
    `}`  
  `},`

  `// Example to fetch all users (for Admin/Super Admin dashboard)`  
  `getAllUsers: async (): Promise<User[]> => {`  
    `try {`  
      `// This might be directly from Firestore SDK in some cases,`  
      `// but if complex filtering/pagination/security is needed, it goes through backend`  
      `const response = await apiClient.get('/admin/users'); // Assuming this endpoint exists`  
      `return response.data;`  
    `} catch (error) {`  
      `console.error('Failed to fetch users:', error);`  
      `throw error;`  
    `}`  
  `},`  
`};`

`// apps/agent-mobile/src/services/propertyService.ts (Example for mobile app)`  
`import apiClient from '@nasaga/utils/apiClient';`  
`import { Property, PropertyStatus } from '@nasaga/types';`

`export const propertyService = {`  
  `createProperty: async (propertyData: Partial<Property>): Promise<Property> => {`  
    `try {`  
      `const response = await apiClient.post('/properties', propertyData);`  
      `return response.data;`  
    `} catch (error) {`  
      `console.error('Failed to create property:', error);`  
      `throw error;`  
    `}`  
  `},`

  `updatePropertyStatus: async (propertyId: string, status: PropertyStatus): Promise<Property> => {`  
    `try {`  
      ``const response = await apiClient.patch(`/properties/${propertyId}/status`, { status });``  
      `return response.data;`  
    `} catch (error) {`  
      `console.error('Failed to update property status:', error);`  
      `throw error;`  
    `}`  
  `},`

  `// Agent's own properties can be fetched via Firestore SDK with security rules`  
  `// For more complex queries, may go through backend API.`  
  `getAgentProperties: async (agentUid: string): Promise<Property[]> => {`  
    `// Example of direct Firestore SDK usage for real-time lists`  
    `// This part would typically be handled within React hooks (e.g., useFirestoreCollection)`  
    `// For this architectural document, assume it can be a backend call if preferred.`  
    `// For now, representing an API client interface.`  
    `try {`  
        ``const response = await apiClient.get(`/properties?agentUid=${agentUid}`);``  
        `return response.data;`  
    `} catch (error) {`  
        `console.error('Failed to fetch agent properties:', error);`  
        `throw error;`  
    `}`  
  `},`  
`};`

### **Routing Architecture**

#### **Route Configuration**

`// apps/admin-web/src/router/index.tsx (Example for React Web)`  
`import React from 'react';`  
`import { BrowserRouter as Router, Routes, Route, Navigate, Outlet } from 'react-router-dom';`  
`import { useAuthStore } from '@nasaga/admin-web/src/stores/authStore'; // Assuming a local auth store`  
`import LoginPage from '@nasaga/admin-web/src/pages/Auth/LoginPage';`  
`import DashboardPage from '@nasaga/admin-web/src/pages/Dashboard/DashboardPage';`  
`import UserManagementPage from '@nasaga/admin-web/src/pages/UserManagement/UserManagementPage';`  
`import PropertyApprovalPage from '@nasaga/admin-web/src/pages/PropertyApproval/PropertyApprovalPage';`  
`import { UserRole } from '@nasaga/types';`

`// A component to protect routes based on authentication and role`  
`interface ProtectedRouteProps {`  
  `allowedRoles: UserRole[];`  
`}`

`const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ allowedRoles }) => {`  
  `const { isAuthenticated, user } = useAuthStore();`

  `if (!isAuthenticated) {`  
    `return <Navigate to="/login" replace />;`  
  `}`

  `if (!user || !allowedRoles.includes(user.role)) {`  
    `// Optionally, redirect to an unauthorized page or dashboard`  
    `return <Navigate to="/unauthorized" replace />;`  
  `}`

  `return <Outlet />;`  
`};`

`const AppRouter: React.FC = () => {`  
  `return (`  
    `<Router>`  
      `<Routes>`  
        `<Route path="/login" element={<LoginPage />} />`  
        `<Route path="/unauthorized" element={<div>Unauthorized Access</div>} /> {/* Placeholder */}`

        `{/* Admin Routes */}`  
        `<Route element={<ProtectedRoute allowedRoles={['admin', 'superAdmin']} />}>`  
          `<Route path="/dashboard" element={<DashboardPage />} />`  
          `<Route path="/properties-approval" element={<PropertyApprovalPage />} />`  
        `</Route>`

        `{/* Super Admin Specific Routes (inherits admin access) */}`  
        `<Route element={<ProtectedRoute allowedRoles={['superAdmin']} />}>`  
          `<Route path="/users" element={<UserManagementPage />} />`  
        `</Route>`

        `<Route path="*" element={<Navigate to="/dashboard" />} /> {/* Default redirect */}`  
      `</Routes>`  
    `</Router>`  
  `);`  
`};`

`export default AppRouter;`

`// apps/agent-mobile/src/navigation/AppNavigator.tsx (Example for React Native)`  
`import React from 'react';`  
`import { NavigationContainer } from '@react-navigation/native';`  
`import { createNativeStackNavigator } from '@react-navigation/native-stack';`  
`import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';`  
`import { useAuthStore } from '@nasaga/agent-mobile/src/stores/authStore'; // Assuming a local auth store`  
`import LoginPage from '@nasaga/agent-mobile/src/screens/Auth/LoginPage';`  
`import ProfileScreen from '@nasaga/agent-mobile/src/screens/Profile/ProfileScreen';`  
`import PropertyListScreen from '@nasaga/agent-mobile/src/screens/Property/PropertyListScreen';`  
`import CreatePropertyScreen from '@nasaga/agent-mobile/src/screens/Property/CreatePropertyScreen';`  
`import PropertyDetailScreen from '@nasaga/agent-mobile/src/screens/Property/PropertyDetailScreen';`  
`import { UserRole } from '@nasaga/types';`

`const AuthStack = createNativeStackNavigator();`  
`const AppStack = createNativeStackNavigator();`  
`const Tab = createBottomTabNavigator();`

`// Main tabs for authenticated Agent users`  
`const AgentTabs = () => {`  
  `return (`  
    `<Tab.Navigator>`  
      `<Tab.Screen name="MyProperties" component={PropertyListScreen} />`  
      `<Tab.Screen name="CreateProperty" component={CreatePropertyScreen} />`  
      `<Tab.Screen name="Profile" component={ProfileScreen} />`  
    `</Tab.Navigator>`  
  `);`  
`};`

`const AppNavigator: React.FC = () => {`  
  `const { isAuthenticated, user } = useAuthStore();`

  `if (!isAuthenticated) {`  
    `return (`  
      `<NavigationContainer>`  
        `<AuthStack.Navigator screenOptions={{ headerShown: false }}>`  
          `<AuthStack.Screen name="Login" component={LoginPage} />`  
        `</AuthStack.Navigator>`  
      `</NavigationContainer>`  
    `);`  
  `}`

  `// Ensure role is 'agent' for Agent app`  
  `if (user?.role !== 'agent') {`  
    `// Optionally, show an unauthorized screen or log out`  
    `return (`  
      `<NavigationContainer>`  
        `<AuthStack.Navigator screenOptions={{ headerShown: false }}>`  
          `<AuthStack.Screen name="Unauthorized" component={() => <LoginPage />} /> {/* Redirect to login or unauthorized message */}`  
        `</AuthStack.Navigator>`  
      `</NavigationContainer>`  
    `);`  
  `}`

  `return (`  
    `<NavigationContainer>`  
      `<AppStack.Navigator screenOptions={{ headerShown: false }}>`  
        `<AppStack.Screen name="AgentMain" component={AgentTabs} />`  
        `<AppStack.Screen name="PropertyDetail" component={PropertyDetailScreen} />`  
        `{/* Add other screens that are part of the navigation flow but not in tabs */}`  
      `</AppStack.Navigator>`  
    `</NavigationContainer>`  
  `);`  
`};`

`export default AppNavigator;`

### **Styling Guidelines**

#### **Styling Approach**

**Tailwind CSS** will be the primary utility-first CSS framework for both React web dashboards and, where possible, integrated into React Native applications (e.g., via tailwindcss-react-native or similar compatible solutions for React Native's StyleSheet API). This approach promotes rapid UI development, consistency, and efficient styling. For React Native, direct StyleSheet API will be used for styles that cannot be elegantly expressed with Tailwind utilities or for performance-critical native styles.

#### **Global Theme Variables**

`/* packages/ui/src/theme/colors.css (for Tailwind CSS in web apps) */`  
`/* This will be imported into a CSS file that Tailwind processes */`  
`@tailwind base;`  
`@tailwind components;`  
`@tailwind utilities;`

`:root {`  
  `--color-primary-50: #EEF2FF;`  
  `--color-primary-100: #E0E7FF;`  
  `--color-primary-200: #C7D2FE;`  
  `--color-primary-300: #A5B4FC;`  
  `--color-primary-400: #818CF8;`  
  `--color-primary-500: #6366F1; /* Primary brand color */`  
  `--color-primary-600: #4F46E5;`  
  `--color-primary-700: #4338CA;`  
  `--color-primary-800: #3730A3;`  
  `--color-primary-900: #312E81;`

  `--color-secondary-50: #F0FDF4;`  
  `--color-secondary-100: #DCFCE7;`  
  `--color-secondary-200: #BBF7D0;`  
  `--color-secondary-300: #86EFAC;`  
  `--color-secondary-400: #4ADE80;`  
  `--color-secondary-500: #22C55E; /* Secondary accent color (e.g., success) */`  
  `--color-secondary-600: #16A34A;`  
  `--color-secondary-700: #15803D;`  
  `--color-secondary-800: #166534;`  
  `--color-secondary-900: #14532D;`

  `/* Neutral colors for text, backgrounds, borders */`  
  `--color-gray-50: #F9FAFB;`  
  `--color-gray-100: #F3F4F6;`  
  `--color-gray-200: #E5E7EB;`  
  `--color-gray-300: #D1D5DB;`  
  `--color-gray-400: #9CA3AF;`  
  `--color-gray-500: #6B7280;`  
  `--color-gray-600: #4B5563;`  
  `--color-gray-700: #374151;`  
  `--color-gray-800: #1F2937;`  
  `--color-gray-900: #111827;`

  `/* Semantic colors */`  
  `--color-success: var(--color-secondary-500);`  
  `--color-warning: #F59E0B; /* amber-500 */`  
  `--color-error: #EF4444; /* red-500 */`  
`}`

`/* You would then configure tailwind.config.js to extend these colors */`  
```` ```typescript ````  
`// packages/ui/src/theme/index.ts (for React Native apps and shared JS/TS access)`  
`import { TextStyle, ViewStyle } from 'react-native';`

`interface Colors {`  
  `primary: string;`  
  `secondary: string;`  
  `accent: string;`  
  `success: string;`  
  `warning: string;`  
  `error: string;`  
  `background: string;`  
  `text: string;`  
  `textMuted: string;`  
`}`

`interface Spacing {`  
  `xs: number;`  
  `sm: number;`  
  `md: number;`  
  `lg: number;`  
  `xl: number;`  
`}`

`interface Typography {`  
  `h1: TextStyle;`  
  `h2: TextStyle;`  
  `body: TextStyle;`  
  `small: TextStyle;`  
`}`

`interface Theme {`  
  `colors: Colors;`  
  `spacing: Spacing;`  
  `typography: Typography;`  
  `// Add more properties like borderRadius, shadows etc.`  
`}`

`export const lightTheme: Theme = {`  
  `colors: {`  
    `primary: '#6366F1', // Tailwind blue-500`  
    `secondary: '#22C55E', // Tailwind green-500`  
    `accent: '#818CF8', // Tailwind blue-400`  
    `success: '#22C55E',`  
    `warning: '#F59E0B',`  
    `error: '#EF4444',`  
    `background: '#FFFFFF',`  
    `text: '#111827', // Tailwind gray-900`  
    `textMuted: '#6B7280', // Tailwind gray-500`  
  `},`  
  `spacing: {`  
    `xs: 4,`  
    `sm: 8,`  
    `md: 16,`  
    `lg: 24,`  
    `xl: 32,`  
  `},`  
  `typography: {`  
    `h1: { fontSize: 32, fontWeight: 'bold', color: '#111827' },`  
    `h2: { fontSize: 24, fontWeight: 'bold', color: '#111827' },`  
    `body: { fontSize: 16, color: '#111827' },`  
    `small: { fontSize: 12, color: '#6B7280' },`  
  `},`  
`};`

`// You can export a darkTheme similarly`

### **Testing Requirements**

#### **Component Test Template**

`// For React Web components (e.g., apps/admin-web/src/components/Button/Button.test.tsx)`  
`import React from 'react';`  
`import { render, screen, fireEvent } from '@testing-library/react';`  
`import Button from './Button'; // Assuming local import for testing`

`describe('Button', () => {`  
  `it('renders correctly with children', () => {`  
    `render(<Button onClick={() => {}}>Click Me</Button>);`  
    `expect(screen.getByText(/Click Me/i)).toBeInTheDocument();`  
  `});`

  `it('calls onClick when clicked', () => {`  
    `const handleClick = jest.fn();`  
    `render(<Button onClick={handleClick}>Test Button</Button>);`  
    `fireEvent.click(screen.getByText(/Test Button/i));`  
    `expect(handleClick).toHaveBeenCalledTimes(1);`  
  `});`

  `it('is disabled when the disabled prop is true', () => {`  
    `const handleClick = jest.fn();`  
    `render(<Button onClick={handleClick} disabled>Disabled Button</Button>);`  
    `expect(screen.getByText(/Disabled Button/i)).toBeDisabled();`  
    `fireEvent.click(screen.getByText(/Disabled Button/i));`  
    `expect(handleClick).not.toHaveBeenCalled();`  
  `});`  
`});`

`// For React Native components (e.g., apps/agent-mobile/src/components/Button/Button.test.tsx)`  
`import React from 'react';`  
`import { render, fireEvent } from '@testing-library/react-native';`  
`import Button from './Button'; // Assuming local import for testing`

`describe('Button', () => {`  
  `it('renders correctly with text content', () => {`  
    `const { getByText } = render(<Button onPress={() => {}}>Press Me</Button>);`  
    `expect(getByText(/Press Me/i)).toBeTruthy();`  
  `});`

  `it('calls onPress when pressed', () => {`  
    `const handlePress = jest.fn();`  
    `const { getByText } = render(<Button onPress={handlePress}>Press Me</Button>);`  
    `fireEvent.press(getByText(/Press Me/i));`  
    `expect(handlePress).toHaveBeenCalledTimes(1);`  
  `});`

  `it('is disabled when the disabled prop is true', () => {`  
    `const handlePress = jest.fn();`  
    `const { getByText } = render(<Button onPress={handlePress} disabled>Disabled Button</Button>);`  
    `expect(getByText(/Disabled Button/i).props.accessibilityState.disabled).toBe(true);`  
    `fireEvent.press(getByText(/Disabled Button/i));`  
    `expect(handlePress).not.toHaveBeenCalled();`  
  `});`  
`});`

#### **Testing Best Practices**

* **Unit Tests**: Test individual components, hooks, and utility functions in isolation. Focus on the smallest testable units to ensure their correctness.  
* **Integration Tests**: Test interactions between multiple components, or between components and services. For frontend, this includes testing data fetching from API services and state updates.  
* **End-to-End (E2E) Tests**: Use Cypress (for web) and/or Playwright (for web, with potential for mobile browser-based E2E if a web-based mobile testing solution emerges) for critical user flows across the entire application, simulating real user interactions in a browser or mobile emulator. Nx provides generators for setting up Cypress/Playwright.  
* **Coverage Goals**: Aim for a reasonable code coverage target (e.g., 80% line coverage) to ensure sufficient testing, focusing on critical paths.  
* **Test Structure**: Follow the Arrange-Act-Assert (AAA) pattern for clear and readable tests.  
* **Mock External Dependencies**: Mock API calls, Firebase SDKs, routing, and global state management to ensure tests are fast, isolated, and deterministic.  
* **Accessibility Testing**: Integrate automated accessibility checks (e.g., jest-axe for React components, or Lighthouse/Pa11y in E2E tests) as part of the CI pipeline.

## **Backend Architecture**

### **Service Architecture**

#### **Traditional Server Architecture**

* **Controller/Route Organization**:  
  `apps/`  
  `└── backend/`  
      `└── src/`  
          `├── controllers/          # Handles incoming API requests, delegates to services`  
          `│   ├── auth.controller.ts`  
          `│   ├── properties.controller.ts`  
          `│   └── users.controller.ts`  
          `├── services/             # Contains core business logic`  
          `│   ├── auth.service.ts`  
          `│   ├── property.service.ts`  
          `│   └── user.service.ts`  
          `├── middleware/           # Express middleware (e.g., authentication, error handling)`  
          `├── models/               # Data access layer / interfaces for Firestore interactions`  
          `├── routes/               # Defines API routes and links to controllers/middleware`  
          `├── utils/                # Backend-specific utilities`  
          `└── app.ts                # Main Express application setup`

* **Controller Template**:  
  `// apps/backend/src/controllers/properties.controller.ts`  
  `import { Request, Response, NextFunction } from 'express';`  
  `import { propertyService } from '../services/property.service';`  
  `import { validatePropertyCreation, validatePropertyStatusUpdate } from '../validation/property.validation'; // Assuming validation`  
  `import { UserRole } from '@nasaga/types'; // Shared types`

  `export const propertiesController = {`  
    `createProperty: async (req: Request, res: Response, next: NextFunction) => {`  
      `try {`  
        `// Get agent UID from authenticated user (via middleware)`  
        `const agentUid = (req as any).user.uid; // Assuming user info is attached by auth middleware`  
        `const propertyData = req.body;`

        `// Validate input (assuming Joi or Zod validation)`  
        `validatePropertyCreation(propertyData);`

        `const newProperty = await propertyService.createProperty(agentUid, propertyData);`  
        `res.status(201).json(newProperty);`  
      `} catch (error) {`  
        `next(error); // Pass error to global error handler`  
      `}`  
    `},`

    `updatePropertyStatus: async (req: Request, res: Response, next: NextFunction) => {`  
      `try {`  
        `const { propertyId } = req.params;`  
        `const { status } = req.body;`  
        `const currentUserRole = (req as any).user.role as UserRole; // Assuming role attached by auth middleware`

        `// Validate input`  
        `validatePropertyStatusUpdate({ status });`

        `// Business logic for status update and role check in service`  
        `const updatedProperty = await propertyService.updatePropertyStatus(propertyId, status, currentUserRole);`  
        `res.status(200).json(updatedProperty);`  
      `} catch (error) {`  
        `next(error);`  
      `}`  
    `},`

    `// Add other methods like getPropertyById, getAgentProperties, etc.`  
  `};`

### **Database Architecture**

#### **Schema Design**

`// Example of a user document in Firestore: users/{uid}`  
`{`  
  `"uid": "firebaseAuthUid123",`  
  `"email": "<EMAIL>",`  
  `"displayName": "Jane Doe",`  
  `"role": "agent",`  
  `"phoneNumber": "+2349012345678",`  
  `"profilePictureUrl": "[https://firebasestorage.googleapis.com/v0/b/nasaga.appspot.com/o/profile_pictures%2Fjane.jpg](https://firebasestorage.googleapis.com/v0/b/nasaga.appspot.com/o/profile_pictures%2Fjane.jpg)",`  
  `"createdAt": {`  
    `"_seconds": 1732730400,`  
    `"_nanoseconds": 0`  
  `},`  
  `"updatedAt": {`  
    `"_seconds": 1732734000,`  
    `"_nanoseconds": 0`  
  `}`  
`}`

`// Example of a property document in Firestore: properties/{propertyId}`  
`{`  
  `"propertyId": "autoGeneratedPropertyId456",`  
  `"title": "Modern Apartment in Ikoyi",`  
  `"description": "Luxurious 2-bedroom apartment with city views.",`  
  `"address": "456 Oak Ave",`  
  `"city": "Ikoyi",`  
  `"state": "Lagos",`  
  `"zipCode": "100001",`  
  `"price": 120000000,`  
  `"propertyType": "Apartment",`  
  `"bedrooms": 2,`  
  `"bathrooms": 2,`  
  `"squareFootage": 1500,`  
  `"agentUid": "firebaseAuthUid123",`  
  `"status": "active",`  
  `"imageUrls": [`  
    `"[https://firebasestorage.googleapis.com/v0/b/nasaga.appspot.com/o/properties%2Fapt1.jpg](https://firebasestorage.googleapis.com/v0/b/nasaga.appspot.com/o/properties%2Fapt1.jpg)",`  
    `"[https://firebasestorage.googleapis.com/v0/b/nasaga.appspot.com/o/properties%2Fapt2.jpg](https://firebasestorage.googleapis.com/v0/b/nasaga.appspot.com/o/properties%2Fapt2.jpg)"`  
  `],`  
  `"documentUrls": [],`  
  `"createdAt": {`  
    `"_seconds": 1732731000,`  
    `"_nanoseconds": 0`  
  `},`  
  `"updatedAt": {`  
    `"_seconds": 1732735000,`  
    `"_nanoseconds": 0`  
  `},`  
  `"approvedByAdminUid": "adminFirebaseUid789",`  
  `"approvedAt": {`  
    `"_seconds": 1732732000,`  
    `"_nanoseconds": 0`  
  `}`  
`}`

#### **Data Access Layer**

`// apps/backend/src/models/user.model.ts (Example User Repository)`  
`import { db } from '../config/firebase.config'; // Firebase Admin SDK Firestore instance`  
`import { User, UserRole } from '@nasaga/types'; // Shared User interface`

`const usersCollection = db.collection('users');`

`export const UserModel = {`  
  `// Create a new user document`  
  `async createUser(uid: string, email: string, displayName: string, role: UserRole): Promise<User> {`  
    `const newUser: User = {`  
      `uid,`  
      `email,`  
      `displayName,`  
      `role,`  
      `createdAt: new Date() as any, // Firestore Timestamp will convert automatically`  
      `updatedAt: new Date() as any,`  
    `};`  
    `await usersCollection.doc(uid).set(newUser);`  
    `return newUser;`  
  `},`

  `// Get a user by UID`  
  `async getUserByUid(uid: string): Promise<User | null> {`  
    `const doc = await usersCollection.doc(uid).get();`  
    `if (!doc.exists) {`  
      `return null;`  
    `}`  
    `return doc.data() as User;`  
  `},`

  `// Update a user's role`  
  `async updateUserRole(uid: string, newRole: UserRole): Promise<User | null> {`  
    `const userRef = usersCollection.doc(uid);`  
    `await userRef.update({`  
      `role: newRole,`  
      `updatedAt: new Date(),`  
    `});`  
    `const updatedDoc = await userRef.get();`  
    `return updatedDoc.data() as User;`  
  `},`

  `// Get users by role`  
  `async getUsersByRole(role: UserRole): Promise<User[]> {`  
    `const snapshot = await usersCollection.where('role', '==', role).get();`  
    `return snapshot.docs.map(doc => doc.data() as User);`  
  `},`

  `// Add other CRUD operations as needed`  
`};`

`// apps/backend/src/models/property.model.ts (Example Property Repository)`  
`import { db } from '../config/firebase.config'; // Firebase Admin SDK Firestore instance`  
`import { Property, PropertyStatus } from '@nasaga/types'; // Shared Property interface`

`const propertiesCollection = db.collection('properties');`

`export const PropertyModel = {`  
  `// Create a new property document`  
  `async createProperty(propertyData: Omit<Property, 'propertyId' | 'createdAt' | 'updatedAt' | 'approvedAt' | 'approvedByAdminUid'>): Promise<Property> {`  
    `const newPropertyRef = propertiesCollection.doc(); // Auto-generate ID`  
    `const newProperty: Property = {`  
      `...propertyData,`  
      `propertyId: newPropertyRef.id,`  
      `status: 'pending', // Default status for new properties`  
      `createdAt: new Date() as any,`  
      `updatedAt: new Date() as any,`  
    `};`  
    `await newPropertyRef.set(newProperty);`  
    `return newProperty;`  
  `},`

  `// Get a property by ID`  
  `async getPropertyById(propertyId: string): Promise<Property | null> {`  
    `const doc = await propertiesCollection.doc(propertyId).get();`  
    `if (!doc.exists) {`  
      `return null;`  
    `}`  
    `return doc.data() as Property;`  
  `},`

  `// Update property status`  
  `async updatePropertyStatus(propertyId: string, status: PropertyStatus, adminUid: string): Promise<Property | null> {`  
    `const propertyRef = propertiesCollection.doc(propertyId);`  
    `await propertyRef.update({`  
      `status: status,`  
      `approvedByAdminUid: adminUid,`  
      `approvedAt: new Date(),`  
      `updatedAt: new Date(),`  
    `});`  
    `const updatedDoc = await propertyRef.get();`  
    `return updatedDoc.data() as Property;`  
  `},`

  `// Get properties by status`  
  `async getPropertiesByStatus(status: PropertyStatus): Promise<Property[]> {`  
    `const snapshot = await propertiesCollection.where('status', '==', status).get();`  
    `return snapshot.docs.map(doc => doc.data() as Property);`  
  `},`

  `// Add other query methods (e.g., by agentUid, search, filter)`  
`};`

### **Authentication and Authorization**

#### **Auth Flow**

`sequenceDiagram`  
    `actor User`  
    `participant ClientApp[Client App (Web/Mobile)]`  
    `participant FirebaseAuthClient[Firebase Auth SDK (Client)]`  
    `participant BackendAPI[Backend API (Node.js/Express)]`  
    `participant FirebaseAuthAdmin[Firebase Auth Admin SDK (Backend)]`  
    `participant Firestore[Firebase Firestore]`

    `User->>ClientApp: 1. Login/Registration`  
    `ClientApp->>FirebaseAuthClient: 2. Authenticates User (e.g., signInWithEmailAndPassword)`  
    `FirebaseAuthClient-->>ClientApp: 3. Returns ID Token`  
    `ClientApp->>BackendAPI: 4. Makes authenticated request (attaches ID Token in Authorization header)`  
    `Note over ClientApp,BackendAPI: e.g., GET /properties-approval`  
    `BackendAPI->>FirebaseAuthAdmin: 5. Verifies ID Token (verifyIdToken)`  
    `FirebaseAuthAdmin-->>BackendAPI: 6. Returns Decoded Token (includes UID, optional custom claims)`  
    `BackendAPI->>Firestore: 7. (Optional) Fetches User Role from Firestore if not in custom claims`  
    `Firestore-->>BackendAPI: 8. Returns User Role`  
    `BackendAPI-->>BackendAPI: 9. Authorizes request based on User Role and Endpoint permissions`  
    `BackendAPI-->>ClientApp: 10. Sends API Response (if authorized)`  
    `BackendAPI--xClientApp: 10. Sends 403 Forbidden (if unauthorized)`

#### **Middleware/Guards**

`// apps/backend/src/middleware/auth.middleware.ts`  
`import { Request, Response, NextFunction } from 'express';`  
`import { getAuth } from 'firebase-admin/auth';`  
`import { db } from '../config/firebase.config'; // Firestore instance`  
`import { UserRole } from '@nasaga/types'; // Shared types`

`// Middleware to verify Firebase ID Token`  
`export const verifyFirebaseToken = async (req: Request, res: Response, next: NextFunction) => {`  
  `const authHeader = req.headers.authorization;`  
  `if (!authHeader || !authHeader.startsWith('Bearer ')) {`  
    `return res.status(401).send({ message: 'Unauthorized: No token provided.' });`  
  `}`

  `const idToken = authHeader.split('Bearer ')[1];`

  `try {`  
    `const decodedToken = await getAuth().verifyIdToken(idToken);`  
    `(req as any).user = { uid: decodedToken.uid, email: decodedToken.email }; // Attach basic user info`

    `// Fetch user role from Firestore if not available as custom claim`  
    `// For production, consider storing role as a Firebase custom claim for faster lookup`  
    `const userDoc = await db.collection('users').doc(decodedToken.uid).get();`  
    `if (userDoc.exists) {`  
      `(req as any).user.role = (userDoc.data() as any).role as UserRole;`  
    `} else {`  
      `// Handle case where user document not found (e.g., new user not yet fully provisioned)`  
      ``console.warn(`User document not found for UID: ${decodedToken.uid}`);``  
      `return res.status(403).send({ message: 'Forbidden: User profile incomplete.' });`  
    `}`

    `next();`  
  `} catch (error) {`  
    `console.error('Error verifying Firebase ID token:', error);`  
    `res.status(401).send({ message: 'Unauthorized: Invalid or expired token.' });`  
  `}`  
`};`

`// Authorization middleware/guard`  
`export const authorizeRoles = (allowedRoles: UserRole[]) => {`  
  `return (req: Request, res: Response, next: NextFunction) => {`  
    `const userRole = (req as any).user?.role;`

    `if (!userRole || !allowedRoles.includes(userRole)) {`  
      ``return res.status(403).send({ message: `Forbidden: Insufficient role. Required: ${allowedRoles.join(', ')}` });``  
    `}`  
    `next();`  
  `};`  
`};`

`// Example usage in a route`  
`/*`  
`import express from 'express';`  
`import { verifyFirebaseToken, authorizeRoles } from '../middleware/auth.middleware';`  
`import { propertiesController } from '../controllers/properties.controller';`

`const router = express.Router();`

`router.patch(`  
  `'/properties/:propertyId/status',`  
  `verifyFirebaseToken,`  
  `authorizeRoles(['admin', 'superAdmin']), // Only Admin/Super Admin can update status`  
  `propertiesController.updatePropertyStatus`  
`);`

`router.post(`  
  `'/properties',`  
  `verifyFirebaseToken,`  
  `authorizeRoles(['agent']), // Only Agents can create properties`  
  `propertiesController.createProperty`  
`);`  
`*/`

## **Unified Project Structure**

`nasaga-monorepo/`  
`├── .github/                    # CI/CD workflows for GitHub Actions`  
`│   └── workflows/`  
`│       ├── ci.yaml             # Continuous Integration pipeline`  
`│       └── deploy.yaml         # Continuous Deployment pipeline`  
`├── apps/                       # Contains individual deployable applications`  
`│   ├── admin-web/              # React web application for Admin dashboard`  
`│   │   ├── src/`  
`│   │   │   ├── assets/         # Static assets (images, fonts)`  
`│   │   │   ├── components/     # Application-specific UI components`  
`│   │   │   ├── configs/        # Application-specific configurations`  
`│   │   │   ├── contexts/       # React Context providers`  
`│   │   │   ├── layouts/        # Common layouts`  
`│   │   │   ├── pages/          # Page-level components/routes`  
`│   │   │   ├── services/       # API client services specific to admin-web`  
`│   │   │   ├── stores/         # Zustand stores for local state`  
`│   │   │   └── utils/          # Admin-web specific utilities`  
`│   │   ├── public/             # Public assets (e.g., index.html, favicon)`  
`│   │   ├── tests/              # Unit and integration tests`  
`│   │   └── project.json        # Nx project configuration`  
`│   ├── super-admin-web/        # React web application for Super Admin dashboard`  
`│   │   ├── src/`  
`│   │   │   ├── assets/`  
`│   │   │   ├── components/`  
`│   │   │   ├── configs/`  
`│   │   │   ├── contexts/`  
`│   │   │   ├── layouts/`  
`│   │   │   ├── pages/`  
`│   │   │   ├── services/`  
`│   │   │   ├── stores/`  
`│   │   │   └── utils/`  
`│   │   ├── public/`  
`│   │   ├── tests/`  
`│   │   └── project.json`  
`│   ├── agent-mobile/           # React Native with Expo mobile application for Agents`  
`│   │   ├── src/`  
`│   │   │   ├── assets/         # Images, fonts, etc.`  
`│   │   │   ├── components/     # Application-specific UI components`  
`│   │   │   ├── configs/`  
`│   │   │   ├── contexts/`  
`│   │   │   ├── hooks/          # Custom React Native hooks`  
`│   │   │   ├── navigation/     # React Navigation setup`  
`│   │   │   ├── screens/        # Screen-level components/routes`  
`│   │   │   ├── services/       # API client services specific to agent-mobile`  
`│   │   │   ├── stores/         # Zustand stores for local state`  
`│   │   │   └── utils/          # Agent-mobile specific utilities`  
`│   │   ├── app.json            # Expo configuration`  
`│   │   ├── tests/              # Unit and integration tests`  
`│   │   └── project.json`  
`│   ├── buyer-mobile/           # React Native with Expo mobile application for Buyers`  
`│   │   ├── src/`  
`│   │   │   ├── assets/`  
`│   │   │   ├── components/`  
`│   │   │   ├── configs/`  
`│   │   │   ├── contexts/`  
`│   │   │   ├── hooks/`  
`│   │   │   ├── navigation/`  
`│   │   │   ├── screens/`  
`│   │   │   ├── services/`  
`│   │   │   ├── stores/`  
`│   │   │   └── utils/`  
`│   │   ├── app.json`  
`│   │   ├── tests/`  
`│   │   └── project.json`  
`│   └── backend/                # Node.js/Express API application`  
`│       ├── src/`  
`│       │   ├── config/         # Firebase Admin SDK setup, environment variables`  
`│       │   ├── controllers/    # Handles incoming API requests, delegates to services`  
`│       │   ├── middleware/     # Express middleware (e.g., authentication, error handling)`  
`│       │   ├── models/         # Data access layer for Firestore interactions`  
`│       │   ├── routes/         # Defines API routes`  
`│       │   ├── services/       # Core business logic`  
`│       │   ├── validation/     # Input validation schemas (e.g., Zod, Joi)`  
`│       │   └── app.ts          # Main Express application setup`  
`│       ├── tests/              # Backend unit and integration tests`  
`│       └── project.json`  
`├── packages/                   # Contains shared libraries`  
`│   ├── ui/                     # Shared UI components (React/React Native compatible)`  
`│   │   ├── src/`  
`│   │   │   ├── common/         # Generic UI components (buttons, inputs)`  
`│   │   │   ├── forms/          # Reusable form components`  
`│   │   │   ├── navigation/     # Shared navigation elements`  
`│   │   │   ├── layouts/        # Shared layout patterns`  
`│   │   │   └── theme/          # Shared styling variables/theme setup`  
`│   │   └── project.json`  
`│   ├── utils/                  # Shared utility functions`  
`│   │   ├── src/`  
`│   │   │   ├── apiClient.ts    # Shared Axios instance with interceptors`  
`│   │   │   ├── formatters.ts   # Data formatting utilities`  
`│   │   │   ├── helpers.ts      # General helpers`  
`│   │   │   └── firebase.ts     # Client-side Firebase initialization (if shared)`  
`│   │   └── project.json`  
`│   └── types/                  # Shared TypeScript types and interfaces`  
`│       ├── src/`  
`│       │   ├── auth.d.ts       # Auth related types`  
`│       │   ├── common.d.ts     # General types`  
`│       │   ├── user.d.ts       # User model interface`  
`│       │   └── property.d.ts   # Property model interface`  
`│       └── project.json`  
`├── infrastructure/             # Infrastructure as Code definitions (e.g., Terraform if used)`  
`│   └── firebase/               # Firebase project configuration / deployments`  
`├── scripts/                    # General monorepo-level build/deploy scripts`  
`├── docs/                       # Project documentation`  
`│   ├── prd.md`  
`│   ├── front-end-spec.md       # Placeholder (should be generated by UX Expert)`  
`│   └── fullstack-architecture.md`  
`├── .env.example                # Template for environment variables`  
`├── package.json                # Root package.json (Nx managed workspaces)`  
`├── nx.json                     # Nx workspace configuration`  
`├── metro.config.js             # Metro bundler configuration for React Native apps in monorepo`  
`├── .npmrc                      # npm configuration (e.g., node-linker=hoisted for pnpm compatibility)`  
`└── README.md`

## **Development Workflow**

### **Local Development Setup**

#### **Prerequisites**

Before starting, ensure the following are installed:  
`# Node.js (LTS version, e.g., v20.x, as per Tech Stack)`  
`# npm (comes with Node.js) or pnpm (recommended for monorepos with Nx)`  
`# Git`  
`# Android Studio (for Android development and emulator on Windows/Linux/macOS)`  
`# Watchman (for React Native file watching on macOS/Linux - not strictly needed on Windows but good practice for project consistency)`  
`# Java Development Kit (JDK) - specific version depends on Android SDK needs`

`# Note for iOS development on Windows:`  
`# Xcode is NOT required for Windows users due to the use of Expo.`  
`# For building iOS applications, Expo Application Services (EAS Build) will be utilized for cloud builds.`  
`# Testing iOS apps during development can be done on a physical iOS device using the Expo Go app.`

#### **Initial Setup**

Once prerequisites are met, clone the repository and install dependencies:  
`# 1. Clone the monorepo`  
`git clone [repository-url] nasaga-monorepo`  
`cd nasaga-monorepo`

`# 2. Install pnpm (if not already installed)`  
`npm install -g pnpm`

`# 3. Configure pnpm for hoisted node_modules (crucial for React Native/Expo in monorepo)`  
`# Add 'node-linker=hoisted' to .npmrc file at the project root`  
`echo "node-linker=hoisted" >> .npmrc`

`# 4. Install monorepo dependencies using pnpm`  
`pnpm install`

`# 5. Link local Expo CLI (if not globally available or needed for Nx integration)`  
`# pnpm add -D expo-cli # Or global install if preferred`

#### **Development Commands**

Nx simplifies running commands across multiple applications in the monorepo. Here are common commands:  
`# Start all services (Backend API, Admin Web, Super Admin Web, Agent Mobile, Buyer Mobile)`  
`# This will typically require separate terminal tabs or a more advanced orchestration script`  
`# For web apps:`  
`npx nx serve admin-web`  
`npx nx serve super-admin-web`  
``# For mobile apps (requires Metro Bundler to start, often handled by `expo start`):``  
`npx nx start agent-mobile --web # For web preview of mobile app`  
`npx nx start buyer-mobile --web # For web preview of mobile app`  
`# For physical device/emulator testing, run 'npx nx start <app-name>' and then`  
`# use the Expo Go app on your device/emulator to scan the QR code.`

`# For backend:`  
`npx nx serve backend`

`# Start frontend only (example for Admin Web)`  
`npx nx serve admin-web`

`# Start backend only`  
`npx nx serve backend`

`# Run tests for all affected projects (after changes)`  
`npx nx affected --target=test --parallel`

`# Run tests for a specific project (e.g., agent-mobile)`  
`npx nx test agent-mobile`

`# Run E2E tests for a specific web app (e.g., admin-web)`  
`npx nx e2e admin-web-e2e`

`# Build a specific project for production (e.g., admin-web)`  
`npx nx build admin-web`

`# Build all affected projects for production`  
`npx nx affected --target=build --parallel`

### **Environment Configuration**

Environment variables will be managed using .env files within each application's root (or globally at the monorepo root for shared variables) and accessed through a consistent configuration loading mechanism. Sensitive variables will be securely handled in deployment environments.

#### **Required Environment Variables**

`# Shared (could be in .env at root or in specific app .env files)`  
`# For Firebase Client SDKs (can often be loaded from firebase.json/app.json by Expo/Firebase itself)`  
`# This is mainly for illustration if manual env setup is needed.`  
`# REACT_APP_FIREBASE_API_KEY="YOUR_FIREBASE_API_KEY"`  
`# REACT_APP_FIREBASE_AUTH_DOMAIN="YOUR_FIREBASE_AUTH_DOMAIN"`  
`# REACT_APP_FIREBASE_PROJECT_ID="YOUR_FIREBASE_PROJECT_ID"`  
`# ...etc.`

`# Frontend (.env.local for web apps, or app.config.js for Expo)`  
`# apps/admin-web/.env.local`  
`REACT_APP_BACKEND_API_URL="http://localhost:3000/v1" # Local backend API URL`

`# Backend (.env for Node.js/Express app)`  
`# apps/backend/.env`  
`FIREBASE_PRIVATE_KEY_ID="YOUR_FIREBASE_PRIVATE_KEY_ID" # For Firebase Admin SDK`  
`FIREBASE_PRIVATE_KEY="YOUR_FIREBASE_PRIVATE_KEY" # Replace \n with actual newlines or load from file`  
`FIREBASE_CLIENT_EMAIL="YOUR_FIREBASE_CLIENT_EMAIL"`  
`FIREBASE_PROJECT_ID="YOUR_FIREBASE_PROJECT_ID"`  
`NODE_ENV="development"`  
`PORT="3000"`

## **Deployment Architecture**

### **Deployment Strategy**

* **Frontend Deployment (Web Apps \- Admin & Super Admin Dashboards):**  
  * **Platform:** Firebase Hosting.  
  * **Build Command:** npx nx build \<app-name\>.  
  * **Output Directory:** The dist/apps/\<app-name\> directory.  
  * **CDN/Edge:** Firebase Hosting automatically integrates with Google's global CDN.  
* **Frontend Deployment (Mobile Apps \- Agent & Buyer Mobile Apps):**  
  * **Platform:** Expo Application Services (EAS Build).  
  * **Build Command:** npx nx run \<app-name\>:build.  
  * **Deployment Method:** Manual submission to Google Play Store and Apple App Store.  
* **Backend Deployment (Node.js/Express API):**  
  * **Platform:** Google Cloud Functions (via Firebase Functions).  
  * **Build Command:** npx nx build backend.  
  * **Deployment Method:** Firebase CLI deployment via CI/CD.

### **CI/CD Pipeline**

`# .github/workflows/ci.yaml`  
`name: CI/CD Pipeline`

`on:`  
  `push:`  
    `branches:`  
      `- main`  
      `- develop`  
  `pull_request:`  
    `branches:`  
      `- main`  
      `- develop`

`jobs:`  
  `build-and-test:`  
    `runs-on: ubuntu-latest`  
    `steps:`  
      `- name: Checkout Repository`  
        `uses: actions/checkout@v3`  
        `with:`  
          `fetch-depth: 0 # Important for Nx affected commands`

      `- name: Setup Node.js`  
        `uses: actions/setup-node@v3`  
        `with:`  
          `node-version: '20' # As per Tech Stack`  
          `cache: 'pnpm'`

      `- name: Install pnpm`  
        `run: npm install -g pnpm`

      `- name: Setup pnpm for hoisted node_modules`  
        `run: echo "node-linker=hoisted" >> .npmrc`

      `- name: Install Monorepo Dependencies`  
        `run: pnpm install --frozen-lockfile`

      `- name: Cache Nx Build Results`  
        `uses: actions/cache@v3`  
        `with:`  
          `path: |`  
            `.nx/cache`  
            `./node_modules/.cache/nx`  
            `./dist`  
          `key: ${{ runner.os }}-nx-${{ hashFiles('**/pnpm-lock.yaml') }}`  
          `restore-keys: |`  
            `${{ runner.os }}-nx-`

      `- name: Login to Nx Cloud`  
        `run: npx nx-cloud start-ci-run --stop-agents-after="build" --github-token=${{ secrets.GITHUB_TOKEN }}`  
        `env:`  
          `NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }} # Ensure this secret is configured`

      `- name: Run Linting for Affected Projects`  
        `run: npx nx affected --target=lint --parallel --max-parallel=3`

      `- name: Run Tests for Affected Projects`  
        `run: npx nx affected --target=test --parallel --max-parallel=3`

      `- name: Build Affected Web Applications`  
        `run: npx nx affected --target=build --base=main --head=HEAD --exclude=backend,*-mobile --parallel --max-parallel=2`

      `- name: Build Backend API (Firebase Functions)`  
        `run: npx nx build backend`

      `- name: Setup Firebase CLI`  
        `run: npm install -g firebase-tools`

      `- name: Deploy Firebase Functions (Backend API)`  
        `if: github.ref == 'refs/heads/main'`  
        `run: firebase deploy --only functions --project ${{ secrets.FIREBASE_PROJECT_ID }}`  
        `env:`  
          `FIREBASE_TOKEN: ${{ secrets.FIREBASE_DEPLOY_TOKEN }}`

      `- name: Deploy Admin Web Dashboard`  
        `if: github.ref == 'refs/heads/main'`  
        `run: firebase deploy --only hosting:admin-web --project ${{ secrets.FIREBASE_PROJECT_ID }}`  
        `env:`  
          `FIREBASE_TOKEN: ${{ secrets.FIREBASE_DEPLOY_TOKEN }}`

      `- name: Deploy Super Admin Web Dashboard`  
        `if: github.ref == 'refs/heads/main'`  
        `run: firebase deploy --only hosting:super-admin-web --project ${{ secrets.FIREBASE_PROJECT_ID }}`  
        `env:`  
          `FIREBASE_TOKEN: ${{ secrets.FIREBASE_DEPLOY_TOKEN }}`

      `- name: Install EAS CLI`  
        `run: npm install -g eas-cli`  
          
      `- name: Log in to EAS (for mobile app builds)`  
        `run: eas login --non-interactive -u ${{ secrets.EAS_USERNAME }} -p ${{ secrets.EAS_PASSWORD }}`  
        `if: github.ref == 'refs/heads/main'`  
        `env:`  
            `EAS_USERNAME: ${{ secrets.EAS_USERNAME }}`  
            `EAS_PASSWORD: ${{ secrets.EAS_PASSWORD }}`

      `- name: Trigger EAS Build for Agent Mobile App`  
        `run: eas build --platform all --profile production --non-interactive --local --output apps/agent-mobile/build.zip --json --no-wait --override-local-build-store`  
        `if: github.ref == 'refs/heads/main'`  
        `working-directory: apps/agent-mobile`  
          
      `- name: Trigger EAS Build for Buyer Mobile App`  
        `run: eas build --platform all --profile production --non-interactive --local --output apps/buyer-mobile/build.zip --json --no-wait --override-local-build-store`  
        `if: github.ref == 'refs/heads/main'`  
        `working-directory: apps/buyer-mobile`

      `- name: Stop Nx Cloud CI Run`  
        `if: always()`  
        `run: npx nx-cloud stop-ci-run`

### **Environments**

| Environment | Frontend URL | Backend URL | Purpose |
| :---- | :---- | :---- | :---- |
| Development | http://localhost:\<port\> (for web) / Expo Go (for mobile) | http://localhost:\<port\> | Local development and testing by individual developers. |
| Staging | https://staging-admin.nasaga.com (example) | https://us-central1-\<project-id\>.cloudfunctions.net/api-staging (example) | Pre-production testing, QA, and stakeholder review. |
| Production | https://admin.nasaga.com (example) | https://us-central1-\<project-id\>.cloudfunctions.net/api (example) | Live environment for end-users. |

## **Security and Performance**

### **Security Requirements**

* **Frontend Security:**  
  * **CSP Headers:** Content Security Policy (CSP) headers will be implemented for web applications.  
  * **XSS Prevention:** All user-generated content rendered on the frontend will be properly sanitized or escaped.  
  * **Secure Storage:** Sensitive data will be stored securely using appropriate mechanisms.  
* **Backend Security:**  
  * **Input Validation:** All incoming data to the backend API will be rigorously validated.  
  * **Rate Limiting:** Rate limiting will be implemented on API endpoints.  
  * **CORS Policy:** Cross-Origin Resource Sharing (CORS) will be strictly configured.  
* **Authentication Security:**  
  * **Token Storage:** Firebase ID tokens will be handled securely.  
  * **Session Management:** Firebase Authentication handles session management securely.  
  * **Password Policy:** Firebase Authentication enforces strong password policies.

### **Performance Optimization**

* **Frontend Performance:**  
  * **Bundle Size Target:** Aim for optimized bundle sizes.  
  * **Loading Strategy:** Implement lazy loading for routes and components.  
  * **Caching Strategy:** Leverage browser caching and application-level caching.  
* **Backend Performance:**  
  * **Response Time Target:** Aim for low latency API responses.  
  * **Database Optimization:** Optimize Firestore queries using appropriate indexing.  
  * **Caching Strategy:** Implement server-side caching mechanisms for frequently accessed data.

## **Testing Strategy**

### **Testing Pyramid**

`E2E Tests`  
       `/        \`  
  `Integration Tests`  
     `/            \`  
`Frontend Unit  Backend Unit`

### **Test Organization**

#### **Frontend Tests**

`apps/`  
`├── admin-web/`  
`│   └── src/`  
`│       ├── components/`  
`│       │   └── Button/`  
`│       │       └── Button.test.tsx # Unit test for Button component`  
`│       ├── pages/`  
`│       │   └── Dashboard/`  
`│       │       └── DashboardPage.test.tsx # Integration test for Dashboard page`  
`│       └── tests/                  # Higher-level integration/setup tests`  
`├── agent-mobile/`  
`│   └── src/`  
`│       ├── components/`  
`│       │   └── PropertyCard/`  
`│       │       └── PropertyCard.test.tsx # Unit test for PropertyCard`  
`│       ├── screens/`  
`│       │   └── CreateProperty/`  
`│       │       └── CreatePropertyScreen.test.tsx # Integration test for screen`  
`│       └── tests/`  
`# ... similar for super-admin-web and buyer-mobile`

#### **Backend Tests**

`apps/`  
`└── backend/`  
    `└── tests/`  
        `├── unit/                   # Unit tests for services, models, utils`  
        `│   ├── services/`  
        `│   │   └── property.service.test.ts`  
        `│   └── models/`  
        `│       └── user.model.test.ts`  
        `└── integration/            # Integration tests for controllers, API routes`  
            `└── controllers/`  
                `└── auth.controller.test.ts`

#### **E2E Tests**

`apps/`  
`├── admin-web-e2e/                  # Cypress or Playwright E2E tests for Admin Web`  
`│   └── src/`  
`│       ├── e2e/`  
`│       │   └── login.cy.ts # E2E test for login flow`  
`│       └── support/`  
`│           └── commands.ts`  
`│   └── project.json`  
`# ... similar for other apps if needed`

### **Test Examples**

#### **Frontend Component Test**

`// apps/admin-web/src/components/Button/Button.test.tsx (as previously defined)`  
`import React from 'react';`  
`import { render, screen, fireEvent } from '@testing-library/react';`  
`import Button from './Button';`

`describe('Button', () => {`  
  `it('renders correctly with children', () => {`  
    `render(<Button onClick={() => {}}>Click Me</Button>);`  
    `expect(screen.getByText(/Click Me/i)).toBeInTheDocument();`  
  `});`

  `it('calls onClick when clicked', () => {`  
    `const handleClick = jest.fn();`  
    `render(<Button onClick={handleClick}>Test Button</Button>);`  
    `fireEvent.click(screen.getByText(/Test Button/i));`  
    `expect(handleClick).toHaveBeenCalledTimes(1);`  
  `});`  
`});`

#### **Backend API Test**

`// apps/backend/tests/integration/controllers/auth.controller.test.ts`  
`import request from 'supertest';`  
`import express from 'express';`  
`import { authController } from '../../../src/controllers/auth.controller'; // Adjust path`  
`import { verifyFirebaseToken } from '../../../src/middleware/auth.middleware'; // Adjust path`  
`import { UserModel } from '../../../src/models/user.model'; // Mock this`

`// Mock Firebase Admin SDK and Firestore`  
`jest.mock('firebase-admin/auth', () => ({`  
  `getAuth: () => ({`  
    `verifyIdToken: jest.fn((token) => {`  
      `if (token === 'valid-token-superadmin') return { uid: 'superadmin123', email: '<EMAIL>' };`  
      `if (token === 'valid-token-admin') return { uid: 'admin456', email: '<EMAIL>' };`  
      `if (token === 'valid-token-agent') return { uid: 'agent789', email: '<EMAIL>' };`  
      `return Promise.reject(new Error('Invalid token'));`  
    `}),`  
  `}),`  
`}));`

`jest.mock('../../../src/models/user.model', () => ({`  
  `UserModel: {`  
    `getUserByUid: jest.fn((uid) => {`  
      `if (uid === 'superadmin123') return Promise.resolve({ uid, email: '<EMAIL>', role: 'superAdmin' });`  
      `if (uid === 'admin456') return Promise.resolve({ uid, email: '<EMAIL>', role: 'admin' });`  
      `if (uid === 'agent789') return Promise.resolve({ uid, email: '<EMAIL>', role: 'agent' });`  
      `return Promise.resolve(null);`  
    `}),`  
  `},`  
`}));`

`// Create a test Express app`  
`const app = express();`  
`app.use(express.json()); // For parsing request body`  
`app.patch('/admin/users/:uid/role', verifyFirebaseToken, authController.updateUserRole);`

`describe('Auth Controller Integration Tests', () => {`  
  `describe('PATCH /admin/users/:uid/role', () => {`  
    `it('should allow superAdmin to update user role to admin', async () => {`  
      `const targetUid = 'someUserUid';`  
      `const newRole = 'admin';`

      `// Mock user model update for this specific test`  
      `(UserModel.updateUserRole as jest.Mock).mockResolvedValueOnce({`  
        `uid: targetUid, email: '<EMAIL>', role: newRole`  
      `});`

      `const response = await request(app)`  
        ``.patch(`/admin/users/${targetUid}/role`)``  
        `.set('Authorization', 'Bearer valid-token-superadmin')`  
        `.send({ role: newRole });`

      `expect(response.statusCode).toEqual(200);`  
      `expect(response.body.uid).toEqual(targetUid);`  
      `expect(response.body.role).toEqual(newRole);`  
      `expect(UserModel.updateUserRole).toHaveBeenCalledWith(targetUid, newRole);`  
    `});`

    `it('should forbid admin from updating user role', async () => {`  
      `const targetUid = 'someUserUid';`  
      `const newRole = 'superAdmin';`

      `const response = await request(app)`  
        ``.patch(`/admin/users/${targetUid}/role`)``  
        `.set('Authorization', 'Bearer valid-token-admin')`  
        `.send({ role: newRole });`

      `expect(response.statusCode).toEqual(403);`  
      `expect(response.body.message).toContain('Forbidden');`  
    `});`

    `it('should return 401 if no token provided', async () => {`  
      `const response = await request(app)`  
        `.patch('/admin/users/someUser/role')`  
        `.send({ role: 'admin' });`

      `expect(response.statusCode).toEqual(401);`  
      `expect(response.body.message).toContain('Unauthorized');`  
    `});`  
  `});`  
`});`

#### **E2E Test**

`// apps/admin-web-e2e/src/e2e/login.cy.ts (Cypress example for web)`  
`describe('Admin Login Flow', () => {`  
  `beforeEach(() => {`  
    `cy.visit('/login'); // Assuming the login page is at /login`  
  `});`

  `it('should allow admin to log in successfully and redirect to dashboard', () => {`  
    `// Mock Firebase login (important for E2E tests to control external dependencies)`  
    `cy.intercept('POST', '[https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=](https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=)*', {`  
      `statusCode: 200,`  
      `body: {`  
        `idToken: 'mock-admin-id-token',`  
        `email: '<EMAIL>',`  
        `refreshToken: 'mock-refresh-token',`  
        `expiresIn: '3600',`  
        `localId: 'mock-admin-uid',`  
      `},`  
    `}).as('firebaseLogin');`

    `// Mock Firestore user role fetch (if done via client or simple lookup)`  
    `// Or mock backend API call if role fetch goes through Node.js API`  
    `cy.intercept('GET', '**/users/mock-admin-uid', {`  
      `statusCode: 200,`  
      `body: {`  
        `uid: 'mock-admin-uid',`  
        `email: '<EMAIL>',`  
        `displayName: 'Admin User',`  
        `role: 'admin',`  
      `},`  
    `}).as('fetchUserRole');`

    `cy.get('input[name="email"]').type('<EMAIL>');`  
    `cy.get('input[name="password"]').type('adminpassword');`  
    `cy.get('button[type="submit"]').click();`

    `cy.wait('@firebaseLogin');`  
    `cy.wait('@fetchUserRole');`

    `// Verify redirection to dashboard`  
    `cy.url().should('include', '/dashboard');`  
    `cy.contains('h1', 'Admin Dashboard'); // Check for content on the dashboard page`  
  `});`

  `it('should display error message for invalid credentials', () => {`  
    `// Simulate Firebase login failure`  
    `cy.intercept('POST', '[https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=](https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=)*', {`  
      `statusCode: 400,`  
      `body: {`  
        `error: {`  
          `code: 400,`  
          `message: 'EMAIL_NOT_FOUND',`  
          `errors: [{ message: 'EMAIL_NOT_FOUND', domain: 'global', reason: 'invalid' }]`  
        `}`  
      `},`  
    `}).as('firebaseLoginError');`

    `cy.get('input[name="email"]').type('<EMAIL>');`  
    `cy.get('input[name="password"]').type('wrongpassword');`  
    `cy.get('button[type="submit"]').click();`

    `cy.wait('@firebaseLoginError');`  
    `cy.contains('Invalid credentials. Please try again.').should('be.visible'); // Check for error message`  
    `cy.url().should('include', '/login'); // Ensure still on login page`  
  `});`  
`});`

## **Coding Standards**

### **Critical Fullstack Rules**

* **Type Sharing:** Always define shared TypeScript types and interfaces in packages/types and import them from there. Never duplicate type definitions across applications.  
* **API Calls:** Never make direct HTTP calls (fetch or axios.create instances) from components or pages/screens. Always use the dedicated service layer (e.g., propertyService.ts, userService.ts) from packages/utils or app-specific services/ folders to encapsulate API interactions.  
* **Environment Variables:** Access environment variables only through a centralized configuration object or service within each application. Never access process.env (Node.js) or Expo.Constants.manifest.extra (Expo) directly within application logic to ensure consistency and testability.  
* **Error Handling:** All API routes on the backend must use the standard error handler defined in apps/backend/src/middleware/error.middleware.ts (to be created). Frontend API calls must utilize the global error handling defined in packages/utils/src/apiClient.ts.  
* **State Updates:** Never mutate state directly in React/React Native. Always use proper state management patterns (e.g., setState for useState, immutability helpers for Zustand actions) to ensure predictable state changes and prevent bugs.  
* **Firebase SDK Usage:**  
  * **Client SDKs:** Use Firebase Client SDKs (Auth, Firestore, Storage) directly in frontend applications for real-time data synchronization and client-side operations.  
  * **Admin SDK:** Use Firebase Admin SDK only in the backend Node.js/Express application for privileged operations (e.g., setting user roles, modifying sensitive data, triggering Cloud Messaging).  
* **UUID Generation:** For unique client-side IDs (e.g., before saving a new document to Firestore), use a consistent UUID generation library (e.g., uuid npm package).

### **Naming Conventions**

| Element | Frontend | Backend | Example |
| :---- | :---- | :---- | :---- |
| Components | PascalCase | \- | UserProfile.tsx |
| Hooks | camelCase with 'use' prefix | \- | useAuth.ts |
| Pages/Screens | PascalCase | \- | LoginPage.tsx, HomeScreen.tsx |
| Services | camelCase ending with Service | camelCase ending with Service | userService.ts, propertyService.ts |
| API Routes | \- | kebab-case | /api/user-profile |
| Database Tables (Collections) | \- | snake\_case (Firestore collections are typically camelCase or kebab-case by convention, but mapping to snake\_case in code if interacting with a relational database or for consistency with existing backend patterns) | user\_profiles (for logical naming, Firestore collections typically users, properties) |
| Types/Interfaces | PascalCase | PascalCase | User, Property |

## **Error Handling Strategy**

### **Error Flow**

`sequenceDiagram`  
    `actor User`  
    `participant ClientApp[Client App (Web/Mobile)]`  
    `participant APIClient[Frontend API Client]`  
    `participant BackendAPI[Backend API (Node.js/Express)]`  
    `participant BackendService[Backend Service Layer]`  
    `participant BackendModel[Backend Data Model (Firestore)]`  
    `participant ErrorMiddleware[Error Handling Middleware]`  
    `participant LoggingService[Logging Service]`

    `User->>ClientApp: 1. User Action (e.g., Submit Form)`  
    `ClientApp->>APIClient: 2. Initiates API Request`  
    `APIClient->>BackendAPI: 3. Sends HTTP Request`  
    `BackendAPI->>BackendService: 4. Delegates to Service`  
    `BackendService->>BackendModel: 5. Attempts Data Operation`  
    `BackendModel--xBackendService: 6. Throws Database Error (e.g., Validation, Permissions)`  
    `BackendService--xBackendAPI: 7. Throws Business Logic/Service Error`  
    `BackendAPI--xErrorMiddleware: 8. Express catches error, passes to Error Middleware`  
    `ErrorMiddleware->>LoggingService: 9. Logs Error Details (e.g., Google Cloud Logging)`  
    `LoggingService-->>ErrorMiddleware: 10. Log Acknowledged`  
    `ErrorMiddleware-->>BackendAPI: 11. Formats Error Response (e.g., ApiError interface)`  
    `BackendAPI--xAPIClient: 12. Sends Error HTTP Response (e.g., 400, 401, 403, 500)`  
    `APIClient--xClientApp: 13. Processes Error Response (e.g., global interceptor)`  
    `ClientApp->>User: 14. Displays User-Friendly Error Message`

### **Error Response Format**

`// packages/types/src/api.d.ts (or error.d.ts)`  
`interface ApiErrorDetail {`  
  `field?: string; // Optional: specific field that caused the error (for validation errors)`  
  `message: string; // Human-readable message for this specific detail`  
  `code?: string; // Optional: specific error code (e.g., 'INVALID_EMAIL', 'PROPERTY_NOT_FOUND')`  
`}`

`export interface ApiResponseError {`  
  `error: {`  
    `code: string; // A general error code (e.g., 'VALIDATION_ERROR', 'UNAUTHORIZED', 'SERVER_ERROR')`  
    `message: string; // A general human-readable message for the error type`  
    `details?: ApiErrorDetail[]; // Optional: array of specific error details (e.g., validation errors per field)`  
    `timestamp: string; // ISO 8601 timestamp of when the error occurred`  
    `requestId?: string; // Optional: Correlation ID for tracing requests through logs`  
  `};`  
`}`

### **Frontend Error Handling**

`// packages/utils/src/apiClient.ts (Response Interceptor - previously defined, here with error formatting)`  
`import axios, { AxiosError } from 'axios';`  
`import { getAuth } from 'firebase/auth';`  
`import { ApiResponseError } from '@nasaga/types'; // Import the defined error interface`

`const apiClient = axios.create({`  
  `baseURL: '[https://api.nasaga.com/v1](https://api.nasaga.com/v1)',`  
  `headers: { 'Content-Type': 'application/json' },`  
`});`

`apiClient.interceptors.request.use(async (config) => {`  
  `const auth = getAuth();`  
  `const user = auth.currentUser;`  
  `if (user) {`  
    `const token = await user.getIdToken();`  
    ``config.headers.Authorization = `Bearer ${token}`;``  
  `}`  
  `return config;`  
`});`

`apiClient.interceptors.response.use(`  
  `(response) => response,`  
  `(error: AxiosError<ApiResponseError>) => {`  
    `let errorMessage = 'An unexpected error occurred.';`  
    `let errorCode = 'UNKNOWN_ERROR';`  
    `let errorDetails: ApiErrorDetail[] | undefined;`

    `if (error.response && error.response.data && error.response.data.error) {`  
      `const apiError = error.response.data.error;`  
      `errorCode = apiError.code || errorCode;`  
      `errorMessage = apiError.message || errorMessage;`  
      `errorDetails = apiError.details;`

      `// Log specific backend errors for debugging`  
      ``console.error(`Backend Error [${errorCode}]: ${errorMessage}`, apiError.details);``

      `// Special handling for specific error codes if needed (e.g., unauthorized)`  
      `if (errorCode === 'UNAUTHORIZED') {`  
        `// Redirect to login or trigger global logout`  
        `// Example: logoutUser();`  
      `}`  
    `} else if (error.request) {`  
      `errorMessage = 'Network error: No response from server. Please check your internet connection.';`  
      `errorCode = 'NETWORK_ERROR';`  
      `console.error('Network Error:', error.request);`  
    `} else {`  
      ``errorMessage = `Request Setup Error: ${error.message}`;``  
      `errorCode = 'CLIENT_ERROR';`  
      `console.error('Client-side error:', error.message);`  
    `}`

    `// You can re-throw a custom error or just the AxiosError for components to catch`  
    `return Promise.reject(error); // Or new CustomAppError(errorMessage, errorCode, errorDetails);`  
  `}`  
`);`

`export default apiClient;`

`// apps/admin-web/src/utils/errorHandler.ts (Helper for displaying errors in UI)`  
`import { AxiosError } from 'axios';`  
`import { ApiResponseError, ApiErrorDetail } from '@nasaga/types';`

`export const getErrorMessage = (error: unknown): string => {`  
  `if (axios.isAxiosError(error) && error.response && error.response.data) {`  
    `const apiError = error.response.data as ApiResponseError;`  
    `if (apiError.error?.details && apiError.error.details.length > 0) {`  
      `// Concatenate detailed messages for user display`  
      `return apiError.error.details.map(d => d.message).join('; ') + '.';`  
    `}`  
    `return apiError.error?.message || 'An API error occurred.';`  
  `}`  
  `if (error instanceof Error) {`  
    `return error.message;`  
  `}`  
  `return 'An unknown error occurred.';`  
`};`

`// Example usage in a React component`  
`/*`  
`import React, { useState } from 'react';`  
`import { propertyService } from '../services/propertyService';`  
`import { getErrorMessage } from '../utils/errorHandler';`

`const CreatePropertyForm = () => {`  
  `const [error, setError] = useState<string | null>(null);`

  `const handleSubmit = async (formData: any) => {`  
    `try {`  
      `setError(null);`  
      `await propertyService.createProperty(formData);`  
      `// Success handling`  
    `} catch (err) {`  
      `setError(getErrorMessage(err));`  
    `}`  
  `};`

  `return (`  
    `<div>`  
      `{error && <div className="text-red-500">{error}</div>}`  
      `<form onSubmit={handleSubmit}>`  
        `// Form fields`  
      `</form>`  
    `</div>`  
  `);`  
`};`  
`*/`

### **Backend Error Handling**

`// apps/backend/src/middleware/error.middleware.ts`  
`import { Request, Response, NextFunction } from 'express';`  
`import { ApiResponseError, ApiErrorDetail } from '@nasaga/types'; // Import shared types`  
`import { logError } from '../utils/logger'; // Assuming a simple logger utility`

`// Define a custom error class for known application errors`  
`export class AppError extends Error {`  
  `public statusCode: number;`  
  `public code: string;`  
  `public details?: ApiErrorDetail[];`

  `constructor(message: string, statusCode: number = 500, code: string = 'SERVER_ERROR', details?: ApiErrorDetail[]) {`  
    `super(message);`  
    `this.name = this.constructor.name;`  
    `this.statusCode = statusCode;`  
    `this.code = code;`  
    `this.details = details;`  
    `Object.setPrototypeOf(this, AppError.prototype); // Proper inheritance for instanceof`  
  `}`  
`}`

`// Centralized error handling middleware`  
`export const globalErrorHandler = (err: any, req: Request, res: Response, next: NextFunction) => {`  
  `let statusCode = err.statusCode || 500;`  
  `let message = err.message || 'An unexpected server error occurred.';`  
  `let code = err.code || 'SERVER_ERROR';`  
  `let details = err.details as ApiErrorDetail[] | undefined;`

  `// Handle specific known error types`  
  `if (err.name === 'ValidationError') { // Example for input validation errors (e.g., Joi/Zod)`  
    `statusCode = 400;`  
    `code = 'VALIDATION_ERROR';`  
    `message = 'Validation failed.';`  
    `details = err.details?.map((detail: any) => ({`  
      `field: detail.context?.key || detail.path?.join('.'),`  
      `message: detail.message,`  
      `code: detail.type,`  
    `})) || [{ message: err.message }];`  
  `} else if (err.name === 'FirebaseError') {`  
    `// Handle Firebase specific errors (e.g., Firestore permission denied)`  
    `statusCode = 500; // Default, might be more specific like 403 or 404`  
    ``code = `FIREBASE_${err.code?.toUpperCase() || 'ERROR'}`;``  
    ``message = `Firebase operation failed: ${err.message}`;``  
  `} else if (err.message.includes('Unauthorized')) { // Simple check for auth errors`  
    `statusCode = 401;`  
    `code = 'UNAUTHORIZED';`  
    `message = 'Authentication required.';`  
  `} else if (err.message.includes('Forbidden')) {`  
    `statusCode = 403;`  
    `code = 'FORBIDDEN';`  
    `message = 'You do not have permission to perform this action.';`  
  `} else if (err instanceof AppError) {`  
    `// Custom AppError instances`  
    `statusCode = err.statusCode;`  
    `code = err.code;`  
    `message = err.message;`  
    `details = err.details;`  
  `}`

  `// Log the error (excluding sensitive details in production logs if not needed)`  
  ``logError(`[${req.method}] ${req.originalUrl} - Status: ${statusCode} - Error: ${message}`, err.stack, req.body);``

  `const errorResponse: ApiResponseError = {`  
    `error: {`  
      `code,`  
      `message,`  
      `details,`  
      `timestamp: new Date().toISOString(),`  
      `requestId: (req as any).id, // Assuming a request ID middleware is used`  
    `},`  
  `};`

  `res.status(statusCode).json(errorResponse);`  
`};`

`/*`  
`// apps/backend/src/app.ts (Integration into Express app)`  
`import express from 'express';`  
`import { globalErrorHandler } from './middleware/error.middleware';`  
`import authRoutes from './routes/auth.routes';`  
`import propertyRoutes from './routes/properties.routes';`

`const app = express();`  
`app.use(express.json());`

`// Routes`  
`app.use('/auth', authRoutes);`  
`app.use('/properties', propertyRoutes);`

`// Catch-all for unhandled routes`  
`app.use((req, res, next) => {`  
  `const error = new AppError('Not Found', 404, 'NOT_FOUND');`  
  `next(error);`  
`});`

`// Global error handler MUST be the last middleware`  
`app.use(globalErrorHandler);`

`export default app;`  
`*/`

## **Monitoring and Observability**

### **Monitoring Stack**

* **Frontend Monitoring:**  
  * **Firebase Performance Monitoring:** For automatic collection of performance data from mobile and web apps.  
  * **Google Analytics for Firebase:** For user engagement and behavioral analytics.  
* **Backend Monitoring:**  
  * **Google Cloud Monitoring (formerly Stackdriver Monitoring):** For monitoring Firebase Functions performance, uptime, and resource utilization.  
  * **Google Cloud Logging (formerly Stackdriver Logging):** For centralized logging of all backend function invocations, errors, and custom logs.  
* **Error Tracking:**  
  * **Firebase Crashlytics:** For real-time crash reporting and error tracking in mobile applications.  
  * **Google Cloud Error Reporting:** For centralized error reporting from the backend API.  
* **Performance Monitoring:**  
  * **Firebase Performance Monitoring:** As above, for client-side performance.  
  * **Google Cloud Trace:** For distributed tracing across Google Cloud Functions.

### **Key Metrics**

* **Frontend Metrics:**  
  * **Core Web Vitals:** (LCP, FID, CLS) for web app user experience performance.  
  * **JavaScript errors:** Number of unhandled exceptions and errors on client-side.  
  * **API response times (client-side):** Latency of calls from frontend to backend.  
  * **User interactions:** Key actions performed.  
  * **App launch time (mobile):** Time taken for the mobile app to become interactive.  
  * **Screen rendering times (mobile):** Performance of individual screens.  
* **Backend Metrics:**  
  * **Request rate:** Number of API requests per second/minute.  
  * **Error rate:** Percentage of API requests resulting in errors.  
  * **Response time:** Latency of API endpoints.  
  * **Database query performance:** Read/write latency and throughput for Firestore operations.  
  * **Function invocations and execution time:** For individual Firebase Functions.  
  * **Cold starts (serverless):** Frequency and duration of cold starts for Firebase Functions.

## **Checklist Results Report**