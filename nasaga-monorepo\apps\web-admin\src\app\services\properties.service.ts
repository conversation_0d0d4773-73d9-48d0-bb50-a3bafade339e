import { getFirebaseFirestore } from '@nasaga-monorepo/utils';
import { 
  collection, 
  doc, 
  getDocs, 
  query, 
  where, 
  orderBy, 
  runTransaction,
  Timestamp 
} from 'firebase/firestore';
import { 
  Property, 
  PropertyStatus, 
  PropertyStatusUpdate,
  PropertyFilters,
  needsAdminApproval
} from '@nasaga-monorepo/models';

export interface PropertiesServiceResponse {
  success: boolean;
  data?: Property[];
  error?: string;
}

export interface PropertyStatusUpdateResponse {
  success: boolean;
  data?: Property;
  error?: string;
}

export class PropertiesService {
  private firestore = getFirebaseFirestore();
  private propertiesCollection = collection(this.firestore, 'properties');

  /**
   * Get all properties with optional filtering
   */
  async getProperties(filters?: PropertyFilters): Promise<PropertiesServiceResponse> {
    try {
      let q = query(this.propertiesCollection, orderBy('createdAt', 'desc'));
      
      // Apply filters
      if (filters?.status && filters.status.length > 0) {
        q = query(q, where('status', 'in', filters.status));
      }
      
      if (filters?.city) {
        q = query(q, where('city', '==', filters.city));
      }
      
      if (filters?.propertyType) {
        q = query(q, where('propertyType', '==', filters.propertyType));
      }

      if (filters?.agentUid) {
        q = query(q, where('agentUid', '==', filters.agentUid));
      }

      const snapshot = await getDocs(q);
      const properties = snapshot.docs.map(doc => ({
        propertyId: doc.id,
        ...doc.data()
      } as Property));

      // Apply price filters (client-side since Firestore doesn't support range queries with other filters)
      let filteredProperties = properties;
      
      if (filters?.minPrice) {
        filteredProperties = filteredProperties.filter(p => p.price >= filters.minPrice!);
      }
      
      if (filters?.maxPrice) {
        filteredProperties = filteredProperties.filter(p => p.price <= filters.maxPrice!);
      }

      return {
        success: true,
        data: filteredProperties
      };
    } catch (error) {
      console.error('Error fetching properties:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch properties'
      };
    }
  }

  /**
   * Get properties that need admin approval
   */
  async getPendingProperties(): Promise<PropertiesServiceResponse> {
    return this.getProperties({ status: ['pending'] });
  }

  /**
   * Update property status using Firestore transaction
   */
  async updatePropertyStatus(
    propertyId: string, 
    newStatus: PropertyStatus, 
    adminUid: string
  ): Promise<PropertyStatusUpdateResponse> {
    try {
      const propertyRef = doc(this.firestore, 'properties', propertyId);
      
      const result = await runTransaction(this.firestore, async (transaction) => {
        const propertyDoc = await transaction.get(propertyRef);
        
        if (!propertyDoc.exists()) {
          throw new Error('Property not found');
        }

        const propertyData = propertyDoc.data() as Property;
        
        // Prepare update data
        const updateData: Partial<Property> = {
          status: newStatus,
          updatedAt: Timestamp.now()
        };

        // If approving a property, record admin approval details
        if (newStatus === 'approved' && propertyData.status === 'pending') {
          updateData.approvedByAdminUid = adminUid;
          updateData.approvedAt = Timestamp.now();
        }

        // Perform the transaction update
        transaction.update(propertyRef, updateData);
        
        return {
          ...propertyData,
          ...updateData,
          propertyId
        } as Property;
      });

      return {
        success: true,
        data: result
      };
    } catch (error) {
      console.error('Error updating property status:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update property status'
      };
    }
  }

  /**
   * Approve a property (convenience method)
   */
  async approveProperty(propertyId: string, adminUid: string): Promise<PropertyStatusUpdateResponse> {
    return this.updatePropertyStatus(propertyId, 'approved', adminUid);
  }

  /**
   * Reject a property by delisting it (convenience method)
   */
  async rejectProperty(propertyId: string, adminUid: string): Promise<PropertyStatusUpdateResponse> {
    return this.updatePropertyStatus(propertyId, 'delisted', adminUid);
  }

  /**
   * Get property statistics for dashboard
   */
  async getPropertyStats(): Promise<{
    total: number;
    pending: number;
    approved: number;
    active: number;
    sold: number;
  }> {
    try {
      const response = await this.getProperties();
      
      if (!response.success || !response.data) {
        return { total: 0, pending: 0, approved: 0, active: 0, sold: 0 };
      }

      const properties = response.data;
      
      return {
        total: properties.length,
        pending: properties.filter(p => p.status === 'pending').length,
        approved: properties.filter(p => p.status === 'approved').length,
        active: properties.filter(p => p.status === 'active').length,
        sold: properties.filter(p => p.status === 'sold').length,
      };
    } catch (error) {
      console.error('Error getting property stats:', error);
      return { total: 0, pending: 0, approved: 0, active: 0, sold: 0 };
    }
  }
}

// Export singleton instance
export const propertiesService = new PropertiesService();
