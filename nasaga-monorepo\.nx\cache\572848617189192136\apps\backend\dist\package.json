{"name": "@nasaga-monorepo/backend", "version": "0.0.1", "private": true, "nx": {"targets": {"serve": {"continuous": true, "executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "@nasaga-monorepo/backend:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "@nasaga-monorepo/backend:build:development"}, "production": {"buildTarget": "@nasaga-monorepo/backend:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}, "dependencies": {"express": "4.21.2"}, "main": "main.js"}