# **Testing Strategy**

## **Testing Pyramid**

`E2E Tests`  
       `/        \`  
  `Integration Tests`  
     `/            \`  
`Frontend Unit  Backend Unit`

## **Test Organization**

### **Frontend Tests**

`apps/`  
`├── admin-web/`  
`│   └── src/`  
`│       ├── components/`  
`│       │   └── Button/`  
`│       │       └── Button.test.tsx # Unit test for Button component`  
`│       ├── pages/`  
`│       │   └── Dashboard/`  
`│       │       └── DashboardPage.test.tsx # Integration test for Dashboard page`  
`│       └── tests/                  # Higher-level integration/setup tests`  
`├── agent-mobile/`  
`│   └── src/`  
`│       ├── components/`  
`│       │   └── PropertyCard/`  
`│       │       └── PropertyCard.test.tsx # Unit test for PropertyCard`  
`│       ├── screens/`  
`│       │   └── CreateProperty/`  
`│       │       └── CreatePropertyScreen.test.tsx # Integration test for screen`  
`│       └── tests/`  
`# ... similar for super-admin-web and buyer-mobile`

### **Backend Tests**

`apps/`  
`└── backend/`  
    `└── tests/`  
        `├── unit/                   # Unit tests for services, models, utils`  
        `│   ├── services/`  
        `│   │   └── property.service.test.ts`  
        `│   └── models/`  
        `│       └── user.model.test.ts`  
        `└── integration/            # Integration tests for controllers, API routes`  
            `└── controllers/`  
                `└── auth.controller.test.ts`

### **E2E Tests**

`apps/`  
`├── admin-web-e2e/                  # Cypress or Playwright E2E tests for Admin Web`  
`│   └── src/`  
`│       ├── e2e/`  
`│       │   └── login.cy.ts # E2E test for login flow`  
`│       └── support/`  
`│           └── commands.ts`  
`│   └── project.json`  
`# ... similar for other apps if needed`

## **Test Examples**

### **Frontend Component Test**

`// apps/admin-web/src/components/Button/Button.test.tsx (as previously defined)`  
`import React from 'react';`  
`import { render, screen, fireEvent } from '@testing-library/react';`  
`import Button from './Button';`

`describe('Button', () => {`  
  `it('renders correctly with children', () => {`  
    `render(<Button onClick={() => {}}>Click Me</Button>);`  
    `expect(screen.getByText(/Click Me/i)).toBeInTheDocument();`  
  `});`

  `it('calls onClick when clicked', () => {`  
    `const handleClick = jest.fn();`  
    `render(<Button onClick={handleClick}>Test Button</Button>);`  
    `fireEvent.click(screen.getByText(/Test Button/i));`  
    `expect(handleClick).toHaveBeenCalledTimes(1);`  
  `});`  
`});`

### **Backend API Test**

`// apps/backend/tests/integration/controllers/auth.controller.test.ts`  
`import request from 'supertest';`  
`import express from 'express';`  
`import { authController } from '../../../src/controllers/auth.controller'; // Adjust path`  
`import { verifyFirebaseToken } from '../../../src/middleware/auth.middleware'; // Adjust path`  
`import { UserModel } from '../../../src/models/user.model'; // Mock this`

`// Mock Firebase Admin SDK and Firestore`  
`jest.mock('firebase-admin/auth', () => ({`  
  `getAuth: () => ({`  
    `verifyIdToken: jest.fn((token) => {`  
      `if (token === 'valid-token-superadmin') return { uid: 'superadmin123', email: '<EMAIL>' };`  
      `if (token === 'valid-token-admin') return { uid: 'admin456', email: '<EMAIL>' };`  
      `if (token === 'valid-token-agent') return { uid: 'agent789', email: '<EMAIL>' };`  
      `return Promise.reject(new Error('Invalid token'));`  
    `}),`  
  `}),`  
`}));`

`jest.mock('../../../src/models/user.model', () => ({`  
  `UserModel: {`  
    `getUserByUid: jest.fn((uid) => {`  
      `if (uid === 'superadmin123') return Promise.resolve({ uid, email: '<EMAIL>', role: 'superAdmin' });`  
      `if (uid === 'admin456') return Promise.resolve({ uid, email: '<EMAIL>', role: 'admin' });`  
      `if (uid === 'agent789') return Promise.resolve({ uid, email: '<EMAIL>', role: 'agent' });`  
      `return Promise.resolve(null);`  
    `}),`  
  `},`  
`}));`

`// Create a test Express app`  
`const app = express();`  
`app.use(express.json()); // For parsing request body`  
`app.patch('/admin/users/:uid/role', verifyFirebaseToken, authController.updateUserRole);`

`describe('Auth Controller Integration Tests', () => {`  
  `describe('PATCH /admin/users/:uid/role', () => {`  
    `it('should allow superAdmin to update user role to admin', async () => {`  
      `const targetUid = 'someUserUid';`  
      `const newRole = 'admin';`

      `// Mock user model update for this specific test`  
      `(UserModel.updateUserRole as jest.Mock).mockResolvedValueOnce({`  
        `uid: targetUid, email: '<EMAIL>', role: newRole`  
      `});`

      `const response = await request(app)`  
        ``.patch(`/admin/users/${targetUid}/role`)``  
        `.set('Authorization', 'Bearer valid-token-superadmin')`  
        `.send({ role: newRole });`

      `expect(response.statusCode).toEqual(200);`  
      `expect(response.body.uid).toEqual(targetUid);`  
      `expect(response.body.role).toEqual(newRole);`  
      `expect(UserModel.updateUserRole).toHaveBeenCalledWith(targetUid, newRole);`  
    `});`

    `it('should forbid admin from updating user role', async () => {`  
      `const targetUid = 'someUserUid';`  
      `const newRole = 'superAdmin';`

      `const response = await request(app)`  
        ``.patch(`/admin/users/${targetUid}/role`)``  
        `.set('Authorization', 'Bearer valid-token-admin')`  
        `.send({ role: newRole });`

      `expect(response.statusCode).toEqual(403);`  
      `expect(response.body.message).toContain('Forbidden');`  
    `});`

    `it('should return 401 if no token provided', async () => {`  
      `const response = await request(app)`  
        `.patch('/admin/users/someUser/role')`  
        `.send({ role: 'admin' });`

      `expect(response.statusCode).toEqual(401);`  
      `expect(response.body.message).toContain('Unauthorized');`  
    `});`  
  `});`  
`});`

### **E2E Test**

`// apps/admin-web-e2e/src/e2e/login.cy.ts (Cypress example for web)`  
`describe('Admin Login Flow', () => {`  
  `beforeEach(() => {`  
    `cy.visit('/login'); // Assuming the login page is at /login`  
  `});`

  `it('should allow admin to log in successfully and redirect to dashboard', () => {`  
    `// Mock Firebase login (important for E2E tests to control external dependencies)`  
    `cy.intercept('POST', '[https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=](https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=)*', {`  
      `statusCode: 200,`  
      `body: {`  
        `idToken: 'mock-admin-id-token',`  
        `email: '<EMAIL>',`  
        `refreshToken: 'mock-refresh-token',`  
        `expiresIn: '3600',`  
        `localId: 'mock-admin-uid',`  
      `},`  
    `}).as('firebaseLogin');`

    `// Mock Firestore user role fetch (if done via client or simple lookup)`  
    `// Or mock backend API call if role fetch goes through Node.js API`  
    `cy.intercept('GET', '**/users/mock-admin-uid', {`  
      `statusCode: 200,`  
      `body: {`  
        `uid: 'mock-admin-uid',`  
        `email: '<EMAIL>',`  
        `displayName: 'Admin User',`  
        `role: 'admin',`  
      `},`  
    `}).as('fetchUserRole');`

    `cy.get('input[name="email"]').type('<EMAIL>');`  
    `cy.get('input[name="password"]').type('adminpassword');`  
    `cy.get('button[type="submit"]').click();`

    `cy.wait('@firebaseLogin');`  
    `cy.wait('@fetchUserRole');`

    `// Verify redirection to dashboard`  
    `cy.url().should('include', '/dashboard');`  
    `cy.contains('h1', 'Admin Dashboard'); // Check for content on the dashboard page`  
  `});`

  `it('should display error message for invalid credentials', () => {`  
    `// Simulate Firebase login failure`  
    `cy.intercept('POST', '[https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=](https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=)*', {`  
      `statusCode: 400,`  
      `body: {`  
        `error: {`  
          `code: 400,`  
          `message: 'EMAIL_NOT_FOUND',`  
          `errors: [{ message: 'EMAIL_NOT_FOUND', domain: 'global', reason: 'invalid' }]`  
        `}`  
      `},`  
    `}).as('firebaseLoginError');`

    `cy.get('input[name="email"]').type('<EMAIL>');`  
    `cy.get('input[name="password"]').type('wrongpassword');`  
    `cy.get('button[type="submit"]').click();`

    `cy.wait('@firebaseLoginError');`  
    `cy.contains('Invalid credentials. Please try again.').should('be.visible'); // Check for error message`  
    `cy.url().should('include', '/login'); // Ensure still on login page`  
  `});`  
`});`
