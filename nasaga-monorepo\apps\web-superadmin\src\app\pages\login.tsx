import React from 'react';
import { useNavigate } from 'react-router-dom';
import { RequireGuest } from '@nasaga-monorepo/auth';
import { LoginPage as SharedLoginPage } from '@nasaga-monorepo/ui';

/**
 * Super Admin Login Page Component
 */
export const LoginPage: React.FC = () => {
  const navigate = useNavigate();

  const handleLoginSuccess = () => {
    navigate('/dashboard/users');
  };

  return (
    <RequireGuest redirectTo="/dashboard/users">
      <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900">
        {/* Custom branding for super admin */}
        <div className="absolute top-4 left-4 text-white">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center">
              <svg className="w-5 h-5 text-black" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <span className="font-bold text-lg">Super Admin Portal</span>
          </div>
        </div>

        {/* Custom login form with super admin styling */}
        <div className="flex items-center justify-center min-h-screen px-4">
          <div className="max-w-md w-full space-y-8 bg-white/10 backdrop-blur-lg rounded-xl p-8 border border-white/20">
            <div className="text-center">
              <div className="mx-auto w-16 h-16 bg-yellow-500 rounded-full flex items-center justify-center mb-4">
                <svg className="w-8 h-8 text-black" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clipRule="evenodd" />
                </svg>
              </div>
              <h2 className="text-3xl font-bold text-white">Super Admin Access</h2>
              <p className="mt-2 text-gray-300">
                High-level administrative access to Nasaga Platform
              </p>
            </div>

            <div className="bg-white rounded-lg p-6">
              <SharedLoginPage
                onLoginSuccess={handleLoginSuccess}
                variant="mobile"
              />
            </div>

            <div className="text-center text-sm text-gray-300">
              <p>Super Admin privileges required</p>
              <p className="mt-1">Contact system administrator for access</p>
            </div>
          </div>
        </div>
      </div>
    </RequireGuest>
  );
};

export default LoginPage;
