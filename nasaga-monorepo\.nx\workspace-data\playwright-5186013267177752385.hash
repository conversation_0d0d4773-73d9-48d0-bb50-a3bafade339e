{"5454639190796323858": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["@nasaga-monorepo/web-admin"], "target": "preview"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output", "{projectRoot}/test-output/playwright/report"]}, "e2e-ci--src/example.spec.ts": {"command": "playwright test src/example.spec.ts --output=test-output\\playwright\\output\\src-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "test-output\\playwright\\report\\src-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "test-output\\playwright\\report\\src-example-spec-ts"}}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in src/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["@nasaga-monorepo/web-admin"], "target": "preview"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output/src-example-spec-ts", "{projectRoot}/test-output/playwright/report/src-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output", "{projectRoot}/test-output/playwright/report"], "dependsOn": [{"target": "e2e-ci--src/example.spec.ts", "projects": "self", "params": "forward"}], "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--src/example.spec.ts", "e2e-ci"]}}}, "5302859409526940461": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["web-superadmin"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output", "{projectRoot}/test-output/playwright/report"]}, "e2e-ci--e2e/example.spec.ts": {"command": "playwright test e2e/example.spec.ts --output=test-output\\playwright\\output\\e2e-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "test-output\\playwright\\report\\e2e-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "test-output\\playwright\\report\\e2e-example-spec-ts"}}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in e2e/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["web-superadmin"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output/e2e-example-spec-ts", "{projectRoot}/test-output/playwright/report/e2e-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output", "{projectRoot}/test-output/playwright/report"], "dependsOn": [{"target": "e2e-ci--e2e/example.spec.ts", "projects": "self", "params": "forward"}], "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--e2e/example.spec.ts", "e2e-ci"]}}}, "2440237151093247698": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["web-superadmin"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output", "{projectRoot}/test-output/playwright/report"]}, "e2e-ci--e2e/example.spec.ts": {"command": "playwright test e2e/example.spec.ts --output=test-output\\playwright\\output\\e2e-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "test-output\\playwright\\report\\e2e-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "test-output\\playwright\\report\\e2e-example-spec-ts"}}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in e2e/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["web-superadmin"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output/e2e-example-spec-ts", "{projectRoot}/test-output/playwright/report/e2e-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output", "{projectRoot}/test-output/playwright/report"], "dependsOn": [{"target": "e2e-ci--e2e/example.spec.ts", "projects": "self", "params": "forward"}], "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--e2e/example.spec.ts", "e2e-ci"]}}}, "2526905217705161449": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["web-superadmin"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output", "{projectRoot}/test-output/playwright/report"]}, "e2e-ci--e2e/example.spec.ts": {"command": "playwright test e2e/example.spec.ts --output=test-output\\playwright\\output\\e2e-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "test-output\\playwright\\report\\e2e-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "test-output\\playwright\\report\\e2e-example-spec-ts"}}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in e2e/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["web-superadmin"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output/e2e-example-spec-ts", "{projectRoot}/test-output/playwright/report/e2e-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output", "{projectRoot}/test-output/playwright/report"], "dependsOn": [{"target": "e2e-ci--e2e/example.spec.ts", "projects": "self", "params": "forward"}], "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--e2e/example.spec.ts", "e2e-ci"]}}}, "17307247216041514418": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["web-superadmin"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output", "{projectRoot}/test-output/playwright/report"]}, "e2e-ci--e2e/example.spec.ts": {"command": "playwright test e2e/example.spec.ts --output=test-output\\playwright\\output\\e2e-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "test-output\\playwright\\report\\e2e-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "test-output\\playwright\\report\\e2e-example-spec-ts"}}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in e2e/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["web-superadmin"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output/e2e-example-spec-ts", "{projectRoot}/test-output/playwright/report/e2e-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output", "{projectRoot}/test-output/playwright/report"], "dependsOn": [{"target": "e2e-ci--e2e/example.spec.ts", "projects": "self", "params": "forward"}], "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--e2e/example.spec.ts", "e2e-ci"]}}}}