{"17463300599234184100": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["@nasaga-monorepo/web-admin"], "target": "preview"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output", "{projectRoot}/test-output/playwright/report"]}, "e2e-ci--src/example.spec.ts": {"command": "playwright test src/example.spec.ts --output=test-output\\playwright\\output\\src-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "test-output\\playwright\\report\\src-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "test-output\\playwright\\report\\src-example-spec-ts"}}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in src/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["@nasaga-monorepo/web-admin"], "target": "preview"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output/src-example-spec-ts", "{projectRoot}/test-output/playwright/report/src-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output", "{projectRoot}/test-output/playwright/report"], "dependsOn": [{"target": "e2e-ci--src/example.spec.ts", "projects": "self", "params": "forward"}], "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--src/example.spec.ts", "e2e-ci"]}}}, "14043636290612682959": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["@nasaga-monorepo/web-admin"], "target": "preview"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output", "{projectRoot}/test-output/playwright/report"]}, "e2e-ci--src/example.spec.ts": {"command": "playwright test src/example.spec.ts --output=test-output\\playwright\\output\\src-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "test-output\\playwright\\report\\src-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "test-output\\playwright\\report\\src-example-spec-ts"}}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in src/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["@nasaga-monorepo/web-admin"], "target": "preview"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output/src-example-spec-ts", "{projectRoot}/test-output/playwright/report/src-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output", "{projectRoot}/test-output/playwright/report"], "dependsOn": [{"target": "e2e-ci--src/example.spec.ts", "projects": "self", "params": "forward"}], "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--src/example.spec.ts", "e2e-ci"]}}}, "14686130428502897901": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["@nasaga-monorepo/web-admin"], "target": "preview"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output", "{projectRoot}/test-output/playwright/report"]}, "e2e-ci--src/example.spec.ts": {"command": "playwright test src/example.spec.ts --output=test-output\\playwright\\output\\src-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "test-output\\playwright\\report\\src-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "test-output\\playwright\\report\\src-example-spec-ts"}}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in src/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["@nasaga-monorepo/web-admin"], "target": "preview"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output/src-example-spec-ts", "{projectRoot}/test-output/playwright/report/src-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output", "{projectRoot}/test-output/playwright/report"], "dependsOn": [{"target": "e2e-ci--src/example.spec.ts", "projects": "self", "params": "forward"}], "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--src/example.spec.ts", "e2e-ci"]}}}, "15451976210543357249": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["@nasaga-monorepo/web-admin"], "target": "preview"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output", "{projectRoot}/test-output/playwright/report"]}, "e2e-ci--src/example.spec.ts": {"command": "playwright test src/example.spec.ts --output=test-output\\playwright\\output\\src-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "test-output\\playwright\\report\\src-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "test-output\\playwright\\report\\src-example-spec-ts"}}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in src/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["@nasaga-monorepo/web-admin"], "target": "preview"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output/src-example-spec-ts", "{projectRoot}/test-output/playwright/report/src-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output", "{projectRoot}/test-output/playwright/report"], "dependsOn": [{"target": "e2e-ci--src/example.spec.ts", "projects": "self", "params": "forward"}], "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--src/example.spec.ts", "e2e-ci"]}}}, "6640052376418619617": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["@nasaga-monorepo/web-admin"], "target": "preview"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output", "{projectRoot}/test-output/playwright/report"]}, "e2e-ci--src/example.spec.ts": {"command": "playwright test src/example.spec.ts --output=test-output\\playwright\\output\\src-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "test-output\\playwright\\report\\src-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "test-output\\playwright\\report\\src-example-spec-ts"}}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in src/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["@nasaga-monorepo/web-admin"], "target": "preview"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output/src-example-spec-ts", "{projectRoot}/test-output/playwright/report/src-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output", "{projectRoot}/test-output/playwright/report"], "dependsOn": [{"target": "e2e-ci--src/example.spec.ts", "projects": "self", "params": "forward"}], "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--src/example.spec.ts", "e2e-ci"]}}}, "2250018537531147804": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["@nasaga-monorepo/web-admin"], "target": "preview"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output", "{projectRoot}/test-output/playwright/report"]}, "e2e-ci--src/example.spec.ts": {"command": "playwright test src/example.spec.ts --output=test-output\\playwright\\output\\src-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "test-output\\playwright\\report\\src-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "test-output\\playwright\\report\\src-example-spec-ts"}}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in src/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["@nasaga-monorepo/web-admin"], "target": "preview"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output/src-example-spec-ts", "{projectRoot}/test-output/playwright/report/src-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output", "{projectRoot}/test-output/playwright/report"], "dependsOn": [{"target": "e2e-ci--src/example.spec.ts", "projects": "self", "params": "forward"}], "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--src/example.spec.ts", "e2e-ci"]}}}, "11373691238027404851": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["@nasaga-monorepo/web-admin"], "target": "preview"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output", "{projectRoot}/test-output/playwright/report"]}, "e2e-ci--src/example.spec.ts": {"command": "playwright test src/example.spec.ts --output=test-output\\playwright\\output\\src-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "test-output\\playwright\\report\\src-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "test-output\\playwright\\report\\src-example-spec-ts"}}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in src/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["@nasaga-monorepo/web-admin"], "target": "preview"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output/src-example-spec-ts", "{projectRoot}/test-output/playwright/report/src-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output", "{projectRoot}/test-output/playwright/report"], "dependsOn": [{"target": "e2e-ci--src/example.spec.ts", "projects": "self", "params": "forward"}], "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--src/example.spec.ts", "e2e-ci"]}}}, "1322391273207377345": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["@nasaga-monorepo/web-admin"], "target": "preview"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output", "{projectRoot}/test-output/playwright/report"]}, "e2e-ci--src/example.spec.ts": {"command": "playwright test src/example.spec.ts --output=test-output\\playwright\\output\\src-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "test-output\\playwright\\report\\src-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "test-output\\playwright\\report\\src-example-spec-ts"}}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in src/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["@nasaga-monorepo/web-admin"], "target": "preview"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output/src-example-spec-ts", "{projectRoot}/test-output/playwright/report/src-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-output/playwright/output", "{projectRoot}/test-output/playwright/report"], "dependsOn": [{"target": "e2e-ci--src/example.spec.ts", "projects": "self", "params": "forward"}], "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--src/example.spec.ts", "e2e-ci"]}}}}