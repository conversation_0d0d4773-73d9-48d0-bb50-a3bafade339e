import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { AuthService, Platform } from './auth-service';
import { UserAuthData, UserRegistrationRequest, UserLoginRequest, UserRole } from '@nasaga-monorepo/models';

/**
 * Authentication context interface
 */
interface AuthContextType {
  user: UserAuthData | null;
  loading: boolean;
  error: string | null;
  signUp: (data: UserRegistrationRequest) => Promise<void>;
  signIn: (data: UserLoginRequest) => Promise<void>;
  signInWithGoogle: () => Promise<void>;
  signOut: () => Promise<void>;
  clearError: () => void;
}

/**
 * Authentication context
 */
const AuthContext = createContext<AuthContextType | undefined>(undefined);

/**
 * Authentication provider props
 */
interface AuthProviderProps {
  children: ReactNode;
  platform?: Platform;
}

/**
 * Authentication provider component
 */
export const AuthProvider: React.FC<AuthProviderProps> = ({ children, platform = 'web' }) => {
  const [user, setUser] = useState<UserAuthData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [authService] = useState(() => new AuthService(platform));

  useEffect(() => {
    // Listen to authentication state changes
    const unsubscribe = authService.onAuthStateChange((userData) => {
      setUser(userData);
      setLoading(false);
    });

    return unsubscribe;
  }, [authService]);

  /**
   * Sign up a new user
   */
  const signUp = async (data: UserRegistrationRequest): Promise<void> => {
    try {
      setLoading(true);
      setError(null);
      const userData = await authService.registerUser(data);
      setUser(userData);
    } catch (err: any) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Sign in an existing user
   */
  const signIn = async (data: UserLoginRequest): Promise<void> => {
    try {
      setLoading(true);
      setError(null);
      const userData = await authService.signInUser(data);
      setUser(userData);
    } catch (err: any) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Sign in with Google
   */
  const signInWithGoogle = async (): Promise<void> => {
    try {
      setLoading(true);
      setError(null);
      const userData = await authService.signInWithGoogle();
      setUser(userData);
    } catch (err: any) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Sign out current user
   */
  const signOut = async (): Promise<void> => {
    try {
      setLoading(true);
      setError(null);
      await authService.signOutUser();
      setUser(null);
    } catch (err: any) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Clear error state
   */
  const clearError = (): void => {
    setError(null);
  };

  const value: AuthContextType = {
    user,
    loading,
    error,
    signUp,
    signIn,
    signInWithGoogle,
    signOut,
    clearError,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

/**
 * Hook to use authentication context
 */
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

/**
 * Hook to get current user
 */
export const useCurrentUser = (): UserAuthData | null => {
  const { user } = useAuth();
  return user;
};

/**
 * Hook to check if user is authenticated
 */
export const useIsAuthenticated = (): boolean => {
  const { user } = useAuth();
  return user !== null;
};

/**
 * Hook to check if user has admin privileges
 */
export const useIsAdmin = (): boolean => {
  const { user } = useAuth();
  return user?.role === 'admin' || user?.role === 'superAdmin';
};

/**
 * Hook to check if user is super admin
 */
export const useIsSuperAdmin = (): boolean => {
  const { user } = useAuth();
  return user?.role === 'superAdmin';
};

/**
 * Hook to check if user is an agent
 */
export const useIsAgent = (): boolean => {
  const { user } = useAuth();
  return user?.role === 'agent';
};

/**
 * Hook to check if user is a buyer
 */
export const useIsBuyer = (): boolean => {
  const { user } = useAuth();
  return user?.role === 'buyer';
};

/**
 * Hook to get current user role
 */
export const useRole = (): UserRole | null => {
  const { user } = useAuth();
  return user?.role || null;
};
