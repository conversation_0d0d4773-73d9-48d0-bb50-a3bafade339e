export default {
  expo: {
    name: "Nasaga Buyer",
    slug: "nasaga-buyer",
    version: "1.0.0",
    orientation: "portrait",
    icon: "./assets/icon.png",
    userInterfaceStyle: "light",
    splash: {
      image: "./assets/splash.png",
      resizeMode: "contain",
      backgroundColor: "#ffffff"
    },
    assetBundlePatterns: [
      "**/*"
    ],
    ios: {
      supportsTablet: true
    },
    android: {
      adaptiveIcon: {
        foregroundImage: "./assets/adaptive-icon.png",
        backgroundColor: "#ffffff"
      },
      googleServicesFile: "./google-services.json"
    },
    web: {
      favicon: "./assets/favicon.png"
    },
    extra: {
      firebase: {
        apiKey: process.env.EXPO_PUBLIC_FIREBASE_API_KEY || "your_api_key_here",
        authDomain: process.env.EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN || "nasaga-real-estate.firebaseapp.com",
        projectId: process.env.EXPO_PUBLIC_FIREBASE_PROJECT_ID || "nasaga-real-estate",
        storageBucket: process.env.EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET || "nasaga-real-estate.appspot.com",
        messagingSenderId: process.env.EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || "your_messaging_sender_id_here",
        appId: process.env.EXPO_PUBLIC_FIREBASE_APP_ID || "your_app_id_here",
        measurementId: process.env.EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID || "your_measurement_id_here"
      },
      eas: {
        projectId: "your-eas-project-id-here"
      }
    }
  }
};
