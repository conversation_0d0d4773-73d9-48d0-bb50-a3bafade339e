import React from 'react';
import { useNavigate } from 'react-router-dom';
import { RegistrationPage } from '@nasaga-monorepo/ui';

export const Register: React.FC = () => {
  const navigate = useNavigate();

  const handleSignIn = () => {
    navigate('/login');
  };

  const handleRegistrationSuccess = () => {
    // Redirect will be handled by the auth redirect hook
    console.log('Registration successful');
  };

  return (
    <RegistrationPage
      onSignIn={handleSignIn}
      onRegistrationSuccess={handleRegistrationSuccess}
      variant="default"
    />
  );
};
