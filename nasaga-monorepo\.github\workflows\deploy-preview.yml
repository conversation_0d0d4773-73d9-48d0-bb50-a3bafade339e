name: Deploy Preview

on:
  pull_request:
    branches: [main, master]
    paths:
      - 'apps/web-admin/**'
      - 'apps/web-superadmin/**'
      - 'libs/**'

env:
  NX_CLOUD_DISTRIBUTED_EXECUTION: false

jobs:
  build-and-preview:
    name: Build and Deploy Preview
    runs-on: ubuntu-latest
    if: ${{ github.event.pull_request.head.repo.full_name == github.repository }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}
          fetch-depth: 0

      - name: Derive appropriate SHAs for base and head for `nx affected` commands
        uses: nrwl/nx-set-shas@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'

      - name: Install dependencies
        run: npm ci --legacy-peer-deps

      - name: Build affected web apps
        run: npx nx affected -t build --parallel=2 --projects=web-admin,web-superadmin

      - name: Deploy web-admin to Firebase Hosting Preview Channel
        uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: '${{ secrets.GITHUB_TOKEN }}'
          firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT }}'
          expires: 30d
          projectId: '${{ secrets.FIREBASE_PROJECT_ID }}'
          target: web-admin
        env:
          FIREBASE_CLI_EXPERIMENTS: webframeworks

      - name: Deploy web-superadmin to Firebase Hosting Preview Channel
        uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: '${{ secrets.GITHUB_TOKEN }}'
          firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT }}'
          expires: 30d
          projectId: '${{ secrets.FIREBASE_PROJECT_ID }}'
          target: web-superadmin
        env:
          FIREBASE_CLI_EXPERIMENTS: webframeworks
