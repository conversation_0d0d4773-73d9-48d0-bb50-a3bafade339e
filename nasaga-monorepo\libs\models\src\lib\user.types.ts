import { Timestamp } from 'firebase/firestore';

/**
 * User roles in the Nasaga Real Estate Management Platform
 */
export type UserRole = 'superAdmin' | 'admin' | 'agent' | 'buyer';

/**
 * User interface representing a user in the system
 * This model stores user authentication details (linked to Firebase Auth UID),
 * role, and basic profile information.
 */
export interface User {
  /** Unique Firebase Authentication user ID */
  uid: string;
  
  /** User's email address */
  email: string;
  
  /** User's display name */
  displayName: string;
  
  /** User's assigned role */
  role: UserRole;
  
  /** User's phone number (optional) */
  phoneNumber?: string;
  
  /** URL to user's profile picture in Firebase Storage (optional) */
  profilePictureUrl?: string;
  
  /** Timestamp of user creation */
  createdAt: Timestamp;
  
  /** Timestamp of last update */
  updatedAt: Timestamp;
}

/**
 * User creation data interface for new user registration
 */
export interface CreateUserData {
  email: string;
  displayName: string;
  role: UserRole;
  phoneNumber?: string;
  profilePictureUrl?: string;
}

/**
 * User update data interface for updating existing users
 */
export interface UpdateUserData {
  displayName?: string;
  phoneNumber?: string;
  profilePictureUrl?: string;
  role?: UserRole;
}

/**
 * User authentication data interface
 */
export interface UserAuthData {
  uid: string;
  email: string;
  displayName: string;
  role: UserRole;
}

/**
 * User registration request interface
 */
export interface UserRegistrationRequest {
  email: string;
  password: string;
  displayName: string;
  role: 'agent' | 'buyer'; // Only agents and buyers can self-register
  phoneNumber?: string;
}

/**
 * User login request interface
 */
export interface UserLoginRequest {
  email: string;
  password: string;
}

/**
 * User profile response interface
 */
export interface UserProfileResponse {
  uid: string;
  email: string;
  displayName: string;
  role: UserRole;
  phoneNumber?: string;
  profilePictureUrl?: string;
  createdAt: string; // ISO string format for API responses
  updatedAt: string; // ISO string format for API responses
}

/**
 * Type guard to check if a user has admin privileges
 */
export const isAdmin = (user: User | UserAuthData): boolean => {
  return user.role === 'admin' || user.role === 'superAdmin';
};

/**
 * Type guard to check if a user is a super admin
 */
export const isSuperAdmin = (user: User | UserAuthData): boolean => {
  return user.role === 'superAdmin';
};

/**
 * Type guard to check if a user is an agent
 */
export const isAgent = (user: User | UserAuthData): boolean => {
  return user.role === 'agent';
};

/**
 * Type guard to check if a user is a buyer
 */
export const isBuyer = (user: User | UserAuthData): boolean => {
  return user.role === 'buyer';
};
