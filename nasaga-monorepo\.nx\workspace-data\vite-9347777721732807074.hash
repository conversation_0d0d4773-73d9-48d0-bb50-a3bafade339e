{"2719490780769843565apps/web-admin/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "apps/web-admin"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}\\dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}, "syncGenerators": ["@nx/js:typescript-sync"]}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "typecheck": {"cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-admin"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "dependsOn": ["^typecheck"], "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/web-admin --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/web-admin"}}, "metadata": {}, "projectType": "application"}, "15474099327009384019apps/web-superadmin/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "apps/web-superadmin"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}\\dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}, "syncGenerators": ["@nx/js:typescript-sync"]}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "typecheck": {"cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "dependsOn": ["^typecheck"], "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/web-superadmin --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/web-superadmin"}}, "metadata": {}, "projectType": "application"}, "2117393093064258686libs/ui/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "libs/ui"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}\\dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "libs/ui"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}, "syncGenerators": ["@nx/js:typescript-sync"]}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "libs/ui"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "libs/ui"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "typecheck": {"cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "libs/ui"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "dependsOn": ["^typecheck"], "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/ui --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/ui"}}, "metadata": {}, "projectType": "library"}, "2900999775356780892apps/web-superadmin/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "apps/web-superadmin"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}\\dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}, "syncGenerators": ["@nx/js:typescript-sync"]}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "typecheck": {"cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "dependsOn": ["^typecheck"], "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/web-superadmin --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/web-superadmin"}}, "metadata": {}, "projectType": "application"}, "9147435613765422519apps/web-superadmin/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "apps/web-superadmin"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}\\dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}, "syncGenerators": ["@nx/js:typescript-sync"]}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "typecheck": {"cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "dependsOn": ["^typecheck"], "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/web-superadmin --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/web-superadmin"}}, "metadata": {}, "projectType": "application"}, "13301566440707677394apps/web-superadmin/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "apps/web-superadmin"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}\\dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}, "syncGenerators": ["@nx/js:typescript-sync"]}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "typecheck": {"cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "dependsOn": ["^typecheck"], "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/web-superadmin --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/web-superadmin"}}, "metadata": {}, "projectType": "application"}, "15196223728112852308apps/web-admin/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "apps/web-admin"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}\\dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}, "syncGenerators": ["@nx/js:typescript-sync"]}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "typecheck": {"cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-admin"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "dependsOn": ["^typecheck"], "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/web-admin --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/web-admin"}}, "metadata": {}, "projectType": "application"}, "15619695548069993569apps/web-admin/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "apps/web-admin"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}\\dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}, "syncGenerators": ["@nx/js:typescript-sync"]}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "typecheck": {"cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-admin"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "dependsOn": ["^typecheck"], "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/web-admin --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/web-admin"}}, "metadata": {}, "projectType": "application"}, "2936628964076527542apps/web-admin/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "apps/web-admin"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}\\dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}, "syncGenerators": ["@nx/js:typescript-sync"]}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "typecheck": {"cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-admin"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "dependsOn": ["^typecheck"], "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/web-admin --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/web-admin"}}, "metadata": {}, "projectType": "application"}, "3179635893569586395apps/web-admin/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "apps/web-admin"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}\\dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}, "syncGenerators": ["@nx/js:typescript-sync"]}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "typecheck": {"cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-admin"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "dependsOn": ["^typecheck"], "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/web-admin --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/web-admin"}}, "metadata": {}, "projectType": "application"}, "15604196467083017158apps/web-admin/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "apps/web-admin"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}\\dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}, "syncGenerators": ["@nx/js:typescript-sync"]}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "typecheck": {"cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-admin"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "dependsOn": ["^typecheck"], "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/web-admin --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/web-admin"}}, "metadata": {}, "projectType": "application"}}