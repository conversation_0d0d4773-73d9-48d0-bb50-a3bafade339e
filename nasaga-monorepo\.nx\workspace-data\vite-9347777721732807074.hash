{"17507677355088141755apps/web-admin/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "apps/web-admin"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}\\dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}, "syncGenerators": ["@nx/js:typescript-sync"]}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "typecheck": {"cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-admin"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "dependsOn": ["^typecheck"], "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/web-admin --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/web-admin"}}, "metadata": {}, "projectType": "application"}, "132901056751141029apps/web-superadmin/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "apps/web-superadmin"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}\\dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}, "syncGenerators": ["@nx/js:typescript-sync"]}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "typecheck": {"cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "dependsOn": ["^typecheck"], "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/web-superadmin --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/web-superadmin"}}, "metadata": {}, "projectType": "application"}, "13078167571409620486apps/web-superadmin/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "apps/web-superadmin"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}\\dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}, "syncGenerators": ["@nx/js:typescript-sync"]}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "typecheck": {"cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "dependsOn": ["^typecheck"], "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/web-superadmin --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/web-superadmin"}}, "metadata": {}, "projectType": "application"}, "1980037342015404245apps/web-admin/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "apps/web-admin"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}\\dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}, "syncGenerators": ["@nx/js:typescript-sync"]}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "typecheck": {"cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-admin"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "dependsOn": ["^typecheck"], "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/web-admin --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/web-admin"}}, "metadata": {}, "projectType": "application"}, "15930898520049780509apps/web-superadmin/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "apps/web-superadmin"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}\\dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}, "syncGenerators": ["@nx/js:typescript-sync"]}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "typecheck": {"cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "dependsOn": ["^typecheck"], "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/web-superadmin --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/web-superadmin"}}, "metadata": {}, "projectType": "application"}, "13596480419892159172apps/web-admin/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "apps/web-admin"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}\\dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}, "syncGenerators": ["@nx/js:typescript-sync"]}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "typecheck": {"cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-admin"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "dependsOn": ["^typecheck"], "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/web-admin --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/web-admin"}}, "metadata": {}, "projectType": "application"}, "17211091415327373726apps/web-admin/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "apps/web-admin"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}\\dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}, "syncGenerators": ["@nx/js:typescript-sync"]}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "typecheck": {"cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-admin"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "dependsOn": ["^typecheck"], "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/web-admin --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/web-admin"}}, "metadata": {}, "projectType": "application"}, "8030688762841536643apps/web-superadmin/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "apps/web-superadmin"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}\\dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}, "syncGenerators": ["@nx/js:typescript-sync"]}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "typecheck": {"cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "dependsOn": ["^typecheck"], "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/web-superadmin --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/web-superadmin"}}, "metadata": {}, "projectType": "application"}, "7967020637486156936apps/web-admin/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "apps/web-admin"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}\\dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}, "syncGenerators": ["@nx/js:typescript-sync"]}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "typecheck": {"cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-admin"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "dependsOn": ["^typecheck"], "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/web-admin --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/web-admin"}}, "metadata": {}, "projectType": "application"}, "5408572123855323237apps/web-superadmin/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "apps/web-superadmin"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}\\dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}, "syncGenerators": ["@nx/js:typescript-sync"]}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "typecheck": {"cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "dependsOn": ["^typecheck"], "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/web-superadmin --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/web-superadmin"}}, "metadata": {}, "projectType": "application"}, "9629670133324977849libs/ui/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "libs/ui"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}\\dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "libs/ui"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}, "syncGenerators": ["@nx/js:typescript-sync"]}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "libs/ui"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "libs/ui"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "typecheck": {"cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "libs/ui"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "dependsOn": ["^typecheck"], "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/ui --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/ui"}}, "metadata": {}, "projectType": "library"}, "1103147602886007758apps/web-admin/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "apps/web-admin"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}\\dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}, "syncGenerators": ["@nx/js:typescript-sync"]}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "typecheck": {"cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-admin"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "dependsOn": ["^typecheck"], "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/web-admin --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/web-admin"}}, "metadata": {}, "projectType": "application"}, "6713787898248034618apps/web-superadmin/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "apps/web-superadmin"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}\\dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}, "syncGenerators": ["@nx/js:typescript-sync"]}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "typecheck": {"cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "dependsOn": ["^typecheck"], "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/web-superadmin --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/web-superadmin"}}, "metadata": {}, "projectType": "application"}, "6409271232348506042libs/ui/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "libs/ui"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}\\dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "libs/ui"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}, "syncGenerators": ["@nx/js:typescript-sync"]}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "libs/ui"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "libs/ui"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "typecheck": {"cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "libs/ui"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "dependsOn": ["^typecheck"], "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/ui --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/ui"}}, "metadata": {}, "projectType": "library"}, "5366297532944066914apps/web-superadmin/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "apps/web-superadmin"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}\\dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}, "syncGenerators": ["@nx/js:typescript-sync"]}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "typecheck": {"cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "dependsOn": ["^typecheck"], "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/web-superadmin --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/web-superadmin"}}, "metadata": {}, "projectType": "application"}, "7481458128954190817apps/web-admin/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "apps/web-admin"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}\\dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}, "syncGenerators": ["@nx/js:typescript-sync"]}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "typecheck": {"cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-admin"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "dependsOn": ["^typecheck"], "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/web-admin --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/web-admin"}}, "metadata": {}, "projectType": "application"}, "12254870850245111016libs/ui/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "libs/ui"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}\\dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "libs/ui"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}, "syncGenerators": ["@nx/js:typescript-sync"]}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "libs/ui"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "libs/ui"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "typecheck": {"cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "libs/ui"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "dependsOn": ["^typecheck"], "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/ui --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/ui"}}, "metadata": {}, "projectType": "library"}, "4070363043296531843apps/web-superadmin/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "apps/web-superadmin"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}\\dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}, "syncGenerators": ["@nx/js:typescript-sync"]}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "typecheck": {"cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "dependsOn": ["^typecheck"], "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/web-superadmin --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/web-superadmin"}}, "metadata": {}, "projectType": "application"}, "6327910985743245442apps/web-admin/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "apps/web-admin"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}\\dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}, "syncGenerators": ["@nx/js:typescript-sync"]}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "typecheck": {"cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-admin"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "dependsOn": ["^typecheck"], "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/web-admin --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/web-admin"}}, "metadata": {}, "projectType": "application"}, "17511753951460887535libs/ui/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "libs/ui"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}\\dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "libs/ui"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}, "syncGenerators": ["@nx/js:typescript-sync"]}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "libs/ui"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "libs/ui"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "typecheck": {"cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "libs/ui"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "dependsOn": ["^typecheck"], "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/ui --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/ui"}}, "metadata": {}, "projectType": "library"}, "9629132550547624771apps/web-admin/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "apps/web-admin"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}\\dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}, "syncGenerators": ["@nx/js:typescript-sync"]}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "apps/web-admin"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "typecheck": {"cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-admin"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "dependsOn": ["^typecheck"], "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/web-admin --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/web-admin"}}, "metadata": {}, "projectType": "application"}, "16097054012308365184apps/web-superadmin/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "apps/web-superadmin"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}\\dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}, "syncGenerators": ["@nx/js:typescript-sync"]}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "apps/web-superadmin"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "typecheck": {"cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-superadmin"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "dependsOn": ["^typecheck"], "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/web-superadmin --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/web-superadmin"}}, "metadata": {}, "projectType": "application"}, "10754222350208584031libs/ui/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "libs/ui"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}\\dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "libs/ui"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}, "syncGenerators": ["@nx/js:typescript-sync"]}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "libs/ui"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "libs/ui"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "typecheck": {"cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "libs/ui"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "dependsOn": ["^typecheck"], "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/ui --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/ui"}}, "metadata": {}, "projectType": "library"}}