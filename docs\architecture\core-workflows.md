# **Core Workflows**

## **User Registration and Role Assignment Workflow**

`sequenceDiagram`  
    `actor User`  
    `participant ClientApp[Client App (Web/Mobile)]`  
    `participant FirebaseAuthClient[Firebase Auth SDK (Client)]`  
    `participant BackendAPI[Backend API (Node.js/Express)]`  
    `participant FirebaseAuthAdmin[Firebase Auth Admin SDK (Backend)]`  
    `participant Firestore[Firebase Firestore]`

    `User->>ClientApp: 1. Initiates Registration (email, password, role)`  
    `ClientApp->>FirebaseAuthClient: 2. Calls createUserWithEmailAndPassword()`  
    `FirebaseAuthClient-->>FirebaseAuthClient: 3. Handles user creation internally`  
    `FirebaseAuthClient-->>ClientApp: 4. Returns user credentials (uid, idToken)`  
    `ClientApp->>BackendAPI: 5. Sends POST /auth/register with idToken and desired role (e.g., 'agent', 'buyer')`  
    `Note over ClientApp,BackendAPI: Client-side registration is limited to 'agent'/'buyer' roles.`  
    `BackendAPI->>FirebaseAuthAdmin: 6. Verifies idToken & extracts UID`  
    `FirebaseAuthAdmin-->>BackendAPI: 7. Token Validated (returns decoded token)`  
    `BackendAPI->>Firestore: 8. Creates new User document in 'users' collection with UID and role`  
    `Firestore-->>BackendAPI: 9. User document created`  
    `BackendAPI-->>ClientApp: 10. Returns success response`  
    `ClientApp->>User: 11. Registration successful, User logged in.`

## **Property Listing Creation & Approval Workflow**

`sequenceDiagram`  
    `actor Agent`  
    `participant AgentMobileApp[Agent Mobile App (RN/Expo)]`  
    `participant FirebaseSDKs[Firebase SDKs (Client)]`  
    `participant BackendAPI[Backend API (Node.js/Express)]`  
    `participant FirebaseAuthAdmin[Firebase Auth Admin SDK (Backend)]`  
    `participant Firestore[Firebase Firestore]`  
    `participant FirebaseStorage[Firebase Storage]`  
    `actor Admin`  
    `participant AdminWebDashboard[Admin Web Dashboard (React)]`

    `Agent->>AgentMobileApp: 1. Initiates 'Create New Property'`  
    `AgentMobileApp->>Agent: 2. Presents Property Form`  
    `Agent->>AgentMobileApp: 3. Enters property details & selects images`  
    `AgentMobileApp->>FirebaseSDKs: 4. Uploads images to Firebase Storage`  
    `FirebaseSDKs-->>FirebaseStorage: 5. Stores images`  
    `FirebaseStorage-->>FirebaseSDKs: 6. Returns image URLs`  
    `AgentMobileApp->>FirebaseSDKs: 7. Saves property data (including image URLs) to Firestore 'properties' collection with status 'pending'`  
    `FirebaseSDKs-->>Firestore: 8. Writes property document`  
    `Firestore-->>FirebaseSDKs: 9. Property document created`  
    `AgentMobileApp-->>Agent: 10. Confirmation: Property submitted for review`

    `Note over Admin,AdminWebDashboard: Admin logs in and views pending listings`  
    `AdminWebDashboard->>Firestore: 11. Real-time query for properties with status 'pending'`  
    `Firestore-->>AdminWebDashboard: 12. Streams pending properties list`  
    `Admin->>AdminWebDashboard: 13. Selects property for review & changes status to 'approved' or 'rejected'`  
    `AdminWebDashboard->>BackendAPI: 14. Calls PATCH /properties/{propertyId}/status (with Admin Firebase ID Token)`  
    `BackendAPI->>FirebaseAuthAdmin: 15. Verifies Admin's ID Token & Role`  
    `FirebaseAuthAdmin-->>BackendAPI: 16. Token Validated (returns decoded token)`  
    `BackendAPI->>Firestore: 17. Updates property document status & adds approvedByAdminUid, approvedAt`  
    `Firestore-->>BackendAPI: 18. Property status updated`  
    `BackendAPI-->>AdminWebDashboard: 19. Returns success response`  
    `AdminWebDashboard-->>Admin: 20. Confirmation: Property status updated`

    `Note over Firestore,AgentMobileApp: Real-time update propagates to Buyer App and Agent's own view`  
    `Firestore->>BuyerMobileApp: 21. Real-time update to Buyer App for favorited properties`  
    `Firestore->>AgentMobileApp: 22. Real-time update to Agent App for their own property status`
