import { AuthProvider, RequireAuth, RequireRole } from '@nasaga-monorepo/auth';
import { Route, Routes } from 'react-router-dom';
import { Login } from './pages/login';
import { Register } from './pages/register';
import { Dashboard } from './pages/dashboard';
import { Properties } from './pages/properties';

export function App() {
  return (
    <AuthProvider platform="web">
      <Routes>
        <Route path="/login" element={<Login />} />
        <Route path="/register" element={<Register />} />
        <Route
          path="/"
          element={
            <RequireAuth>
              <Dashboard />
            </RequireAuth>
          }
        />
        <Route
          path="/dashboard/properties"
          element={
            <RequireRole roles={['admin', 'superAdmin']} fallback={
              <div className="flex items-center justify-center min-h-screen">
                <div className="text-center">
                  <h1 className="text-4xl font-bold text-gray-900 mb-4">Access Denied</h1>
                  <p className="text-xl text-gray-600 mb-8">Admin or Super Admin access required to view this page.</p>
                  <button 
                    onClick={() => window.history.back()}
                    className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                  >
                    Go Back
                  </button>
                </div>
              </div>
            }>
              <Properties />
            </RequireRole>
          }
        />
      </Routes>
    </AuthProvider>
  );
}

export default App;
