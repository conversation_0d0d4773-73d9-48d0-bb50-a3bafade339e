import React, { ReactNode } from 'react';
import { useAuth, useIsAdmin, useIsSuperAdmin, useIsAgent, useIsBuyer } from './auth-context';
import { UserRole } from '@nasaga-monorepo/models';

/**
 * Props for authentication guard components
 */
interface AuthGuardProps {
  children: ReactNode;
  fallback?: ReactNode;
  redirectTo?: string;
}

/**
 * Props for role-based guard components
 */
interface RoleGuardProps extends AuthGuardProps {
  roles?: UserRole[];
}

/**
 * Loading component for authentication states
 */
const AuthLoading: React.FC = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
  </div>
);

/**
 * Unauthorized access component
 */
const Unauthorized: React.FC<{ message?: string }> = ({ 
  message = "You don't have permission to access this page." 
}) => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="text-center">
      <h1 className="text-4xl font-bold text-gray-900 mb-4">403</h1>
      <p className="text-xl text-gray-600 mb-8">{message}</p>
      <button 
        onClick={() => window.history.back()}
        className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
      >
        Go Back
      </button>
    </div>
  </div>
);

/**
 * Login required component
 */
const LoginRequired: React.FC = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="text-center">
      <h1 className="text-4xl font-bold text-gray-900 mb-4">Authentication Required</h1>
      <p className="text-xl text-gray-600 mb-8">Please log in to access this page.</p>
      <button 
        onClick={() => window.location.href = '/login'}
        className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
      >
        Go to Login
      </button>
    </div>
  </div>
);

/**
 * Guard component that requires user to be authenticated
 */
export const RequireAuth: React.FC<AuthGuardProps> = ({ 
  children, 
  fallback = <LoginRequired /> 
}) => {
  const { user, loading } = useAuth();

  if (loading) {
    return <AuthLoading />;
  }

  if (!user) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

/**
 * Guard component that requires user to have admin privileges
 */
export const RequireAdmin: React.FC<AuthGuardProps> = ({ 
  children, 
  fallback = <Unauthorized message="Admin access required." /> 
}) => {
  const { user, loading } = useAuth();
  const isAdmin = useIsAdmin();

  if (loading) {
    return <AuthLoading />;
  }

  if (!user) {
    return <LoginRequired />;
  }

  if (!isAdmin) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

/**
 * Guard component that requires user to be a super admin
 */
export const RequireSuperAdmin: React.FC<AuthGuardProps> = ({ 
  children, 
  fallback = <Unauthorized message="Super Admin access required." /> 
}) => {
  const { user, loading } = useAuth();
  const isSuperAdmin = useIsSuperAdmin();

  if (loading) {
    return <AuthLoading />;
  }

  if (!user) {
    return <LoginRequired />;
  }

  if (!isSuperAdmin) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

/**
 * Guard component that requires user to be an agent
 */
export const RequireAgent: React.FC<AuthGuardProps> = ({ 
  children, 
  fallback = <Unauthorized message="Agent access required." /> 
}) => {
  const { user, loading } = useAuth();
  const isAgent = useIsAgent();

  if (loading) {
    return <AuthLoading />;
  }

  if (!user) {
    return <LoginRequired />;
  }

  if (!isAgent) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

/**
 * Guard component that requires user to be a buyer
 */
export const RequireBuyer: React.FC<AuthGuardProps> = ({ 
  children, 
  fallback = <Unauthorized message="Buyer access required." /> 
}) => {
  const { user, loading } = useAuth();
  const isBuyer = useIsBuyer();

  if (loading) {
    return <AuthLoading />;
  }

  if (!user) {
    return <LoginRequired />;
  }

  if (!isBuyer) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

/**
 * Guard component that requires user to have specific roles
 */
export const RequireRole: React.FC<RoleGuardProps> = ({ 
  children, 
  roles = [], 
  fallback = <Unauthorized /> 
}) => {
  const { user, loading } = useAuth();

  if (loading) {
    return <AuthLoading />;
  }

  if (!user) {
    return <LoginRequired />;
  }

  if (roles.length > 0 && !roles.includes(user.role)) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

/**
 * Guard component that redirects authenticated users (for login/register pages)
 */
export const RequireGuest: React.FC<AuthGuardProps> = ({ 
  children, 
  redirectTo = '/dashboard' 
}) => {
  const { user, loading } = useAuth();

  if (loading) {
    return <AuthLoading />;
  }

  if (user) {
    // In a real app, you'd use your router's redirect mechanism
    window.location.href = redirectTo;
    return null;
  }

  return <>{children}</>;
};

/**
 * Higher-order component that wraps a component with role-based access control
 */
export function withRoleGuard<P extends object>(
  Component: React.ComponentType<P>,
  allowedRoles: UserRole[],
  fallbackComponent?: React.ComponentType
): React.ComponentType<P> {
  const WrappedComponent: React.FC<P> = (props) => {
    const { user, loading } = useAuth();
    const FallbackComponent = fallbackComponent || Unauthorized;

    if (loading) {
      return <AuthLoading />;
    }

    if (!user) {
      return <LoginRequired />;
    }

    if (!allowedRoles.includes(user.role)) {
      return <FallbackComponent />;
    }

    return <Component {...props} />;
  };

  WrappedComponent.displayName = `withRoleGuard(${Component.displayName || Component.name})`;
  return WrappedComponent;
}

/**
 * Convenience function to create a super admin guard HOC
 */
export const withSuperAdminGuard = <P extends object>(
  Component: React.ComponentType<P>
) => withRoleGuard(Component, ['superAdmin']);

/**
 * Convenience function to create an admin guard HOC (includes super admin)
 */
export const withAdminGuard = <P extends object>(
  Component: React.ComponentType<P>
) => withRoleGuard(Component, ['admin', 'superAdmin']);
