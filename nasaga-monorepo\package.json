{"name": "@nasaga-monorepo/source", "version": "0.0.0", "license": "MIT", "scripts": {"prepare": "husky"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"]}, "private": true, "devDependencies": {"@babel/core": "^7.14.5", "@babel/preset-react": "^7.14.5", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/js": "^9.8.0", "@expo/cli": "~0.21.8", "@nx/devkit": "21.3.9", "@nx/eslint": "^21.3.9", "@nx/eslint-plugin": "21.3.9", "@nx/expo": "^21.3.9", "@nx/express": "^21.3.9", "@nx/jest": "^21.3.9", "@nx/js": "21.3.9", "@nx/node": "^21.3.9", "@nx/playwright": "21.3.9", "@nx/react": "^21.3.9", "@nx/react-native": "^21.3.9", "@nx/vite": "21.3.9", "@nx/web": "21.3.9", "@nx/webpack": "21.3.9", "@playwright/test": "^1.36.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.7", "@svgr/webpack": "^8.0.1", "@swc-node/register": "~1.9.1", "@swc/cli": "~0.6.0", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@swc/jest": "~0.2.38", "@testing-library/dom": "10.4.0", "@testing-library/jest-native": "~5.4.3", "@testing-library/react": "16.1.0", "@testing-library/react-native": "~12.9.0", "@types/express": "^4.17.21", "@types/jest": "^30.0.0", "@types/node": "^20.0.0", "@types/react": "19.0.0", "@types/react-dom": "19.0.0", "@vitejs/plugin-react": "^4.2.0", "@vitest/ui": "^3.0.0", "ajv": "^8.0.0", "autoprefixer": "10.4.13", "babel-jest": "^30.0.2", "babel-preset-expo": "~12.0.1", "eslint": "^9.8.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.1", "eslint-plugin-playwright": "^1.6.2", "eslint-plugin-react": "7.35.0", "eslint-plugin-react-hooks": "5.0.0", "husky": "^9.1.7", "jest": "^30.0.2", "jest-environment-jsdom": "^30.0.2", "jest-environment-node": "^30.0.2", "jest-expo": "~52.0.2", "jest-util": "^30.0.2", "jiti": "2.4.2", "jsdom": "~22.1.0", "jsonc-eslint-parser": "^2.1.0", "lint-staged": "^16.1.2", "nx": "21.3.9", "postcss": "8.4.38", "prettier": "^2.6.2", "react-refresh": "^0.10.0", "react-test-renderer": "~18.3.1", "tailwindcss": "3.4.3", "ts-jest": "^29.4.0", "ts-node": "10.9.1", "tslib": "^2.3.0", "typescript": "~5.8.2", "typescript-eslint": "^8.29.0", "vite": "^6.0.0", "vite-plugin-dts": "~4.5.0", "vitest": "^3.0.0", "webpack-cli": "^5.1.4"}, "workspaces": ["packages/*", "apps/*", "libs/*"], "dependencies": {"@expo/metro-config": "~0.19.4", "@expo/metro-runtime": "~4.0.0", "axios": "^1.6.0", "expo": "~52.0.11", "expo-splash-screen": "~0.29.13", "expo-status-bar": "~2.0.0", "express": "^4.21.2", "firebase": "^12.0.0", "firebase-admin": "^13.4.0", "react": "19.0.0", "react-dom": "19.0.0", "react-firebase-hooks": "^5.1.1", "react-native": "0.76.3", "react-native-svg": "~15.8.0", "react-native-svg-transformer": "~1.5.0", "react-native-web": "~0.19.13", "react-router-dom": "6.29.0"}}