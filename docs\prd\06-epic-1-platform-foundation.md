# Epic 1: Platform Foundation & Admin/Super Admin Management

**Epic Goal**: Establish core platform infrastructure, integrate Firebase services for authentication and data, and deliver functional Admin and Super Admin web dashboards to securely manage users and initial platform configurations, ensuring a solid foundation for all subsequent development.

## Story 1.1 Project Setup & Core Authentication

**As a** developer,  
**I want** to set up the monorepo, initialize Firebase, and implement core authentication,  
**so that** all subsequent development can proceed on a stable and secure foundation with user login capabilities.

### Acceptance Criteria

1.  **1**: A monorepo structure (e.g., using Nx or Turborepo) is initialized, containing basic application shells for web (Admin/Super Admin), mobile (Agent/Buyer), and backend.  
2.  **2**: Firebase project is initialized and connected to the monorepo projects.  
3.  **3**: Firebase Authentication is configured for email/password and Google Sign-In providers.  
4.  **4**: A user registration endpoint (backend) and login component (web/mobile) are implemented and successfully authenticate users against Firebase.  
5.  **5**: User roles (superAdmin, admin, agent, buyer) are securely stored in Firestore upon user creation/signup and linked to Firebase Auth UIDs.  
6.  **6**: Basic Firebase Security Rules are defined for `users` collection to prevent unauthorized role modification.

## Story 1.2 Super Admin Web Dashboard Access & Basic User Management

**As a** Super Admin,  
**I want** to securely log in to a dedicated web dashboard and view basic user information,  
**so that** I can begin overseeing platform users and managing other administrative roles.

### Acceptance Criteria

1.  **1**: A login page for the Super Admin web dashboard is implemented using React/TypeScript.  
2.  **2**: Upon successful login, Super Admin users are redirected to a Super Admin dashboard homepage.  
3.  **3**: The Super Admin dashboard displays a list of all registered users (Agents, Buyers, Admins, Super Admins) with their email and role.  
4.  **4**: Super Admins can filter users by role.  
5.  **5**: Access to the Super Admin dashboard is restricted solely to users with the 'superAdmin' role as verified by Firebase and Firestore rules.  
6.  **6**: The dashboard ensures secure logout functionality.

## Story 1.3 Admin Web Dashboard Access & Basic Property Approval Workflow

**As an** Admin,  
**I want** to securely log in to my dedicated web dashboard and view pending property listings for approval,  
**so that** I can manage content moderation and ensure platform quality.

### Acceptance Criteria

1.  **1**: A separate login page (or a shared login with role-based routing) for the Admin web dashboard is implemented.  
2.  **2**: Upon successful login, Admin users are redirected to an Admin dashboard homepage.  
3.  **3**: The Admin dashboard displays a list of properties with a "pending" status, including basic property details (title, agent).  
4.  **4**: Admins can change a property's status from "pending" to "approved" or "rejected" via the dashboard.  
5.  **5**: Access to the Admin dashboard is restricted solely to users with 'admin' or 'superAdmin' roles.  
6.  **6**: The dashboard ensures secure logout functionality.
