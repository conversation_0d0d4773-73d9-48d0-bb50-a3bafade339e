{"name": "@nasaga-monorepo/backend-e2e", "version": "0.0.1", "private": true, "nx": {"implicitDependencies": ["@nasaga-monorepo/backend"], "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{projectRoot}/test-output/jest/coverage"], "options": {"jestConfig": "apps/backend-e2e/jest.config.ts", "passWithNoTests": true}, "dependsOn": ["@nasaga-monorepo/backend:build", "@nasaga-monorepo/backend:serve"]}}}}