import { httpsCallable } from 'firebase/functions';
import { getFirebaseFunctions } from '@nasaga-monorepo/utils';

/**
 * Call the setRole function to assign a role to a user.
 * This function should only be callable by a super admin.
 */
export const setRoleBySuperAdmin = async (email: string, role: string): Promise<{ success: boolean; message: string }> => {
  try {
    const functions = getFirebaseFunctions();
    const setRole = httpsCallable(functions, 'setRole');
    const result = await setRole({ email, role });
    return result.data as { success: boolean; message: string };
  } catch (error: any) {
    console.error('Error setting user role:', error);
    return { success: false, message: error.message || 'An unexpected error occurred.' };
  }
};
