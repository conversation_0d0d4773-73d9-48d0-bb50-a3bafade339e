import React, { useState } from 'react';
import { Box, Drawer, List, ListItem, ListItemIcon, ListItemText, Collapse, IconButton, Typography, Divider, styled } from '@mui/material';
import { ChevronLeft as ChevronLeftIcon, ChevronRight as ChevronRightIcon } from '@mui/icons-material';
import { UserRole } from '@nasaga-monorepo/models';
import { SideNavProps, NavigationItem } from './types';

const drawerWidth = 240;

const StyledDrawer = styled(Drawer)(({ theme, open }) => ({
  '& .MuiDrawer-paper': {
    width: open ? drawerWidth : theme.spacing(7),
    transition: theme.transitions.create('width', {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.enteringScreen,
    }),
    overflowX: 'hidden',
  },
}));

const SideNav: React.FC<SideNavProps> = ({ userRole, currentPath, navigation, collapsed: initialCollapsed = false, onNavigate, onCollapseChange, logoUrl, brandName }) => {
  const [collapsed, setCollapsed] = useState(initialCollapsed);
  const [openSubMenus, setOpenSubMenus] = useState<string[]>([]);

  const handleToggleCollapse = () => {
    const newCollapsed = !collapsed;
    setCollapsed(newCollapsed);
    onCollapseChange?.(newCollapsed);
  };

  const handleSubMenuToggle = (id: string) => {
    setOpenSubMenus(prev => (prev.includes(id) ? prev.filter(item => item !== id) : [...prev, id]));
  };

  const renderNavItem = (item: NavigationItem) => {
    if (!item.roles.includes(userRole)) {
      return null;
    }

    const isActive = currentPath === item.path;
    const hasChildren = item.children && item.children.length > 0;

    const listItem = (
      <ListItem
        button
        onClick={() => (hasChildren ? handleSubMenuToggle(item.id) : onNavigate?.(item.path))}
        selected={isActive}
      >
        <ListItemIcon>
          <item.icon />
        </ListItemIcon>
        <ListItemText primary={item.title} />
      </ListItem>
    );

    if (hasChildren) {
      return (
        <React.Fragment key={item.id}>
          {listItem}
          <Collapse in={openSubMenus.includes(item.id)} timeout="auto" unmountOnExit>
            <List component="div" disablePadding>
              {item.children.map(child => (
                <ListItem button key={child.id} onClick={() => onNavigate?.(child.path)} selected={currentPath === child.path}>
                  <ListItemIcon />
                  <ListItemText primary={child.title} />
                </ListItem>
              ))}
            </List>
          </Collapse>
        </React.Fragment>
      );
    }

    return listItem;
  };

  return (
    <StyledDrawer variant="permanent" open={!collapsed}>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', padding: theme => theme.spacing(0, 1) }}>
        {logoUrl && <img src={logoUrl} alt={brandName} width="40" height="40" />}
        {!collapsed && <Typography variant="h6">{brandName}</Typography>}
        <IconButton onClick={handleToggleCollapse}>
          {collapsed ? <ChevronRightIcon /> : <ChevronLeftIcon />}
        </IconButton>
      </Box>
      <Divider />
      <List>
        {navigation.sections.map(section => (
          <React.Fragment key={section.title}>
            {!collapsed && <Typography variant="caption" sx={{ padding: theme => theme.spacing(2, 2, 1, 2) }}>{section.title}</Typography>}
            {section.items.map(item => renderNavItem(item))}
          </React.Fragment>
        ))}
      </List>
    </StyledDrawer>
  );
};

export default SideNav;

