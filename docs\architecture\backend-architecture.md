# **Backend Architecture**

## **Service Architecture**

### **Traditional Server Architecture**

* **Controller/Route Organization**:  
  `apps/`  
  `└── backend/`  
      `└── src/`  
          `├── controllers/          # Handles incoming API requests, delegates to services`  
          `│   ├── auth.controller.ts`  
          `│   ├── properties.controller.ts`  
          `│   └── users.controller.ts`  
          `├── services/             # Contains core business logic`  
          `│   ├── auth.service.ts`  
          `│   ├── property.service.ts`  
          `│   └── user.service.ts`  
          `├── middleware/           # Express middleware (e.g., authentication, error handling)`  
          `├── models/               # Data access layer / interfaces for Firestore interactions`  
          `├── routes/               # Defines API routes and links to controllers/middleware`  
          `├── utils/                # Backend-specific utilities`  
          `└── app.ts                # Main Express application setup`

* **Controller Template**:  
  `// apps/backend/src/controllers/properties.controller.ts`  
  `import { Request, Response, NextFunction } from 'express';`  
  `import { propertyService } from '../services/property.service';`  
  `import { validatePropertyCreation, validatePropertyStatusUpdate } from '../validation/property.validation'; // Assuming validation`  
  `import { UserRole } from '@nasaga/types'; // Shared types`

  `export const propertiesController = {`  
    `createProperty: async (req: Request, res: Response, next: NextFunction) => {`  
      `try {`  
        `// Get agent UID from authenticated user (via middleware)`  
        `const agentUid = (req as any).user.uid; // Assuming user info is attached by auth middleware`  
        `const propertyData = req.body;`

        `// Validate input (assuming Joi or Zod validation)`  
        `validatePropertyCreation(propertyData);`

        `const newProperty = await propertyService.createProperty(agentUid, propertyData);`  
        `res.status(201).json(newProperty);`  
      `} catch (error) {`  
        `next(error); // Pass error to global error handler`  
      `}`  
    `},`

    `updatePropertyStatus: async (req: Request, res: Response, next: NextFunction) => {`  
      `try {`  
        `const { propertyId } = req.params;`  
        `const { status } = req.body;`  
        `const currentUserRole = (req as any).user.role as UserRole; // Assuming role attached by auth middleware`

        `// Validate input`  
        `validatePropertyStatusUpdate({ status });`

        `// Business logic for status update and role check in service`  
        `const updatedProperty = await propertyService.updatePropertyStatus(propertyId, status, currentUserRole);`  
        `res.status(200).json(updatedProperty);`  
      `} catch (error) {`  
        `next(error);`  
      `}`  
    `},`

    `// Add other methods like getPropertyById, getAgentProperties, etc.`  
  `};`

## **Database Architecture**

### **Schema Design**

`// Example of a user document in Firestore: users/{uid}`  
`{`  
  `"uid": "firebaseAuthUid123",`  
  `"email": "<EMAIL>",`  
  `"displayName": "Jane Doe",`  
  `"role": "agent",`  
  `"phoneNumber": "+2349012345678",`  
  `"profilePictureUrl": "[https://firebasestorage.googleapis.com/v0/b/nasaga.appspot.com/o/profile_pictures%2Fjane.jpg](https://firebasestorage.googleapis.com/v0/b/nasaga.appspot.com/o/profile_pictures%2Fjane.jpg)",`  
  `"createdAt": {`  
    `"_seconds": 1732730400,`  
    `"_nanoseconds": 0`  
  `},`  
  `"updatedAt": {`  
    `"_seconds": 1732734000,`  
    `"_nanoseconds": 0`  
  `}`  
`}`

`// Example of a property document in Firestore: properties/{propertyId}`  
`{`  
  `"propertyId": "autoGeneratedPropertyId456",`  
  `"title": "Modern Apartment in Ikoyi",`  
  `"description": "Luxurious 2-bedroom apartment with city views.",`  
  `"address": "456 Oak Ave",`  
  `"city": "Ikoyi",`  
  `"state": "Lagos",`  
  `"zipCode": "100001",`  
  `"price": 120000000,`  
  `"propertyType": "Apartment",`  
  `"bedrooms": 2,`  
  `"bathrooms": 2,`  
  `"squareFootage": 1500,`  
  `"agentUid": "firebaseAuthUid123",`  
  `"status": "active",`  
  `"imageUrls": [`  
    `"[https://firebasestorage.googleapis.com/v0/b/nasaga.appspot.com/o/properties%2Fapt1.jpg](https://firebasestorage.googleapis.com/v0/b/nasaga.appspot.com/o/properties%2Fapt1.jpg)",`  
    `"[https://firebasestorage.googleapis.com/v0/b/nasaga.appspot.com/o/properties%2Fapt2.jpg](https://firebasestorage.googleapis.com/v0/b/nasaga.appspot.com/o/properties%2Fapt2.jpg)"`  
  `],`  
  `"documentUrls": [],`  
  `"createdAt": {`  
    `"_seconds": 1732731000,`  
    `"_nanoseconds": 0`  
  `},`  
  `"updatedAt": {`  
    `"_seconds": 1732735000,`  
    `"_nanoseconds": 0`  
  `},`  
  `"approvedByAdminUid": "adminFirebaseUid789",`  
  `"approvedAt": {`  
    `"_seconds": 1732732000,`  
    `"_nanoseconds": 0`  
  `}`  
`}`

### **Data Access Layer**

`// apps/backend/src/models/user.model.ts (Example User Repository)`  
`import { db } from '../config/firebase.config'; // Firebase Admin SDK Firestore instance`  
`import { User, UserRole } from '@nasaga/types'; // Shared User interface`

`const usersCollection = db.collection('users');`

`export const UserModel = {`  
  `// Create a new user document`  
  `async createUser(uid: string, email: string, displayName: string, role: UserRole): Promise<User> {`  
    `const newUser: User = {`  
      `uid,`  
      `email,`  
      `displayName,`  
      `role,`  
      `createdAt: new Date() as any, // Firestore Timestamp will convert automatically`  
      `updatedAt: new Date() as any,`  
    `};`  
    `await usersCollection.doc(uid).set(newUser);`  
    `return newUser;`  
  `},`

  `// Get a user by UID`  
  `async getUserByUid(uid: string): Promise<User | null> {`  
    `const doc = await usersCollection.doc(uid).get();`  
    `if (!doc.exists) {`  
      `return null;`  
    `}`  
    `return doc.data() as User;`  
  `},`

  `// Update a user's role`  
  `async updateUserRole(uid: string, newRole: UserRole): Promise<User | null> {`  
    `const userRef = usersCollection.doc(uid);`  
    `await userRef.update({`  
      `role: newRole,`  
      `updatedAt: new Date(),`  
    `});`  
    `const updatedDoc = await userRef.get();`  
    `return updatedDoc.data() as User;`  
  `},`

  `// Get users by role`  
  `async getUsersByRole(role: UserRole): Promise<User[]> {`  
    `const snapshot = await usersCollection.where('role', '==', role).get();`  
    `return snapshot.docs.map(doc => doc.data() as User);`  
  `},`

  `// Add other CRUD operations as needed`  
`};`

`// apps/backend/src/models/property.model.ts (Example Property Repository)`  
`import { db } from '../config/firebase.config'; // Firebase Admin SDK Firestore instance`  
`import { Property, PropertyStatus } from '@nasaga/types'; // Shared Property interface`

`const propertiesCollection = db.collection('properties');`

`export const PropertyModel = {`  
  `// Create a new property document`  
  `async createProperty(propertyData: Omit<Property, 'propertyId' | 'createdAt' | 'updatedAt' | 'approvedAt' | 'approvedByAdminUid'>): Promise<Property> {`  
    `const newPropertyRef = propertiesCollection.doc(); // Auto-generate ID`  
    `const newProperty: Property = {`  
      `...propertyData,`  
      `propertyId: newPropertyRef.id,`  
      `status: 'pending', // Default status for new properties`  
      `createdAt: new Date() as any,`  
      `updatedAt: new Date() as any,`  
    `};`  
    `await newPropertyRef.set(newProperty);`  
    `return newProperty;`  
  `},`

  `// Get a property by ID`  
  `async getPropertyById(propertyId: string): Promise<Property | null> {`  
    `const doc = await propertiesCollection.doc(propertyId).get();`  
    `if (!doc.exists) {`  
      `return null;`  
    `}`  
    `return doc.data() as Property;`  
  `},`

  `// Update property status`  
  `async updatePropertyStatus(propertyId: string, status: PropertyStatus, adminUid: string): Promise<Property | null> {`  
    `const propertyRef = propertiesCollection.doc(propertyId);`  
    `await propertyRef.update({`  
      `status: status,`  
      `approvedByAdminUid: adminUid,`  
      `approvedAt: new Date(),`  
      `updatedAt: new Date(),`  
    `});`  
    `const updatedDoc = await propertyRef.get();`  
    `return updatedDoc.data() as Property;`  
  `},`

  `// Get properties by status`  
  `async getPropertiesByStatus(status: PropertyStatus): Promise<Property[]> {`  
    `const snapshot = await propertiesCollection.where('status', '==', status).get();`  
    `return snapshot.docs.map(doc => doc.data() as Property);`  
  `},`

  `// Add other query methods (e.g., by agentUid, search, filter)`  
`};`

## **Authentication and Authorization**

### **Auth Flow**

`sequenceDiagram`  
    `actor User`  
    `participant ClientApp[Client App (Web/Mobile)]`  
    `participant FirebaseAuthClient[Firebase Auth SDK (Client)]`  
    `participant BackendAPI[Backend API (Node.js/Express)]`  
    `participant FirebaseAuthAdmin[Firebase Auth Admin SDK (Backend)]`  
    `participant Firestore[Firebase Firestore]`

    `User->>ClientApp: 1. Login/Registration`  
    `ClientApp->>FirebaseAuthClient: 2. Authenticates User (e.g., signInWithEmailAndPassword)`  
    `FirebaseAuthClient-->>ClientApp: 3. Returns ID Token`  
    `ClientApp->>BackendAPI: 4. Makes authenticated request (attaches ID Token in Authorization header)`  
    `Note over ClientApp,BackendAPI: e.g., GET /properties-approval`  
    `BackendAPI->>FirebaseAuthAdmin: 5. Verifies ID Token (verifyIdToken)`  
    `FirebaseAuthAdmin-->>BackendAPI: 6. Returns Decoded Token (includes UID, optional custom claims)`  
    `BackendAPI->>Firestore: 7. (Optional) Fetches User Role from Firestore if not in custom claims`  
    `Firestore-->>BackendAPI: 8. Returns User Role`  
    `BackendAPI-->>BackendAPI: 9. Authorizes request based on User Role and Endpoint permissions`  
    `BackendAPI-->>ClientApp: 10. Sends API Response (if authorized)`  
    `BackendAPI--xClientApp: 10. Sends 403 Forbidden (if unauthorized)`

### **Middleware/Guards**

`// apps/backend/src/middleware/auth.middleware.ts`  
`import { Request, Response, NextFunction } from 'express';`  
`import { getAuth } from 'firebase-admin/auth';`  
`import { db } from '../config/firebase.config'; // Firestore instance`  
`import { UserRole } from '@nasaga/types'; // Shared types`

`// Middleware to verify Firebase ID Token`  
`export const verifyFirebaseToken = async (req: Request, res: Response, next: NextFunction) => {`  
  `const authHeader = req.headers.authorization;`  
  `if (!authHeader || !authHeader.startsWith('Bearer ')) {`  
    `return res.status(401).send({ message: 'Unauthorized: No token provided.' });`  
  `}`

  `const idToken = authHeader.split('Bearer ')[1];`

  `try {`  
    `const decodedToken = await getAuth().verifyIdToken(idToken);`  
    `(req as any).user = { uid: decodedToken.uid, email: decodedToken.email }; // Attach basic user info`

    `// Fetch user role from Firestore if not available as custom claim`  
    `// For production, consider storing role as a Firebase custom claim for faster lookup`  
    `const userDoc = await db.collection('users').doc(decodedToken.uid).get();`  
    `if (userDoc.exists) {`  
      `(req as any).user.role = (userDoc.data() as any).role as UserRole;`  
    `} else {`  
      `// Handle case where user document not found (e.g., new user not yet fully provisioned)`  
      ``console.warn(`User document not found for UID: ${decodedToken.uid}`);``  
      `return res.status(403).send({ message: 'Forbidden: User profile incomplete.' });`  
    `}`

    `next();`  
  `} catch (error) {`  
    `console.error('Error verifying Firebase ID token:', error);`  
    `res.status(401).send({ message: 'Unauthorized: Invalid or expired token.' });`  
  `}`  
`};`

`// Authorization middleware/guard`  
`export const authorizeRoles = (allowedRoles: UserRole[]) => {`  
  `return (req: Request, res: Response, next: NextFunction) => {`  
    `const userRole = (req as any).user?.role;`

    `if (!userRole || !allowedRoles.includes(userRole)) {`  
      ``return res.status(403).send({ message: `Forbidden: Insufficient role. Required: ${allowedRoles.join(', ')}` });``  
    `}`  
    `next();`  
  `};`  
`};`

`// Example usage in a route`  
`/*`  
`import express from 'express';`  
`import { verifyFirebaseToken, authorizeRoles } from '../middleware/auth.middleware';`  
`import { propertiesController } from '../controllers/properties.controller';`

`const router = express.Router();`

`router.patch(`  
  `'/properties/:propertyId/status',`  
  `verifyFirebaseToken,`  
  `authorizeRoles(['admin', 'superAdmin']), // Only Admin/Super Admin can update status`  
  `propertiesController.updatePropertyStatus`  
`);`

`router.post(`  
  `'/properties',`  
  `verifyFirebaseToken,`  
  `authorizeRoles(['agent']), // Only Agents can create properties`  
  `propertiesController.createProperty`  
`);`  
`*/`
