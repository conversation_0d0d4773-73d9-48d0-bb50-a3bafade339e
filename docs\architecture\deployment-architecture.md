# **Deployment Architecture**

## **Deployment Strategy**

* **Frontend Deployment (Web Apps \- Admin & Super Admin Dashboards):**  
  * **Platform:** Firebase Hosting.  
  * **Build Command:** npx nx build \<app-name\>.  
  * **Output Directory:** The dist/apps/\<app-name\> directory.  
  * **CDN/Edge:** Firebase Hosting automatically integrates with Google's global CDN.  
* **Frontend Deployment (Mobile Apps \- Agent & Buyer Mobile Apps):**  
  * **Platform:** Expo Application Services (EAS Build).  
  * **Build Command:** npx nx run \<app-name\>:build.  
  * **Deployment Method:** Manual submission to Google Play Store and Apple App Store.  
* **Backend Deployment (Node.js/Express API):**  
  * **Platform:** Google Cloud Functions (via Firebase Functions).  
  * **Build Command:** npx nx build backend.  
  * **Deployment Method:** Firebase CLI deployment via CI/CD.

## **CI/CD Pipeline**

`# .github/workflows/ci.yaml`  
`name: CI/CD Pipeline`

`on:`  
  `push:`  
    `branches:`  
      `- main`  
      `- develop`  
  `pull_request:`  
    `branches:`  
      `- main`  
      `- develop`

`jobs:`  
  `build-and-test:`  
    `runs-on: ubuntu-latest`  
    `steps:`  
      `- name: Checkout Repository`  
        `uses: actions/checkout@v3`  
        `with:`  
          `fetch-depth: 0 # Important for Nx affected commands`

      `- name: Setup Node.js`  
        `uses: actions/setup-node@v3`  
        `with:`  
          `node-version: '20' # As per Tech Stack`  
          `cache: 'pnpm'`

      `- name: Install pnpm`  
        `run: npm install -g pnpm`

      `- name: Setup pnpm for hoisted node_modules`  
        `run: echo "node-linker=hoisted" >> .npmrc`

      `- name: Install Monorepo Dependencies`  
        `run: pnpm install --frozen-lockfile`

      `- name: Cache Nx Build Results`  
        `uses: actions/cache@v3`  
        `with:`  
          `path: |`  
            `.nx/cache`  
            `./node_modules/.cache/nx`  
            `./dist`  
          `key: ${{ runner.os }}-nx-${{ hashFiles('**/pnpm-lock.yaml') }}`  
          `restore-keys: |`  
            `${{ runner.os }}-nx-`

      `- name: Login to Nx Cloud`  
        `run: npx nx-cloud start-ci-run --stop-agents-after="build" --github-token=${{ secrets.GITHUB_TOKEN }}`  
        `env:`  
          `NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }} # Ensure this secret is configured`

      `- name: Run Linting for Affected Projects`  
        `run: npx nx affected --target=lint --parallel --max-parallel=3`

      `- name: Run Tests for Affected Projects`  
        `run: npx nx affected --target=test --parallel --max-parallel=3`

      `- name: Build Affected Web Applications`  
        `run: npx nx affected --target=build --base=main --head=HEAD --exclude=backend,*-mobile --parallel --max-parallel=2`

      `- name: Build Backend API (Firebase Functions)`  
        `run: npx nx build backend`

      `- name: Setup Firebase CLI`  
        `run: npm install -g firebase-tools`

      `- name: Deploy Firebase Functions (Backend API)`  
        `if: github.ref == 'refs/heads/main'`  
        `run: firebase deploy --only functions --project ${{ secrets.FIREBASE_PROJECT_ID }}`  
        `env:`  
          `FIREBASE_TOKEN: ${{ secrets.FIREBASE_DEPLOY_TOKEN }}`

      `- name: Deploy Admin Web Dashboard`  
        `if: github.ref == 'refs/heads/main'`  
        `run: firebase deploy --only hosting:admin-web --project ${{ secrets.FIREBASE_PROJECT_ID }}`  
        `env:`  
          `FIREBASE_TOKEN: ${{ secrets.FIREBASE_DEPLOY_TOKEN }}`

      `- name: Deploy Super Admin Web Dashboard`  
        `if: github.ref == 'refs/heads/main'`  
        `run: firebase deploy --only hosting:super-admin-web --project ${{ secrets.FIREBASE_PROJECT_ID }}`  
        `env:`  
          `FIREBASE_TOKEN: ${{ secrets.FIREBASE_DEPLOY_TOKEN }}`

      `- name: Install EAS CLI`  
        `run: npm install -g eas-cli`  
          
      `- name: Log in to EAS (for mobile app builds)`  
        `run: eas login --non-interactive -u ${{ secrets.EAS_USERNAME }} -p ${{ secrets.EAS_PASSWORD }}`  
        `if: github.ref == 'refs/heads/main'`  
        `env:`  
            `EAS_USERNAME: ${{ secrets.EAS_USERNAME }}`  
            `EAS_PASSWORD: ${{ secrets.EAS_PASSWORD }}`

      `- name: Trigger EAS Build for Agent Mobile App`  
        `run: eas build --platform all --profile production --non-interactive --local --output apps/agent-mobile/build.zip --json --no-wait --override-local-build-store`  
        `if: github.ref == 'refs/heads/main'`  
        `working-directory: apps/agent-mobile`  
          
      `- name: Trigger EAS Build for Buyer Mobile App`  
        `run: eas build --platform all --profile production --non-interactive --local --output apps/buyer-mobile/build.zip --json --no-wait --override-local-build-store`  
        `if: github.ref == 'refs/heads/main'`  
        `working-directory: apps/buyer-mobile`

      `- name: Stop Nx Cloud CI Run`  
        `if: always()`  
        `run: npx nx-cloud stop-ci-run`

## **Environments**

| Environment | Frontend URL | Backend URL | Purpose |
| :---- | :---- | :---- | :---- |
| Development | http://localhost:\<port\> (for web) / Expo Go (for mobile) | http://localhost:\<port\> | Local development and testing by individual developers. |
| Staging | https://staging-admin.nasaga.com (example) | https://us-central1-\<project-id\>.cloudfunctions.net/api-staging (example) | Pre-production testing, QA, and stakeholder review. |
| Production | https://admin.nasaga.com (example) | https://us-central1-\<project-id\>.cloudfunctions.net/api (example) | Live environment for end-users. |
