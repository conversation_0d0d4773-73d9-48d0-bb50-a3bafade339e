import { Request, Response, NextFunction } from 'express';
import { verifyIdToken } from '@nasaga-monorepo/utils';
import { UserRole } from '@nasaga-monorepo/models';
import { AppError } from './error-handler';
import { HttpStatusCode } from '@nasaga-monorepo/models';

/**
 * Extended Request interface to include user data
 */
export interface AuthenticatedRequest extends Request {
  user?: {
    uid: string;
    email: string;
    role?: UserRole;
  };
}

/**
 * Middleware to verify Firebase ID token
 */
export const authenticateToken = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new AppError('Authorization token required', HttpStatusCode.UNAUTHORIZED);
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    
    // Verify the token with Firebase Admin
    const decodedToken = await verifyIdToken(token);
    
    // Add user info to request
    req.user = {
      uid: decodedToken.uid,
      email: decodedToken.email || '',
      role: decodedToken.role as UserRole,
    };

    next();
  } catch (error: any) {
    console.error('Authentication error:', error);
    next(new AppError('Invalid or expired token', HttpStatusCode.UNAUTHORIZED));
  }
};

/**
 * Middleware to require specific roles
 */
export const requireRole = (allowedRoles: UserRole[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      return next(new AppError('Authentication required', HttpStatusCode.UNAUTHORIZED));
    }

    if (!req.user.role || !allowedRoles.includes(req.user.role)) {
      return next(new AppError('Insufficient permissions', HttpStatusCode.FORBIDDEN));
    }

    next();
  };
};

/**
 * Middleware to require admin role (admin or superAdmin)
 */
export const requireAdmin = requireRole(['admin', 'superAdmin']);

/**
 * Middleware to require super admin role
 */
export const requireSuperAdmin = requireRole(['superAdmin']);

/**
 * Middleware to require agent role
 */
export const requireAgent = requireRole(['agent']);

/**
 * Middleware to require buyer role
 */
export const requireBuyer = requireRole(['buyer']);

/**
 * Middleware to require agent or admin roles
 */
export const requireAgentOrAdmin = requireRole(['agent', 'admin', 'superAdmin']);

/**
 * Middleware to check if user owns the resource or is admin
 */
export const requireOwnershipOrAdmin = (uidParam: string = 'uid') => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      return next(new AppError('Authentication required', HttpStatusCode.UNAUTHORIZED));
    }

    const resourceUid = req.params[uidParam];
    const isOwner = req.user.uid === resourceUid;
    const isAdmin = req.user.role === 'admin' || req.user.role === 'superAdmin';

    if (!isOwner && !isAdmin) {
      return next(new AppError('Access denied', HttpStatusCode.FORBIDDEN));
    }

    next();
  };
};
