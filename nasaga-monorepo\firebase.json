{"hosting": [{"target": "web-admin", "public": "dist/apps/web-admin", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}]}, {"target": "web-superadmin", "public": "dist/apps/web-superadmin", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}]}], "functions": [{"source": "dist/apps/backend", "codebase": "default", "runtime": "nodejs18"}], "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "emulators": {"auth": {"port": 9099}, "functions": {"port": 5001}, "firestore": {"port": 8080}, "hosting": {"port": 5000}, "ui": {"enabled": true}, "singleProjectMode": true}}