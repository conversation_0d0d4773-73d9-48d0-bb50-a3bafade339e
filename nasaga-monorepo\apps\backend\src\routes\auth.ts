import { Router } from 'express';
import { 
  getFirebaseAdminAuth, 
  getFirebaseAdminFirestore,
  setUserRole 
} from '@nasaga-monorepo/utils';
import { 
  UserRegistrationRequest, 
  UserRole, 
  User,
  ApiSuccess,
  HttpStatusCode 
} from '@nasaga-monorepo/models';
import { AppError, asyncHandler } from '../middleware/error-handler';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth-middleware';

const router = Router();

/**
 * POST /api/auth/register
 * Register a new user (agents and buyers only)
 */
router.post('/register', asyncHandler(async (req, res) => {
  const { email, password, displayName, role, phoneNumber }: UserRegistrationRequest = req.body;

  // Validate input
  if (!email || !password || !displayName || !role) {
    throw new AppError('Email, password, display name, and role are required', HttpStatusCode.BAD_REQUEST);
  }

  // Only allow agent and buyer self-registration
  if (role !== 'agent' && role !== 'buyer') {
    throw new AppError('Only agents and buyers can self-register', HttpStatusCode.BAD_REQUEST);
  }

  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    throw new AppError('Invalid email format', HttpStatusCode.BAD_REQUEST);
  }

  // Validate password strength
  if (password.length < 6) {
    throw new AppError('Password must be at least 6 characters long', HttpStatusCode.BAD_REQUEST);
  }

  try {
    const auth = getFirebaseAdminAuth();
    const firestore = getFirebaseAdminFirestore();

    // Create user with Firebase Auth
    const userRecord = await auth.createUser({
      email,
      password,
      displayName,
    });

    // Set custom claims for role
    await setUserRole(userRecord.uid, role);

    // Create user document in Firestore
    const now = new Date();
    const userData: User = {
      uid: userRecord.uid,
      email,
      displayName,
      role,
      phoneNumber,
      createdAt: { seconds: Math.floor(now.getTime() / 1000), nanoseconds: 0 } as any,
      updatedAt: { seconds: Math.floor(now.getTime() / 1000), nanoseconds: 0 } as any,
    };

    await firestore.collection('users').doc(userRecord.uid).set(userData);

    // Return success response
    const response: ApiSuccess = {
      success: true,
      data: {
        uid: userRecord.uid,
        email,
        displayName,
        role,
      },
      message: 'User registered successfully',
      timestamp: new Date().toISOString(),
    };

    res.status(HttpStatusCode.CREATED).json(response);
  } catch (error: any) {
    console.error('Registration error:', error);
    
    if (error.code === 'auth/email-already-exists') {
      throw new AppError('An account with this email already exists', HttpStatusCode.CONFLICT);
    }
    
    throw new AppError('Failed to register user', HttpStatusCode.INTERNAL_SERVER_ERROR);
  }
}));

/**
 * GET /api/auth/profile
 * Get current user profile
 */
router.get('/profile', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw new AppError('User not authenticated', HttpStatusCode.UNAUTHORIZED);
  }

  try {
    const firestore = getFirebaseAdminFirestore();
    const userDoc = await firestore.collection('users').doc(req.user.uid).get();

    if (!userDoc.exists) {
      throw new AppError('User profile not found', HttpStatusCode.NOT_FOUND);
    }

    const userData = userDoc.data() as User;

    const response: ApiSuccess = {
      success: true,
      data: {
        uid: userData.uid,
        email: userData.email,
        displayName: userData.displayName,
        role: userData.role,
        phoneNumber: userData.phoneNumber,
        profilePictureUrl: userData.profilePictureUrl,
        createdAt: userData.createdAt,
        updatedAt: userData.updatedAt,
      },
      timestamp: new Date().toISOString(),
    };

    res.json(response);
  } catch (error: any) {
    console.error('Profile fetch error:', error);
    throw new AppError('Failed to fetch user profile', HttpStatusCode.INTERNAL_SERVER_ERROR);
  }
}));

/**
 * PUT /api/auth/profile
 * Update current user profile
 */
router.put('/profile', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw new AppError('User not authenticated', HttpStatusCode.UNAUTHORIZED);
  }

  const { displayName, phoneNumber, profilePictureUrl } = req.body;

  // Validate input
  if (!displayName && !phoneNumber && !profilePictureUrl) {
    throw new AppError('At least one field must be provided for update', HttpStatusCode.BAD_REQUEST);
  }

  try {
    const firestore = getFirebaseAdminFirestore();
    const updateData: Partial<User> = {
      updatedAt: { seconds: Math.floor(Date.now() / 1000), nanoseconds: 0 } as any,
    };

    if (displayName) updateData.displayName = displayName;
    if (phoneNumber) updateData.phoneNumber = phoneNumber;
    if (profilePictureUrl) updateData.profilePictureUrl = profilePictureUrl;

    await firestore.collection('users').doc(req.user.uid).update(updateData);

    const response: ApiSuccess = {
      success: true,
      data: { message: 'Profile updated successfully' },
      timestamp: new Date().toISOString(),
    };

    res.json(response);
  } catch (error: any) {
    console.error('Profile update error:', error);
    throw new AppError('Failed to update user profile', HttpStatusCode.INTERNAL_SERVER_ERROR);
  }
}));

/**
 * POST /api/auth/verify-token
 * Verify Firebase ID token
 */
router.post('/verify-token', asyncHandler(async (req, res) => {
  const { token } = req.body;

  if (!token) {
    throw new AppError('Token is required', HttpStatusCode.BAD_REQUEST);
  }

  try {
    const auth = getFirebaseAdminAuth();
    const decodedToken = await auth.verifyIdToken(token);

    const response: ApiSuccess = {
      success: true,
      data: {
        uid: decodedToken.uid,
        email: decodedToken.email,
        role: decodedToken.role,
        valid: true,
      },
      timestamp: new Date().toISOString(),
    };

    res.json(response);
  } catch (error: any) {
    console.error('Token verification error:', error);
    throw new AppError('Invalid or expired token', HttpStatusCode.UNAUTHORIZED);
  }
}));

export default router;
