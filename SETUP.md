# Development Environment Setup

## Prerequisites ✅

The following tools are already installed and configured:

- **Node.js**: v22.14.0 (✅ meets ≥18 requirement)
- **npm**: v10.9.2 (✅)
- **Yarn**: v1.22.22 (✅)
- **pnpm**: v10.13.1 (✅)
- **Git**: v2.46.0 (✅)

## Configuration Files Created

### `.nvmrc`
Specifies Node.js version (22.14.0) for team consistency. Use with:
```bash
nvm use
```

### `.editorconfig`
Ensures consistent coding styles across different editors and IDEs:
- UTF-8 encoding
- LF line endings
- 2-space indentation for JS/TS/JSON/YAML
- 4-space indentation for Java/Kotlin
- Tab indentation for Makefiles
- 100 character line length for JS/TS files

### `.vscode/settings.json`
VS Code workspace settings optimized for React Native development:
- Format on save enabled
- ESLint auto-fix on save
- Auto-organize imports
- TypeScript/React file associations
- Excludes build directories from search
- Prettier as default formatter

### `.vscode/extensions.json`
Recommended VS Code extensions for the team:
- Prettier (code formatting)
- ESLint (linting)
- React Native Tools
- TypeScript support
- EditorConfig support
- TailwindCSS IntelliSense
- Path IntelliSense

## Android Development Setup (Required)

Since you're on Windows, you'll need Android Studio for React Native development:

### 1. Install Android Studio
1. Download from [developer.android.com](https://developer.android.com/studio)
2. Install with default settings
3. Open Android Studio and complete the setup wizard

### 2. Configure Android SDK
1. Open Android Studio → More Actions → SDK Manager
2. Install the following:
   - Android SDK Platform 34 (API Level 34)
   - Android SDK Build-Tools 34.0.0
   - Android Emulator
   - Android SDK Platform-Tools

### 3. Set Environment Variables
Add these to your system environment variables:
```
ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
```

Add to PATH:
```
%ANDROID_HOME%\platform-tools
%ANDROID_HOME%\emulator
%ANDROID_HOME%\tools
%ANDROID_HOME%\tools\bin
```

### 4. Create Virtual Device
1. Open Android Studio → More Actions → AVD Manager
2. Create a new virtual device (recommended: Pixel 7 with API 34)

## Getting Started

1. Clone this repository
2. Run `nvm use` to ensure correct Node version
3. Install dependencies: `npm install` or `yarn install` or `pnpm install`
4. For React Native: `npx react-native run-android`

## Team Consistency

All team members should:
1. Use the specified Node.js version (check `.nvmrc`)
2. Install the recommended VS Code extensions
3. Ensure EditorConfig plugin is active in their editor
4. Follow the code formatting rules (automatic with VS Code settings)

## Package Manager Usage

You can use any of the installed package managers:
- **npm**: `npm install`, `npm run`
- **Yarn**: `yarn install`, `yarn run`
- **pnpm**: `pnpm install`, `pnpm run` (fastest, uses less disk space)
