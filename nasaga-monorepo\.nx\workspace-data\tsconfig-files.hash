{"version": 1, "data": {"tsconfig.base.json": {"data": {"options": {}, "raw": {"nx": {}}, "extendedConfigFiles": []}, "extendedFilesHash": "", "hash": "8900268571896142108"}, "apps\\backend\\tsconfig.app.json": {"data": {"options": {"rootDir": "apps\\backend\\src", "outDir": "apps\\backend\\dist", "tsBuildInfoFile": "apps\\backend\\dist\\tsconfig.app.tsbuildinfo"}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}], "projectReferences": [{"path": "libs\\models\\tsconfig.lib.json", "originalPath": "../../libs/models/tsconfig.lib.json"}, {"path": "libs\\utils\\tsconfig.lib.json", "originalPath": "../../libs/utils/tsconfig.lib.json"}]}, "extendedFilesHash": "8900268571896142108|", "hash": "15702670538018423620"}, "apps\\backend\\tsconfig.json": {"data": {"options": {}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}], "projectReferences": [{"path": "libs\\models", "originalPath": "../../libs/models"}, {"path": "libs\\utils", "originalPath": "../../libs/utils"}, {"path": "apps\\backend\\tsconfig.app.json", "originalPath": "./tsconfig.app.json"}, {"path": "apps\\backend\\tsconfig.spec.json", "originalPath": "./tsconfig.spec.json"}]}, "extendedFilesHash": "8900268571896142108|", "hash": "18291125838922499647"}, "apps\\backend\\tsconfig.spec.json": {"data": {"options": {"outDir": "apps\\backend\\out-tsc\\jest"}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}], "projectReferences": [{"path": "apps\\backend\\tsconfig.app.json", "originalPath": "./tsconfig.app.json"}]}, "extendedFilesHash": "8900268571896142108|", "hash": "5626006774251950194"}, "apps\\backend-e2e\\tsconfig.json": {"data": {"options": {"outDir": "apps\\backend-e2e\\out-tsc\\@nasaga-monorepo\\backend-e2e"}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}]}, "extendedFilesHash": "8900268571896142108|", "hash": "3237992238728296076"}, "apps\\mobile-agent\\tsconfig.app.json": {"data": {"options": {"rootDir": "apps\\mobile-agent\\src", "outDir": "apps\\mobile-agent\\dist", "tsBuildInfoFile": "apps\\mobile-agent\\dist\\tsconfig.app.tsbuildinfo"}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}], "projectReferences": [{"path": "libs\\ui\\tsconfig.lib.json", "originalPath": "../../libs/ui/tsconfig.lib.json"}, {"path": "libs\\auth\\tsconfig.lib.json", "originalPath": "../../libs/auth/tsconfig.lib.json"}]}, "extendedFilesHash": "8900268571896142108|", "hash": "12655416099839703627"}, "apps\\mobile-agent\\tsconfig.json": {"data": {"options": {}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}], "projectReferences": [{"path": "libs\\ui", "originalPath": "../../libs/ui"}, {"path": "libs\\auth", "originalPath": "../../libs/auth"}, {"path": "apps\\mobile-agent\\tsconfig.app.json", "originalPath": "./tsconfig.app.json"}, {"path": "apps\\mobile-agent\\tsconfig.spec.json", "originalPath": "./tsconfig.spec.json"}]}, "extendedFilesHash": "8900268571896142108|", "hash": "15963222867769846524"}, "apps\\mobile-agent\\tsconfig.spec.json": {"data": {"options": {"outDir": "apps\\mobile-agent\\out-tsc\\jest"}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}], "projectReferences": [{"path": "apps\\mobile-agent\\tsconfig.app.json", "originalPath": "./tsconfig.app.json"}]}, "extendedFilesHash": "8900268571896142108|", "hash": "10834278139151176693"}, "apps\\mobile-buyer\\tsconfig.app.json": {"data": {"options": {"rootDir": "apps\\mobile-buyer\\src", "outDir": "apps\\mobile-buyer\\dist", "tsBuildInfoFile": "apps\\mobile-buyer\\dist\\tsconfig.app.tsbuildinfo"}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}]}, "extendedFilesHash": "8900268571896142108|", "hash": "8967954828260004517"}, "apps\\mobile-buyer\\tsconfig.json": {"data": {"options": {}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}], "projectReferences": [{"path": "apps\\mobile-buyer\\tsconfig.app.json", "originalPath": "./tsconfig.app.json"}, {"path": "apps\\mobile-buyer\\tsconfig.spec.json", "originalPath": "./tsconfig.spec.json"}]}, "extendedFilesHash": "8900268571896142108|", "hash": "9214820933160915425"}, "apps\\mobile-buyer\\tsconfig.spec.json": {"data": {"options": {"outDir": "apps\\mobile-buyer\\out-tsc\\jest"}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}], "projectReferences": [{"path": "apps\\mobile-buyer\\tsconfig.app.json", "originalPath": "./tsconfig.app.json"}]}, "extendedFilesHash": "8900268571896142108|", "hash": "10834278139151176693"}, "apps\\web-admin\\tsconfig.app.json": {"data": {"options": {"rootDir": "apps\\web-admin\\src", "outDir": "apps\\web-admin\\dist", "tsBuildInfoFile": "apps\\web-admin\\dist\\tsconfig.app.tsbuildinfo"}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}], "projectReferences": [{"path": "libs\\ui\\tsconfig.lib.json", "originalPath": "../../libs/ui/tsconfig.lib.json"}, {"path": "libs\\auth\\tsconfig.lib.json", "originalPath": "../../libs/auth/tsconfig.lib.json"}]}, "extendedFilesHash": "8900268571896142108|", "hash": "12970424305908101486"}, "apps\\web-admin\\tsconfig.json": {"data": {"options": {}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}], "projectReferences": [{"path": "libs\\ui", "originalPath": "../../libs/ui"}, {"path": "libs\\auth", "originalPath": "../../libs/auth"}, {"path": "apps\\web-admin\\tsconfig.app.json", "originalPath": "./tsconfig.app.json"}, {"path": "apps\\web-admin\\tsconfig.spec.json", "originalPath": "./tsconfig.spec.json"}]}, "extendedFilesHash": "8900268571896142108|", "hash": "16084232584204600883"}, "apps\\web-admin\\tsconfig.spec.json": {"data": {"options": {"outDir": "apps\\web-admin\\out-tsc\\jest"}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}], "projectReferences": [{"path": "apps\\web-admin\\tsconfig.app.json", "originalPath": "./tsconfig.app.json"}]}, "extendedFilesHash": "8900268571896142108|", "hash": "6057203337721981610"}, "apps\\web-admin-e2e\\tsconfig.json": {"data": {"options": {"outDir": "apps\\web-admin-e2e\\out-tsc\\playwright"}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}]}, "extendedFilesHash": "8900268571896142108|", "hash": "15001458170383955177"}, "apps\\web-superadmin\\tsconfig.app.json": {"data": {"options": {"rootDir": "apps\\web-superadmin\\src", "outDir": "apps\\web-superadmin\\dist", "tsBuildInfoFile": "apps\\web-superadmin\\dist\\tsconfig.app.tsbuildinfo"}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}], "projectReferences": [{"path": "libs\\ui\\tsconfig.lib.json", "originalPath": "../../libs/ui/tsconfig.lib.json"}, {"path": "libs\\models\\tsconfig.lib.json", "originalPath": "../../libs/models/tsconfig.lib.json"}, {"path": "libs\\auth\\tsconfig.lib.json", "originalPath": "../../libs/auth/tsconfig.lib.json"}]}, "extendedFilesHash": "8900268571896142108|", "hash": "12390187870132128919"}, "apps\\web-superadmin\\tsconfig.e2e.json": {"data": {"options": {"outDir": "apps\\web-superadmin\\out-tsc\\playwright"}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}]}, "extendedFilesHash": "8900268571896142108|", "hash": "16681535235805080818"}, "apps\\web-superadmin\\tsconfig.json": {"data": {"options": {}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}], "projectReferences": [{"path": "libs\\ui", "originalPath": "../../libs/ui"}, {"path": "libs\\models", "originalPath": "../../libs/models"}, {"path": "libs\\auth", "originalPath": "../../libs/auth"}, {"path": "apps\\web-superadmin\\tsconfig.app.json", "originalPath": "./tsconfig.app.json"}, {"path": "apps\\web-superadmin\\tsconfig.spec.json", "originalPath": "./tsconfig.spec.json"}, {"path": "apps\\web-superadmin\\tsconfig.e2e.json", "originalPath": "./tsconfig.e2e.json"}]}, "extendedFilesHash": "8900268571896142108|", "hash": "1662147713237246032"}, "apps\\web-superadmin\\tsconfig.spec.json": {"data": {"options": {"outDir": "apps\\web-superadmin\\out-tsc\\jest"}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}], "projectReferences": [{"path": "apps\\web-superadmin\\tsconfig.app.json", "originalPath": "./tsconfig.app.json"}]}, "extendedFilesHash": "8900268571896142108|", "hash": "6057203337721981610"}, "libs\\auth\\tsconfig.json": {"data": {"options": {}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}], "projectReferences": [{"path": "libs\\utils", "originalPath": "../utils"}, {"path": "libs\\models", "originalPath": "../models"}, {"path": "libs\\auth\\tsconfig.lib.json", "originalPath": "./tsconfig.lib.json"}, {"path": "libs\\auth\\tsconfig.spec.json", "originalPath": "./tsconfig.spec.json"}]}, "extendedFilesHash": "8900268571896142108|", "hash": "3956941568341465959"}, "libs\\auth\\tsconfig.lib.json": {"data": {"options": {"rootDir": "libs\\auth\\src", "outDir": "libs\\auth\\dist", "tsBuildInfoFile": "libs\\auth\\dist\\tsconfig.lib.tsbuildinfo"}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}], "projectReferences": [{"path": "libs\\utils\\tsconfig.lib.json", "originalPath": "../utils/tsconfig.lib.json"}, {"path": "libs\\models\\tsconfig.lib.json", "originalPath": "../models/tsconfig.lib.json"}]}, "extendedFilesHash": "8900268571896142108|", "hash": "7298881631146884418"}, "libs\\auth\\tsconfig.spec.json": {"data": {"options": {"outDir": "libs\\auth\\out-tsc\\jest"}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}], "projectReferences": [{"path": "libs\\auth\\tsconfig.lib.json", "originalPath": "./tsconfig.lib.json"}]}, "extendedFilesHash": "8900268571896142108|", "hash": "12971962995897902409"}, "libs\\models\\tsconfig.json": {"data": {"options": {}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}], "projectReferences": [{"path": "libs\\models\\tsconfig.lib.json", "originalPath": "./tsconfig.lib.json"}, {"path": "libs\\models\\tsconfig.spec.json", "originalPath": "./tsconfig.spec.json"}]}, "extendedFilesHash": "8900268571896142108|", "hash": "12834038490315927699"}, "libs\\models\\tsconfig.lib.json": {"data": {"options": {"rootDir": "libs\\models\\src", "outDir": "libs\\models\\dist", "tsBuildInfoFile": "libs\\models\\dist\\tsconfig.lib.tsbuildinfo"}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}]}, "extendedFilesHash": "8900268571896142108|", "hash": "17233988772328281602"}, "libs\\models\\tsconfig.spec.json": {"data": {"options": {"outDir": "libs\\models\\out-tsc\\jest"}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}], "projectReferences": [{"path": "libs\\models\\tsconfig.lib.json", "originalPath": "./tsconfig.lib.json"}]}, "extendedFilesHash": "8900268571896142108|", "hash": "12971962995897902409"}, "libs\\ui\\tsconfig.json": {"data": {"options": {}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}], "projectReferences": [{"path": "libs\\models", "originalPath": "../models"}, {"path": "libs\\auth", "originalPath": "../auth"}, {"path": "libs\\ui\\tsconfig.lib.json", "originalPath": "./tsconfig.lib.json"}, {"path": "libs\\ui\\tsconfig.spec.json", "originalPath": "./tsconfig.spec.json"}]}, "extendedFilesHash": "8900268571896142108|", "hash": "3143512756470678497"}, "libs\\ui\\tsconfig.lib.json": {"data": {"options": {"rootDir": "libs\\ui\\src", "outDir": "libs\\ui\\dist", "tsBuildInfoFile": "libs\\ui\\dist\\tsconfig.lib.tsbuildinfo"}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}], "projectReferences": [{"path": "libs\\models\\tsconfig.lib.json", "originalPath": "../models/tsconfig.lib.json"}, {"path": "libs\\auth\\tsconfig.lib.json", "originalPath": "../auth/tsconfig.lib.json"}]}, "extendedFilesHash": "8900268571896142108|", "hash": "16960883786419930543"}, "libs\\ui\\tsconfig.spec.json": {"data": {"options": {"outDir": "libs\\ui\\out-tsc\\jest"}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}], "projectReferences": [{"path": "libs\\ui\\tsconfig.lib.json", "originalPath": "./tsconfig.lib.json"}]}, "extendedFilesHash": "8900268571896142108|", "hash": "200392398545240393"}, "libs\\utils\\tsconfig.json": {"data": {"options": {}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}], "projectReferences": [{"path": "libs\\utils\\tsconfig.lib.json", "originalPath": "./tsconfig.lib.json"}, {"path": "libs\\utils\\tsconfig.spec.json", "originalPath": "./tsconfig.spec.json"}]}, "extendedFilesHash": "8900268571896142108|", "hash": "12834038490315927699"}, "libs\\utils\\tsconfig.lib.json": {"data": {"options": {"rootDir": "libs\\utils\\src", "outDir": "libs\\utils\\dist", "tsBuildInfoFile": "libs\\utils\\dist\\tsconfig.lib.tsbuildinfo"}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}]}, "extendedFilesHash": "8900268571896142108|", "hash": "17233988772328281602"}, "libs\\utils\\tsconfig.spec.json": {"data": {"options": {"outDir": "libs\\utils\\out-tsc\\jest"}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}], "projectReferences": [{"path": "libs\\utils\\tsconfig.lib.json", "originalPath": "./tsconfig.lib.json"}]}, "extendedFilesHash": "8900268571896142108|", "hash": "12971962995897902409"}, "tsconfig.json": {"data": {"options": {}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}], "projectReferences": [{"path": "apps\\web-admin-e2e", "originalPath": "./apps/web-admin-e2e"}, {"path": "apps\\web-admin", "originalPath": "./apps/web-admin"}, {"path": "apps\\web-superadmin", "originalPath": "./apps/web-superadmin"}, {"path": "apps\\mobile-agent", "originalPath": "./apps/mobile-agent"}, {"path": "apps\\mobile-buyer", "originalPath": "./apps/mobile-buyer"}, {"path": "apps\\backend-e2e", "originalPath": "./apps/backend-e2e"}, {"path": "apps\\backend", "originalPath": "./apps/backend"}, {"path": "libs\\ui", "originalPath": "./libs/ui"}, {"path": "libs\\auth", "originalPath": "./libs/auth"}, {"path": "libs\\models", "originalPath": "./libs/models"}, {"path": "libs\\utils", "originalPath": "./libs/utils"}]}, "extendedFilesHash": "8900268571896142108|", "hash": "14053782038406953560"}}}