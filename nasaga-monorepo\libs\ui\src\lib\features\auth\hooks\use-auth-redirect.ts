import { useEffect } from 'react';
import { useAuth } from '@nasaga-monorepo/auth';
import { UserRole } from '@nasaga-monorepo/models';

export interface AuthRedirectConfig {
  [key: string]: string;
  superAdmin: string;
  admin: string;
  agent: string;
  buyer: string;
  default: string;
}

/**
 * Default redirect paths for different user roles
 */
export const DEFAULT_REDIRECT_PATHS: AuthRedirectConfig = {
  superAdmin: '/superadmin/dashboard',
  admin: '/admin/dashboard',
  agent: '/agent/dashboard',
  buyer: '/buyer/dashboard',
  default: '/dashboard',
};

/**
 * Hook for handling authentication-based redirects
 */
export const useAuthRedirect = (
  redirectPaths: Partial<AuthRedirectConfig> = {},
  options: {
    redirectOnLogin?: boolean;
    redirectOnLogout?: boolean;
    logoutPath?: string;
  } = {}
) => {
  const { user, loading } = useAuth();
  const {
    redirectOnLogin = true,
    redirectOnLogout = true,
    logoutPath = '/login',
  } = options;

  const paths = { ...DEFAULT_REDIRECT_PATHS, ...redirectPaths };

  /**
   * Get the redirect path for a specific user role
   */
  const getRedirectPath = (role: UserRole): string => {
    return paths[role] || paths.default;
  };

  /**
   * Redirect to the appropriate path based on user role
   */
  const redirectByRole = (role: UserRole) => {
    const redirectPath = getRedirectPath(role);
    window.location.href = redirectPath;
  };

  /**
   * Redirect to login page
   */
  const redirectToLogin = () => {
    window.location.href = logoutPath;
  };

  useEffect(() => {
    // Don't redirect while loading
    if (loading) return;

    if (user && redirectOnLogin) {
      // User is authenticated, redirect to role-based dashboard
      redirectByRole(user.role);
    } else if (!user && redirectOnLogout) {
      // User is not authenticated, redirect to login
      redirectToLogin();
    }
  }, [user, loading, redirectOnLogin, redirectOnLogout]);

  return {
    user,
    loading,
    getRedirectPath,
    redirectByRole,
    redirectToLogin,
  };
};

/**
 * Hook specifically for login success redirects
 */
export const useLoginRedirect = (
  redirectPaths: Partial<AuthRedirectConfig> = {}
) => {
  const { user } = useAuth();
  const paths = { ...DEFAULT_REDIRECT_PATHS, ...redirectPaths };

  const handleLoginSuccess = () => {
    if (user) {
      const redirectPath = paths[user.role] || paths.default;
      window.location.href = redirectPath;
    }
  };

  return { handleLoginSuccess };
};

/**
 * Hook for protecting routes based on user role
 */
export const useRoleBasedAccess = (allowedRoles: UserRole[]) => {
  const { user, loading } = useAuth();

  const hasAccess = user ? allowedRoles.includes(user.role) : false;
  const isLoading = loading;
  const isAuthenticated = !!user;

  return {
    hasAccess,
    isLoading,
    isAuthenticated,
    user,
    userRole: user?.role || null,
  };
};
