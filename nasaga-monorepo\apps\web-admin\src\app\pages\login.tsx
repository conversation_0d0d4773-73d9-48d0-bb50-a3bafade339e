import React from 'react';
import { useNavigate } from 'react-router-dom';
import { LoginPage } from '@nasaga-monorepo/ui';

export const Login: React.FC = () => {
  const navigate = useNavigate();

  const handleSignUp = () => {
    navigate('/register');
  };

  const handleForgotPassword = () => {
    // TODO: Implement forgot password functionality
    console.log('Forgot password clicked');
  };

  const handleLoginSuccess = () => {
    // Redirect will be handled by the auth redirect hook
    console.log('Login successful');
  };

  return (
    <LoginPage
      onSignUp={handleSignUp}
      onForgotPassword={handleForgotPassword}
      onLoginSuccess={handleLoginSuccess}
      variant="default"
    />
  );
};
