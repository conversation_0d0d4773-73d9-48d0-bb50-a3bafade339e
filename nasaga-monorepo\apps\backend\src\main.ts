/**
 * Nasaga Real Estate Management Platform Backend API
 * Express TypeScript app for Cloud Functions deployment
 */

import { initializeFirebaseAdmin } from '@nasaga-monorepo/utils';
import * as functions from 'firebase-functions';
import cors from 'cors';
import express from 'express';
import helmet from 'helmet';
import * as path from 'path';
import { errorHandler } from './middleware/error-handler';
import { requestLogger } from './middleware/request-logger';
import { authenticateToken } from './middleware/auth-middleware';
import adminRoutes from './routes/admin';
import authRoutes from './routes/auth';
import propertyRoutes from './routes/properties';
import userRoutes from './routes/users';

// Initialize Firebase Admin SDK
try {
  initializeFirebaseAdmin();
  console.log('Firebase Admin initialized successfully');
} catch (error) {
  console.error('Failed to initialize Firebase Admin:', error);
  // Don't exit process in Cloud Functions environment
  if (process.env.NODE_ENV !== 'production') {
    process.exit(1);
  }
}

const app = express();

// Security middleware
app.use(helmet());
app.use(
  cors({
    origin:
      process.env['NODE_ENV'] === 'production'
        ? ['https://admin.nasaga.com', 'https://superadmin.nasaga.com']
        : ['http://localhost:4200', 'http://localhost:4201'],
    credentials: true,
  })
);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request logging
app.use(requestLogger);

// Static assets
app.use('/assets', express.static(path.join(__dirname, 'assets')));

// Health check endpoint (public)
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    services: {
      database: 'connected',
      storage: 'connected',
      auth: 'connected',
    },
  });
});

// Public auth routes (no JWT verification required)
app.use('/api/auth', authRoutes); // This includes register, verify-token endpoints

// Register endpoint alias (public - allows self-registration for agents and buyers)
app.use('/register', authRoutes);

// Protected API routes (require JWT verification for specific endpoints)
// Note: Auth routes handle their own authentication where needed
app.use('/api/users', authenticateToken, userRoutes);
app.use('/api/properties', authenticateToken, propertyRoutes);
app.use('/api/admin', authenticateToken, adminRoutes);

// Default API endpoint
app.get('/api', (req, res) => {
  res.json({
    message: 'Welcome to Nasaga Real Estate Management Platform API!',
    version: '1.0.0',
    endpoints: {
      health: '/health',
      register: '/register',
      auth: '/api/auth',
      users: '/api/users',
      properties: '/api/properties',
      admin: '/api/admin',
    },
  });
});

// Error handling middleware (must be last)
app.use(errorHandler);

// Export for Cloud Functions
export const api = functions.https.onRequest(app);

// For local development
if (process.env.NODE_ENV !== 'production') {
  const port = process.env['PORT'] || 3333;
  const server = app.listen(port, () => {
    console.log(`🚀 Nasaga API Server listening at http://localhost:${port}`);
    console.log(`📚 API Documentation: http://localhost:${port}/api`);
    console.log(`🏥 Health Check: http://localhost:${port}/health`);
    console.log(`📝 Register: http://localhost:${port}/register`);
  });

  server.on('error', console.error);
}

// Also export the express app for testing
export default app;
