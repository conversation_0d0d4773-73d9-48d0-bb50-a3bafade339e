import { Route, Routes, Navigate } from 'react-router-dom';
import { AuthProvider } from '@nasaga-monorepo/auth';
import LoginPage from './pages/login';
import UsersDashboard from './pages/users-dashboard';
import DashboardLayout from './components/dashboard-layout';

export function App() {
  return (
    <AuthProvider platform="web">
      <Routes>
        {/* Public Routes */}
        <Route path="/login" element={<LoginPage />} />
        
        {/* Protected Dashboard Routes */}
        <Route path="/dashboard" element={<DashboardLayout />}>
          <Route path="users" element={<UsersDashboard />} />
          {/* Redirect /dashboard to /dashboard/users */}
          <Route index element={<Navigate to="users" replace />} />
        </Route>
        
        {/* Root redirect to login */}
        <Route path="/" element={<Navigate to="/login" replace />} />
        
        {/* Catch all other routes and redirect to login */}
        <Route path="*" element={<Navigate to="/login" replace />} />
      </Routes>
    </AuthProvider>
  );
}

export default App;
