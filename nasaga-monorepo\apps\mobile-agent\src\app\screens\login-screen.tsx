import React from 'react';
import { LoginPage } from '@nasaga-monorepo/ui';

export const LoginScreen: React.FC<{ navigation: any }> = ({ navigation }) => {
  const handleSignUp = () => {
    navigation.navigate('Register');
  };

  const handleForgotPassword = () => {
    // TODO: Implement forgot password functionality
    console.log('Forgot password clicked');
  };

  const handleLoginSuccess = () => {
    // Redirect will be handled by the auth redirect hook
    console.log('Login successful');
  };

  return (
    <LoginPage
      onSignUp={handleSignUp}
      onForgotPassword={handleForgotPassword}
      onLoginSuccess={handleLoginSuccess}
      variant="mobile"
    />
  );
};
