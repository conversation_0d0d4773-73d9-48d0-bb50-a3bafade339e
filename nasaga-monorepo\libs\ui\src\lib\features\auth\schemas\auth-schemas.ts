import * as yup from 'yup';

/**
 * Validation schema for user login
 */
export const loginSchema = yup.object().shape({
  email: yup
    .string()
    .email('Please enter a valid email address')
    .required('Email is required'),
  password: yup
    .string()
    .min(6, 'Password must be at least 6 characters long')
    .required('Password is required'),
});

/**
 * Validation schema for user registration
 */
export const registrationSchema = yup.object().shape({
  email: yup
    .string()
    .email('Please enter a valid email address')
    .required('Email is required'),
  password: yup
    .string()
    .min(6, 'Password must be at least 6 characters long')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      'Password must contain at least one uppercase letter, one lowercase letter, and one number'
    )
    .required('Password is required'),
  confirmPassword: yup
    .string()
    .oneOf([yup.ref('password')], 'Passwords must match')
    .required('Please confirm your password'),
  displayName: yup
    .string()
    .min(2, 'Display name must be at least 2 characters long')
    .max(50, 'Display name must be less than 50 characters')
    .required('Display name is required'),
  role: yup
    .string()
    .oneOf(['agent', 'buyer'], 'Please select a valid role')
    .required('Role is required'),
  phoneNumber: yup
    .string()
    .matches(
      /^[\+]?[1-9][\d]{0,15}$/,
      'Please enter a valid phone number'
    )
    .optional(),
});

/**
 * Type definitions for form data based on schemas
 */
export type LoginFormData = yup.InferType<typeof loginSchema>;
export type RegistrationFormData = yup.InferType<typeof registrationSchema>;
