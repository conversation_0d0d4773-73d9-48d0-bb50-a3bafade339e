import React from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { registrationSchema, RegistrationFormData } from '../schemas/auth-schemas';
import { InputField } from './input-field';
import { SelectField } from './select-field';
import { FormButton } from './form-button';

export interface RegistrationFormProps {
  onSubmit: (data: RegistrationFormData) => Promise<void>;
  onSignIn?: () => void;
  isLoading?: boolean;
  error?: string | null;
  variant?: 'default' | 'mobile';
}

/**
 * Registration form component with validation using React Hook Form and Yup
 */
export const RegistrationForm: React.FC<RegistrationFormProps> = ({
  onSubmit,
  onSignIn,
  isLoading = false,
  error = null,
  variant = 'default',
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<RegistrationFormData>({
    resolver: yupResolver(registrationSchema),
    mode: 'onBlur',
    defaultValues: {
      role: 'buyer',
    },
  });

  const submitHandler: SubmitHandler<RegistrationFormData> = async (data) => {
    try {
      await onSubmit(data);
    } catch (err) {
      // Error handling is managed by parent component
      console.error('Registration error:', err);
    }
  };

  const roleOptions = [
    { value: 'buyer', label: 'I am a Buyer' },
    { value: 'agent', label: 'I am an Agent' },
  ];

  const containerClasses = variant === 'mobile' 
    ? 'w-full max-w-lg mx-auto px-6'
    : 'w-full max-w-lg mx-auto';

  const formClasses = variant === 'mobile'
    ? 'space-y-6'
    : 'space-y-4';

  const titleClasses = variant === 'mobile'
    ? 'text-3xl font-bold text-center text-gray-900 mb-8'
    : 'text-2xl font-bold text-center text-gray-900 mb-6';

  return (
    <div className={containerClasses}>
      <div className="bg-white shadow-lg rounded-lg px-8 py-8">
        <h2 className={titleClasses}>Create an Account</h2>

        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded" role="alert">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit(submitHandler)} className={formClasses}>
          <InputField
            id="displayName"
            label="Full Name"
            type="text"
            variant={variant}
            required
            autoComplete="name"
            error={errors.displayName?.message}
            {...register('displayName')}
          />

          <InputField
            id="email"
            label="Email Address"
            type="email"
            variant={variant}
            required
            autoComplete="email"
            error={errors.email?.message}
            {...register('email')}
          />
          
          <InputField
            id="phoneNumber"
            label="Phone Number (Optional)"
            type="tel"
            variant={variant}
            autoComplete="tel"
            error={errors.phoneNumber?.message}
            {...register('phoneNumber')}
          />

          <InputField
            id="password"
            label="Password"
            type="password"
            variant={variant}
            required
            autoComplete="new-password"
            error={errors.password?.message}
            {...register('password')}
          />

          <InputField
            id="confirmPassword"
            label="Confirm Password"
            type="password"
            variant={variant}
            required
            autoComplete="new-password"
            error={errors.confirmPassword?.message}
            {...register('confirmPassword')}
          />

          <SelectField
            id="role"
            label="I am a..."
            options={roleOptions}
            variant={variant}
            required
            error={errors.role?.message}
            {...register('role')}
          />

          <FormButton
            type="submit"
            isLoading={isLoading || isSubmitting}
            fullWidth
            size={variant === 'mobile' ? 'lg' : 'md'}
          >
            Create Account
          </FormButton>

          {onSignIn && (
            <div className="text-center">
              <span className="text-sm text-gray-600">
                Already have an account?{' '}
                <button
                  type="button"
                  onClick={onSignIn}
                  className="text-blue-600 hover:text-blue-500 focus:outline-none focus:underline font-medium"
                >
                  Sign in
                </button>
              </span>
            </div>
          )}
        </form>
      </div>
    </div>
  );
};
