Compiling with S<PERSON> for @nasaga-monorepo/models...
[1m[31mSWC compilation failed[39m[22m
[1m[31mnode:internal/modules/cjs/loader:1228[39m[22m
[1m[31m  throw err;[39m[22m
[1m[31m  ^[39m[22m
[1m[31m[39m[22m
[1m[31mError: Cannot find module 'C:\Users\<USER>\Desktop\nasaga'[39m[22m
[1m[31m[90m    at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15)[39m[31m[39m[22m
[1m[31m[90m    at Function._load (node:internal/modules/cjs/loader:1055:27)[39m[31m[39m[22m
[1m[31m[90m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m[31m[39m[22m
[1m[31m[90m    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)[39m[31m[39m[22m
[1m[31m[90m    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)[39m[31m[39m[22m
[1m[31m[90m    at node:internal/main/run_main_module:36:49[39m[31m {[39m[22m
[1m[31m  code: [32m'MODULE_NOT_FOUND'[39m[31m,[39m[22m
[1m[31m  requireStack: [][39m[22m
[1m[31m}[39m[22m
[1m[31m[39m[22m
[1m[31mNode.js v22.14.0[39m[22m
[1m[31m[39m[22m
