import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signInWithPopup,
  GoogleAuthProvider,
  signOut,
  onAuthStateChanged,
  User as FirebaseUser,
  UserCredential,
  Auth,
} from 'firebase/auth';
import {
  doc,
  setDoc,
  getDoc,
  Timestamp,
  Firestore,
} from 'firebase/firestore';
import { getFirebaseAuth, getFirebaseFirestore } from '@nasaga-monorepo/utils';
import {
  User,
  UserRole,
  UserRegistrationRequest,
  UserLoginRequest,
  CreateUserData,
  UserAuthData,
} from '@nasaga-monorepo/models';

/**
 * Platform type for determining default user roles
 */
export type Platform = 'web' | 'mobile';

/**
 * Authentication service for handling user authentication and user management
 */
export class AuthService {
  private auth: Auth;
  private firestore: Firestore;
  private platform: Platform;

  constructor(platform: Platform = 'web') {
    this.auth = getFirebaseAuth();
    this.firestore = getFirebaseFirestore();
    this.platform = platform;
  }

  /**
   * Register a new user with email and password
   */
  async registerUser(registrationData: UserRegistrationRequest): Promise<UserAuthData> {
    try {
      // Create user with Firebase Auth
      const userCredential: UserCredential = await createUserWithEmailAndPassword(
        this.auth,
        registrationData.email,
        registrationData.password
      );

      const firebaseUser = userCredential.user;

      // Create user document in Firestore
      const userData: CreateUserData = {
        email: registrationData.email,
        displayName: registrationData.displayName,
        role: registrationData.role,
        phoneNumber: registrationData.phoneNumber,
      };

      await this.createUserDocument(firebaseUser.uid, userData);

      // Return user auth data
      return {
        uid: firebaseUser.uid,
        email: firebaseUser.email!,
        displayName: registrationData.displayName,
        role: registrationData.role,
      };
    } catch (error: any) {
      console.error('Error registering user:', error);
      throw new Error(this.getAuthErrorMessage(error.code));
    }
  }

  /**
   * Sign in user with email and password
   */
  async signInUser(loginData: UserLoginRequest): Promise<UserAuthData> {
    try {
      const userCredential: UserCredential = await signInWithEmailAndPassword(
        this.auth,
        loginData.email,
        loginData.password
      );

      const firebaseUser = userCredential.user;

      // Get user data from Firestore
      const userData = await this.getUserData(firebaseUser.uid);
      if (!userData) {
        throw new Error('User data not found');
      }

      return {
        uid: firebaseUser.uid,
        email: firebaseUser.email!,
        displayName: userData.displayName,
        role: userData.role,
      };
    } catch (error: any) {
      console.error('Error signing in user:', error);
      throw new Error(this.getAuthErrorMessage(error.code));
    }
  }

  /**
   * Sign in user with Google
   */
  async signInWithGoogle(): Promise<UserAuthData> {
    try {
      const provider = new GoogleAuthProvider();
      const userCredential: UserCredential = await signInWithPopup(this.auth, provider);
      const firebaseUser = userCredential.user;

      // Check if user document exists
      let userData = await this.getUserData(firebaseUser.uid);
      
      if (!userData) {
        // First-time sign-up: create user document with platform-based default role
        const defaultRole: UserRole = this.getDefaultRoleForPlatform();
        const newUserData: CreateUserData = {
          email: firebaseUser.email!,
          displayName: firebaseUser.displayName || 'Google User',
          role: defaultRole,
          profilePictureUrl: firebaseUser.photoURL || undefined,
        };

        await this.createUserDocument(firebaseUser.uid, newUserData);
        userData = await this.getUserData(firebaseUser.uid);
      }

      if (!userData) {
        throw new Error('Failed to create or retrieve user data');
      }

      return {
        uid: firebaseUser.uid,
        email: firebaseUser.email!,
        displayName: userData.displayName,
        role: userData.role,
      };
    } catch (error: any) {
      console.error('Error signing in with Google:', error);
      throw new Error(this.getAuthErrorMessage(error.code) || 'Failed to sign in with Google');
    }
  }

  /**
   * Sign out current user
   */
  async signOutUser(): Promise<void> {
    try {
      await signOut(this.auth);
    } catch (error: any) {
      console.error('Error signing out user:', error);
      throw new Error('Failed to sign out');
    }
  }

  /**
   * Get current authenticated user
   */
  getCurrentUser(): FirebaseUser | null {
    return this.auth.currentUser;
  }

  /**
   * Get current user's ID token
   */
  async getCurrentUserToken(): Promise<string | null> {
    const user = this.getCurrentUser();
    if (!user) return null;

    try {
      return await user.getIdToken();
    } catch (error) {
      console.error('Error getting user token:', error);
      return null;
    }
  }

  /**
   * Listen to authentication state changes
   */
  onAuthStateChange(callback: (user: UserAuthData | null) => void): () => void {
    return onAuthStateChanged(this.auth, async (firebaseUser) => {
      if (firebaseUser) {
        try {
          const userData = await this.getUserData(firebaseUser.uid);
          if (userData) {
            callback({
              uid: firebaseUser.uid,
              email: firebaseUser.email!,
              displayName: userData.displayName,
              role: userData.role,
            });
          } else {
            callback(null);
          }
        } catch (error) {
          console.error('Error getting user data:', error);
          callback(null);
        }
      } else {
        callback(null);
      }
    });
  }

  /**
   * Create user document in Firestore
   */
  private async createUserDocument(uid: string, userData: CreateUserData): Promise<void> {
    const now = Timestamp.now();
    const userDoc: User = {
      uid,
      email: userData.email,
      displayName: userData.displayName,
      role: userData.role,
      phoneNumber: userData.phoneNumber,
      profilePictureUrl: userData.profilePictureUrl,
      createdAt: now,
      updatedAt: now,
    };

    await setDoc(doc(this.firestore, 'users', uid), userDoc);
  }

  /**
   * Get user data from Firestore
   */
  async getUserData(uid: string): Promise<User | null> {
    try {
      const userDoc = await getDoc(doc(this.firestore, 'users', uid));
      if (userDoc.exists()) {
        return userDoc.data() as User;
      }
      return null;
    } catch (error) {
      console.error('Error getting user data:', error);
      return null;
    }
  }

  /**
   * Update user role (admin only)
   */
  async updateUserRole(uid: string, role: UserRole): Promise<void> {
    try {
      const userRef = doc(this.firestore, 'users', uid);
      await setDoc(userRef, { 
        role, 
        updatedAt: Timestamp.now() 
      }, { merge: true });
    } catch (error) {
      console.error('Error updating user role:', error);
      throw new Error('Failed to update user role');
    }
  }

  /**
   * Get default role based on platform
   */
  private getDefaultRoleForPlatform(): UserRole {
    return this.platform === 'web' ? 'buyer' : 'agent';
  }

  /**
   * Get user-friendly error messages for Firebase Auth errors
   */
  private getAuthErrorMessage(errorCode: string): string {
    switch (errorCode) {
      case 'auth/email-already-in-use':
        return 'An account with this email already exists';
      case 'auth/invalid-email':
        return 'Invalid email address';
      case 'auth/operation-not-allowed':
        return 'Email/password accounts are not enabled';
      case 'auth/weak-password':
        return 'Password should be at least 6 characters';
      case 'auth/user-disabled':
        return 'This account has been disabled';
      case 'auth/user-not-found':
        return 'No account found with this email';
      case 'auth/wrong-password':
        return 'Incorrect password';
      case 'auth/invalid-credential':
        return 'Invalid email or password';
      case 'auth/too-many-requests':
        return 'Too many failed attempts. Please try again later';
      default:
        return 'An error occurred during authentication';
    }
  }
}
