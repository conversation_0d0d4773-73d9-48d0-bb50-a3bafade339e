import { Request, Response, NextFunction } from 'express';

/**
 * Request logging middleware
 */
export const requestLogger = (req: Request, res: Response, next: NextFunction): void => {
  const start = Date.now();
  
  // Log request
  console.log(`📥 ${req.method} ${req.url} - ${req.ip}`);
  
  // Log request body for non-GET requests (excluding sensitive data)
  if (req.method !== 'GET' && req.body) {
    const logBody = { ...req.body };
    
    // Remove sensitive fields from logs
    if (logBody.password) logBody.password = '[REDACTED]';
    if (logBody.token) logBody.token = '[REDACTED]';
    
    console.log('📄 Request Body:', JSON.stringify(logBody, null, 2));
  }

  // Override res.json to log response
  const originalJson = res.json;
  res.json = function(body: any) {
    const duration = Date.now() - start;
    
    // Log response
    console.log(`📤 ${req.method} ${req.url} - ${res.statusCode} - ${duration}ms`);
    
    // Log response body for errors or in development
    if (res.statusCode >= 400 || process.env['NODE_ENV'] === 'development') {
      const logBody = { ...body };
      
      // Remove sensitive fields from logs
      if (logBody.token) logBody.token = '[REDACTED]';
      if (logBody.data && logBody.data.token) logBody.data.token = '[REDACTED]';
      
      console.log('📄 Response Body:', JSON.stringify(logBody, null, 2));
    }
    
    return originalJson.call(this, body);
  };

  next();
};
