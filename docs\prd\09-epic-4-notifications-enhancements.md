# Epic 4: Real-time Notifications & Platform Enhancements

**Epic Goal**: Implement robust real-time communication channels through push notifications and continuously enhance platform reliability and user experience based on initial feedback and operational insights.

## Story 4.1 Push Notifications for Agent Inquiries

**As an** Agent,  
**I want** to receive push notifications when a buyer inquires about one of my properties,  
**so that** I can respond quickly and not miss potential leads.

### Acceptance Criteria

1.  **1**: Firebase Cloud Messaging (FCM) is configured for the Agent Mobile App.  
2.  **2**: When a buyer expresses interest or inquires about a property (e.g., by tapping "Contact Agent" button), a notification is triggered.  
3.  **3**: The agent associated with the property receives a push notification on their mobile device.  
4.  **4**: The notification content is clear and includes relevant inquiry details (e.g., "New inquiry on [Property Title]").  
5.  **5**: Tapping the notification takes the agent to the relevant section (e.g., inquiry details) within the Agent App.

## Story 4.2 Push Notifications for Buyer Updates

**As a** Buyer,  
**I want** to receive push notifications for updates on my favorited properties,  
**so that** I stay informed about changes (e.g., price drops, status changes).

### Acceptance Criteria

1.  **1**: FCM is configured for the Buyer Mobile App.  
2.  **2**: Buyers receive push notifications when a favorited property's price or status (e.g., 'under offer', 'sold') is updated.  
3.  **3**: The notification content is clear and includes relevant property and update details.  
4.  **4**: Tapping the notification takes the buyer to the specific property detail page within the Buyer App.

## Story 4.3 Platform Analytics Integration & Initial Reporting

**As a** Super Admin,  
**I want** to view basic platform usage analytics,  
**so that** I can monitor overall platform health and user engagement.

### Acceptance Criteria

1.  **1**: Firebase Analytics is integrated into all client applications (web and mobile).  
2.  **2**: The Super Admin dashboard displays key metrics: total users, active users, total properties, number of approved/pending properties, number of favorited properties.  
3.  **3**: Data presented in the dashboard is derived from Firebase Analytics or Firestore aggregated data.  
4.  **4**: The dashboard is performant and displays data accurately.
