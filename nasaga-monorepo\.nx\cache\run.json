{"run": {"command": "nx run backend:build", "startTime": "2025-07-30T12:34:17.695Z", "endTime": "2025-07-30T12:34:23.645Z", "inner": false}, "tasks": [{"taskId": "@nasaga-monorepo/backend:build", "target": "build", "projectName": "@nasaga-monorepo/backend", "hash": "572848617189192136", "startTime": "2025-07-30T12:34:17.736Z", "endTime": "2025-07-30T12:34:23.589Z", "params": "", "cacheStatus": "cache-miss", "status": 0}]}