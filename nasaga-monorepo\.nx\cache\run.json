{"run": {"command": "nx build auth", "startTime": "2025-07-30T13:31:19.512Z", "endTime": "2025-07-30T13:31:20.636Z", "inner": false}, "tasks": [{"taskId": "@nasaga-monorepo/utils:build", "target": "build", "projectName": "@nasaga-monorepo/utils", "hash": "2063315632866559525", "startTime": "2025-07-30T13:31:19.560Z", "endTime": "2025-07-30T13:31:20.619Z", "params": "", "cacheStatus": "cache-miss", "status": 1}, {"taskId": "@nasaga-monorepo/models:build", "target": "build", "projectName": "@nasaga-monorepo/models", "hash": "7294186535834140752", "startTime": "2025-07-30T13:31:19.560Z", "endTime": "2025-07-30T13:31:20.629Z", "params": "", "cacheStatus": "cache-miss", "status": 1}]}