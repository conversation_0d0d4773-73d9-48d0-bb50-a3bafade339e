{"run": {"command": "nx build backend", "startTime": "2025-07-30T13:55:47.573Z", "endTime": "2025-07-30T13:56:00.197Z", "inner": false}, "tasks": [{"taskId": "@nasaga-monorepo/utils:build", "target": "build", "projectName": "@nasaga-monorepo/utils", "hash": "4656431613056215243", "startTime": "2025-07-30T13:55:47.752Z", "endTime": "2025-07-30T13:55:55.994Z", "params": "", "cacheStatus": "cache-miss", "status": 1}, {"taskId": "@nasaga-monorepo/models:build", "target": "build", "projectName": "@nasaga-monorepo/models", "hash": "9913611470080839345", "startTime": "2025-07-30T13:55:47.752Z", "endTime": "2025-07-30T13:55:56.323Z", "params": "", "cacheStatus": "cache-miss", "status": 1}]}