{"name": "@nasaga/ui", "version": "0.0.1", "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"development": "./src/index.ts", "types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.js"}}, "dependencies": {"@mui/material": "^6.0.0", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@mui/icons-material": "^6.0.0", "@mui/lab": "^6.0.0-beta.14", "clsx": "^2.1.1"}}