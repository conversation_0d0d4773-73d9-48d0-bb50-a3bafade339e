# **Unified Project Structure**

`nasaga-monorepo/`  
`├── .github/                    # CI/CD workflows for GitHub Actions`  
`│   └── workflows/`  
`│       ├── ci.yaml             # Continuous Integration pipeline`  
`│       └── deploy.yaml         # Continuous Deployment pipeline`  
`├── apps/                       # Contains individual deployable applications`  
`│   ├── admin-web/              # React web application for Admin dashboard`  
`│   │   ├── src/`  
`│   │   │   ├── assets/         # Static assets (images, fonts)`  
`│   │   │   ├── components/     # Application-specific UI components`  
`│   │   │   ├── configs/        # Application-specific configurations`  
`│   │   │   ├── contexts/       # React Context providers`  
`│   │   │   ├── layouts/        # Common layouts`  
`│   │   │   ├── pages/          # Page-level components/routes`  
`│   │   │   ├── services/       # API client services specific to admin-web`  
`│   │   │   ├── stores/         # Zustand stores for local state`  
`│   │   │   └── utils/          # Admin-web specific utilities`  
`│   │   ├── public/             # Public assets (e.g., index.html, favicon)`  
`│   │   ├── tests/              # Unit and integration tests`  
`│   │   └── project.json        # Nx project configuration`  
`│   ├── super-admin-web/        # React web application for Super Admin dashboard`  
`│   │   ├── src/`  
`│   │   │   ├── assets/`  
`│   │   │   ├── components/`  
`│   │   │   ├── configs/`  
`│   │   │   ├── contexts/`  
`│   │   │   ├── layouts/`  
`│   │   │   ├── pages/`  
`│   │   │   ├── services/`  
`│   │   │   ├── stores/`  
`│   │   │   └── utils/`  
`│   │   ├── public/`  
`│   │   ├── tests/`  
`│   │   └── project.json`  
`│   ├── agent-mobile/           # React Native with Expo mobile application for Agents`  
`│   │   ├── src/`  
`│   │   │   ├── assets/         # Images, fonts, etc.`  
`│   │   │   ├── components/     # Application-specific UI components`  
`│   │   │   ├── configs/`  
`│   │   │   ├── contexts/`  
`│   │   │   ├── hooks/          # Custom React Native hooks`  
`│   │   │   ├── navigation/     # React Navigation setup`  
`│   │   │   ├── screens/        # Screen-level components/routes`  
`│   │   │   ├── services/       # API client services specific to agent-mobile`  
`│   │   │   ├── stores/         # Zustand stores for local state`  
`│   │   │   └── utils/          # Agent-mobile specific utilities`  
`│   │   ├── app.json            # Expo configuration`  
`│   │   ├── tests/              # Unit and integration tests`  
`│   │   └── project.json`  
`│   ├── buyer-mobile/           # React Native with Expo mobile application for Buyers`  
`│   │   ├── src/`  
`│   │   │   ├── assets/`  
`│   │   │   ├── components/`  
`│   │   │   ├── configs/`  
`│   │   │   ├── contexts/`  
`│   │   │   ├── hooks/`  
`│   │   │   ├── navigation/`  
`│   │   │   ├── screens/`  
`│   │   │   ├── services/`  
`│   │   │   ├── stores/`  
`│   │   │   └── utils/`  
`│   │   ├── app.json`  
`│   │   ├── tests/`  
`│   │   └── project.json`  
`│   └── backend/                # Node.js/Express API application`  
`│       ├── src/`  
`│       │   ├── config/         # Firebase Admin SDK setup, environment variables`  
`│       │   ├── controllers/    # Handles incoming API requests, delegates to services`  
`│       │   ├── middleware/     # Express middleware (e.g., authentication, error handling)`  
`│       │   ├── models/         # Data access layer for Firestore interactions`  
`│       │   ├── routes/         # Defines API routes`  
`│       │   ├── services/       # Core business logic`  
`│       │   ├── validation/     # Input validation schemas (e.g., Zod, Joi)`  
`│       │   └── app.ts          # Main Express application setup`  
`│       ├── tests/              # Backend unit and integration tests`  
`│       └── project.json`  
`├── packages/                   # Contains shared libraries`  
`│   ├── ui/                     # Shared UI components (React/React Native compatible)`  
`│   │   ├── src/`  
`│   │   │   ├── common/         # Generic UI components (buttons, inputs)`  
`│   │   │   ├── forms/          # Reusable form components`  
`│   │   │   ├── navigation/     # Shared navigation elements`  
`│   │   │   ├── layouts/        # Shared layout patterns`  
`│   │   │   └── theme/          # Shared styling variables/theme setup`  
`│   │   └── project.json`  
`│   ├── utils/                  # Shared utility functions`  
`│   │   ├── src/`  
`│   │   │   ├── apiClient.ts    # Shared Axios instance with interceptors`  
`│   │   │   ├── formatters.ts   # Data formatting utilities`  
`│   │   │   ├── helpers.ts      # General helpers`  
`│   │   │   └── firebase.ts     # Client-side Firebase initialization (if shared)`  
`│   │   └── project.json`  
`│   └── types/                  # Shared TypeScript types and interfaces`  
`│       ├── src/`  
`│       │   ├── auth.d.ts       # Auth related types`  
`│       │   ├── common.d.ts     # General types`  
`│       │   ├── user.d.ts       # User model interface`  
`│       │   └── property.d.ts   # Property model interface`  
`│       └── project.json`  
`├── infrastructure/             # Infrastructure as Code definitions (e.g., Terraform if used)`  
`│   └── firebase/               # Firebase project configuration / deployments`  
`├── scripts/                    # General monorepo-level build/deploy scripts`  
`├── docs/                       # Project documentation`  
`│   ├── prd.md`  
`│   ├── front-end-spec.md       # Placeholder (should be generated by UX Expert)`  
`│   └── fullstack-architecture.md`  
`├── .env.example                # Template for environment variables`  
`├── package.json                # Root package.json (Nx managed workspaces)`  
`├── nx.json                     # Nx workspace configuration`  
`├── metro.config.js             # Metro bundler configuration for React Native apps in monorepo`  
`├── .npmrc                      # npm configuration (e.g., node-linker=hoisted for pnpm compatibility)`  
`└── README.md`
