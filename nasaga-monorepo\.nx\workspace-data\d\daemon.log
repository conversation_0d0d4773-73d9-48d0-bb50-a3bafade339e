[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:10.084Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\dc5fb78e19b1f076be7c\d.sock
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:10.095Z - [WATCHER]: Subscribed to changes within: C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo (native)
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:10.103Z - Established a connection. Number of open connections: 1
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:10.105Z - Established a connection. Number of open connections: 2
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:10.110Z - Closed a connection. Number of open connections: 1
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:10.117Z - [REQUEST]: Client Request for Project Graph Received
Time for 'plugin worker 19200 code loading' 20.934700000000007
Time for 'plugin worker 12384 code loading' 29.530200000000008
Time for 'plugin worker 13632 code loading' 36.8373
Time for 'plugin worker 7604 code loading' 54.11280000000001
Time for 'plugin worker 12440 code loading' 72.99690000000001
Time for 'plugin worker 2692 code loading' 51.8304
Time for 'plugin worker 18300 code loading' 18.7568
Time for 'plugin worker 2004 code loading' 18.179399999999987
Time for 'plugin worker 12736 code loading' 50.372799999999984
Time for 'plugin worker 16800 code loading' 17.662200000000013
Time for 'plugin worker 15668 code loading' 18.733900000000006
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:12.016Z - Time taken for 'Load Nx Plugin: C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\node_modules\nx\src\plugins\package-json' 1857.0661999999998ms
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:12.100Z - Time taken for 'Load Nx Plugin: C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\node_modules\nx\src\plugins\project-json\build-nodes\project-json' 1940.784ms
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:12.212Z - Time taken for 'loadDefaultNxPlugins' 2055.979ms
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:12.376Z - Time taken for 'Load Nx Plugin: @nx/jest/plugin' 2232.1625999999997ms
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:12.491Z - Time taken for 'Load Nx Plugin: @nx/eslint/plugin' 2360.5449ms
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:12.498Z - Time taken for 'Load Nx Plugin: @nx/js/typescript' 2377.2583999999997ms
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:12.750Z - Time taken for 'Load Nx Plugin: @nx/vite/plugin' 2615.6628ms
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:12.791Z - Time taken for 'Load Nx Plugin: @nx/webpack/plugin' 2639.4552999999996ms
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:12.852Z - Time taken for 'Load Nx Plugin: @nx/playwright/plugin' 2712.9751ms
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:12.871Z - Time taken for 'Load Nx Plugin: @nx/react/router-plugin' 2744.052ms
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.021Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.021Z - [REQUEST]: 
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.021Z - [REQUEST]: 
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.030Z - No files changed, but no in-memory cached project graph found. Recomputing it...
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.083Z - Time taken for 'loadSpecifiedNxPlugins' 2899.1172ms
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.085Z - Established a connection. Number of open connections: 2
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.085Z - Established a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.086Z - Established a connection. Number of open connections: 4
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.086Z - Established a connection. Number of open connections: 5
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.087Z - Closed a connection. Number of open connections: 4
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.088Z - Closed a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.089Z - Established a connection. Number of open connections: 4
Time for 'nx/core/project-json:createNodes' 0.5731000000000677
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.102Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.110Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.113Z - Established a connection. Number of open connections: 5
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.113Z - Established a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.114Z - Established a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.114Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.115Z - Handled HASH_GLOB. Handling time: 8. Response time: 13.
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.115Z - Established a connection. Number of open connections: 8
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.115Z - Done responding to the client handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.115Z - Handled GLOB. Handling time: 4. Response time: 5.
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.116Z - Closed a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.116Z - Closed a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.116Z - Closed a connection. Number of open connections: 5
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.117Z - Established a connection. Number of open connections: 6
Time for 'total for sendMessageToDaemon()' 34.23720000000003
Time for 'deserialize daemon response' 0.09320000000025175
Time for '@nx/react/router-plugin:createNodes' 81.29189999999971
Time for 'total for sendMessageToDaemon()' 41.46260000000029
Time for 'deserialize daemon response' 0.059500000000298314
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.127Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.136Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.137Z - Closed a connection. Number of open connections: 5
Time for 'nx/core/package-json:createNodes' 53.55650000000014
Time for 'nx/js/dependencies-and-lockfile:createNodes' 104.07650000000012
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.210Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.211Z - Established a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.211Z - Established a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.212Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.212Z - Handled HASH_GLOB. Handling time: 6. Response time: 85.
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.212Z - Established a connection. Number of open connections: 8
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.213Z - Established a connection. Number of open connections: 9
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.213Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.213Z - Handled HASH_GLOB. Handling time: 5. Response time: 77.
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.214Z - Closed a connection. Number of open connections: 8
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.227Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.227Z - Handled HASH_GLOB. Handling time: 68. Response time: 17.
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.246Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.276Z - [REQUEST]: Responding to the client. handleHashGlob
Time for 'total for sendMessageToDaemon()' 168.1342999999997
Time for 'deserialize daemon response' 0.08439999999973224
Time for 'total for sendMessageToDaemon()' 128.61389999999983
Time for 'deserialize daemon response' 0.09810000000015862
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.391Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.392Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.392Z - Handled HASH_GLOB. Handling time: 15. Response time: 146.
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.392Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.392Z - Handled HASH_GLOB. Handling time: 8. Response time: 116.
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.400Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.400Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.400Z - Handled HASH_GLOB. Handling time: 111. Response time: 9.
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.401Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.401Z - Handled HASH_GLOB. Handling time: 4. Response time: 1.
Time for 'total for sendMessageToDaemon()' 120.9041000000002
Time for 'deserialize daemon response' 0.03290000000015425
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.414Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.415Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.415Z - Handled HASH_GLOB. Handling time: 8. Response time: 1.
Time for 'total for sendMessageToDaemon()' 13.440599999999904
Time for 'deserialize daemon response' 0.2055000000000291
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.423Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.424Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T13:30:13.425Z - Handled HASH_GLOB. Handling time: 2. Response time: 2.
Time for '@nx/expo/plugin:createNodes' 346.9363000000003
Time for 'total for sendMessageToDaemon()' 8.622600000000148
Time for 'deserialize daemon response' 0.05050000000028376
