[NX v21.3.9 Daemon Server] - 2025-07-30T11:59:01.671Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\dc5fb78e19b1f076be7c\d.sock
[NX v21.3.9 Daemon Server] - 2025-07-30T11:59:01.682Z - [WATCHER]: Subscribed to changes within: C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo (native)
[NX v21.3.9 Daemon Server] - 2025-07-30T11:59:01.689Z - Established a connection. Number of open connections: 1
[NX v21.3.9 Daemon Server] - 2025-07-30T11:59:01.694Z - Closed a connection. Number of open connections: 0
[NX v21.3.9 Daemon Server] - 2025-07-30T11:59:01.695Z - Established a connection. Number of open connections: 1
[NX v21.3.9 Daemon Server] - 2025-07-30T11:59:01.700Z - [REQUEST]: Client Request for Project Graph Received
Time for 'plugin worker 17452 code loading' 16.061
Time for 'plugin worker 8068 code loading' 17.400099999999995
Time for 'plugin worker 12440 code loading' 18.0708
Time for 'plugin worker 9924 code loading' 19.275799999999997
[NX v21.3.9 Daemon Server] - 2025-07-30T11:59:02.418Z - Time taken for 'Load Nx Plugin: C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\node_modules\nx\src\plugins\project-json\build-nodes\project-json' 709.3619ms
[NX v21.3.9 Daemon Server] - 2025-07-30T11:59:02.420Z - Time taken for 'Load Nx Plugin: C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\node_modules\nx\src\plugins\package-json' 712.5003ms
[NX v21.3.9 Daemon Server] - 2025-07-30T11:59:02.489Z - Time taken for 'loadDefaultNxPlugins' 782.9804000000001ms
[NX v21.3.9 Daemon Server] - 2025-07-30T11:59:02.731Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.3.9 Daemon Server] - 2025-07-30T11:59:02.732Z - [REQUEST]: 
[NX v21.3.9 Daemon Server] - 2025-07-30T11:59:02.732Z - [REQUEST]: 
[NX v21.3.9 Daemon Server] - 2025-07-30T11:59:02.737Z - No files changed, but no in-memory cached project graph found. Recomputing it...
[NX v21.3.9 Daemon Server] - 2025-07-30T11:59:02.748Z - Time taken for 'loadSpecifiedNxPlugins' 1027.4476ms
Time for 'nx/core/project-json:createNodes' 1.001700000000028
Time for 'nx/core/package-json:createNodes' 11.261799999999994
Time for 'nx/js/dependencies-and-lockfile:createNodes' 14.346400000000017
Time for '@nx/js/typescript:createNodes' 383.17319999999995
[NX v21.3.9 Daemon Server] - 2025-07-30T11:59:03.165Z - Time taken for 'build-project-configs' 394.09169999999995ms
[NX v21.3.9 Daemon Server] - 2025-07-30T11:59:03.170Z - Time taken for '@nx/js/typescript:createDependencies' 7.323500000000422ms
Time for 'build typescript dependencies' 0.434099999999944
[NX v21.3.9 Daemon Server] - 2025-07-30T11:59:03.226Z - Wrote project graph cache to C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\.nx\workspace-data\project-graph.json
[NX v21.3.9 Daemon Server] - 2025-07-30T11:59:03.227Z - [SYNC]: collect registered sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T11:59:03.229Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T11:59:03.229Z - Time taken for 'total for creating and serializing project graph' 1529.3657999999998ms
[NX v21.3.9 Daemon Server] - 2025-07-30T11:59:03.231Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T11:59:03.231Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1530. Response time: 2.
[NX v21.3.9 Daemon Server] - 2025-07-30T11:59:03.241Z - Closed a connection. Number of open connections: 0
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:42.049Z - [WATCHER]: package.json was modified
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:42.052Z - [WATCHER]: Processing file changes in outputs
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:42.165Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:42.165Z - [REQUEST]: package.json
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:42.165Z - [REQUEST]: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:42.176Z - Time taken for 'hash changed files from watcher' 2.4223000000056345ms
Time for 'nx/core/project-json:createNodes' 1.0868999999947846
Time for 'nx/core/package-json:createNodes' 4.844899999996414
Time for 'nx/js/dependencies-and-lockfile:createNodes' 12.745900000008987
Time for '@nx/js/typescript:createNodes' 19.086799999990035
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:42.204Z - Time taken for 'build-project-configs' 25.10809999999765ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:42.206Z - Time taken for '@nx/js/typescript:createDependencies' 5.171100000006845ms
Time for 'build typescript dependencies' 0.08289999999396969
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:42.246Z - Wrote project graph cache to C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\.nx\workspace-data\project-graph.json
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:42.246Z - [SYNC]: collect registered sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:42.247Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:42.248Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:42.248Z - Time taken for 'total execution time for createProjectGraph()' 28.639899999994668ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:44.758Z - [WATCHER]: Processing file changes in outputs
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:44.763Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo (sources)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:44.764Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo (outputs)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:44.766Z - Server stopped because: "LOCK_FILES_CHANGED"
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:53.043Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\dc5fb78e19b1f076be7c\d.sock
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:53.049Z - [WATCHER]: Subscribed to changes within: C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo (native)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:53.054Z - Established a connection. Number of open connections: 1
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:53.056Z - Established a connection. Number of open connections: 2
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:53.059Z - Closed a connection. Number of open connections: 1
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:53.063Z - [REQUEST]: Client Request for Project Graph Received
Time for 'plugin worker 7508 code loading' 15.634199999999993
Time for 'plugin worker 12160 code loading' 14.191500000000005
Time for 'plugin worker 11988 code loading' 21.756499999999996
Time for 'plugin worker 14872 code loading' 16.506500000000003
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:53.735Z - Time taken for 'Load Nx Plugin: C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\node_modules\nx\src\plugins\project-json\build-nodes\project-json' 663.7268ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:53.750Z - Time taken for 'Load Nx Plugin: C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\node_modules\nx\src\plugins\package-json' 679.7412ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:53.830Z - Time taken for 'loadDefaultNxPlugins' 760.8005999999999ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:54.071Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:54.072Z - [REQUEST]: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:54.072Z - [REQUEST]: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:54.075Z - No files changed, but no in-memory cached project graph found. Recomputing it...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:54.087Z - Time taken for 'loadSpecifiedNxPlugins' 1005.5961000000001ms
Time for 'nx/core/project-json:createNodes' 0.43829999999991287
Time for '@nx/js/typescript:createNodes' 14.262399999999957
Time for 'nx/core/package-json:createNodes' 13.714600000000019
Time for 'nx/js/dependencies-and-lockfile:createNodes' 26.581299999999942
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:54.171Z - Time taken for 'build-project-configs' 46.70879999999988ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:54.175Z - Time taken for '@nx/js/typescript:createDependencies' 8.076699999999846ms
Time for 'build typescript dependencies' 0.6044999999999163
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:54.302Z - Wrote project graph cache to C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\.nx\workspace-data\project-graph.json
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:54.303Z - [SYNC]: collect registered sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:54.305Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:54.307Z - Time taken for 'total for creating and serializing project graph' 1242.6853ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:54.309Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:00:54.309Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1243. Response time: 4.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:01:59.531Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:01:59.532Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:01:59.533Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:01:59.534Z - Time taken for 'total for creating and serializing project graph' 1.179999999993015ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:01:59.535Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:01:59.535Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 2.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:01:59.547Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:01:59.547Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:01:59.548Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:01:59.548Z - Time taken for 'total for creating and serializing project graph' 0.8298999999969965ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:01:59.549Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:01:59.549Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 1.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:01:59.563Z - [REQUEST]: Responding to the client. handleMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:01:59.563Z - Done responding to the client handleMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:01:59.563Z - Handled MULTI_GLOB. Handling time: 1. Response time: 0.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:01:59.570Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:01:59.571Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:01:59.571Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:01:59.623Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:01:59.624Z - Done responding to the client handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:01:59.624Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:01:59.632Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:01:59.632Z - Done responding to the client handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:01:59.632Z - Handled GLOB. Handling time: 1. Response time: 0.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:00.816Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:00.817Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:00.817Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:00.818Z - Time taken for 'total for creating and serializing project graph' 1.0025000000023283ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:00.819Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:00.819Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:00.831Z - [REQUEST]: Responding to the client. handleMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:00.832Z - Done responding to the client handleMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:00.832Z - Handled MULTI_GLOB. Handling time: 0. Response time: 1.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:00.837Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:00.838Z - Done responding to the client handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:00.838Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:00.841Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:00.842Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:00.842Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:01.457Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:01.459Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:01.459Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:01.460Z - Time taken for 'total for creating and serializing project graph' 1.2284999999974389ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:01.461Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:01.461Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 2.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:17.647Z - Established a connection. Number of open connections: 2
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:17.647Z - Closed a connection. Number of open connections: 1
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:17.648Z - Established a connection. Number of open connections: 2
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:17.650Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:17.651Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:17.651Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:17.652Z - Time taken for 'total for creating and serializing project graph' 1.0160000000032596ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:17.653Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:17.653Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:17.737Z - [REQUEST]: Responding to the client. handleMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:17.737Z - Done responding to the client handleMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:17.737Z - Handled MULTI_GLOB. Handling time: 1. Response time: 0.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:17.745Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:17.746Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:17.746Z - Handled HASH_GLOB. Handling time: 0. Response time: 1.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:17.766Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:17.767Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:17.767Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:17.768Z - Time taken for 'total for creating and serializing project graph' 1.2406999999948312ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:17.769Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:17.769Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 2.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.249Z - Established a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.250Z - Closed a connection. Number of open connections: 2
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.250Z - Established a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.253Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.253Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.254Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.254Z - Time taken for 'total for creating and serializing project graph' 0.7219000000040978ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.255Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.255Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 1.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.265Z - [REQUEST]: Responding to the client. handleMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.265Z - Done responding to the client handleMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.265Z - Handled MULTI_GLOB. Handling time: 1. Response time: 0.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.357Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.358Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.358Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.358Z - Time taken for 'total for creating and serializing project graph' 1.0596999999979744ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.359Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.360Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.388Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.389Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.390Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.391Z - Time taken for 'total for creating and serializing project graph' 1.2527000000118278ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.392Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.392Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 2.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.502Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.503Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.503Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.504Z - Time taken for 'total for creating and serializing project graph' 0.9499999999970896ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.504Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.505Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.512Z - [REQUEST]: Responding to the client. handleMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.512Z - Done responding to the client handleMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.513Z - Handled MULTI_GLOB. Handling time: 1. Response time: 1.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.516Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.516Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.516Z - Handled HASH_GLOB. Handling time: 0. Response time: 0.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.571Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.572Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.572Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.574Z - Time taken for 'total for creating and serializing project graph' 1.0124999999970896ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.575Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.575Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 3.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.988Z - [WATCHER]: 0 file(s) created or restored, 28 file(s) modified, 0 file(s) deleted
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.992Z - [WATCHER]: Processing file changes in outputs
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.994Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo (sources)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.994Z - Closed a connection. Number of open connections: 2
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.994Z - Closed a connection. Number of open connections: 1
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.994Z - Closed a connection. Number of open connections: 0
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.995Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo (outputs)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:02:37.996Z - Server stopped because: "Stopping the daemon the set of ignored files changed (native)"
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:38.308Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\dc5fb78e19b1f076be7c\d.sock
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:38.319Z - [WATCHER]: Subscribed to changes within: C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo (native)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:38.326Z - Established a connection. Number of open connections: 1
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:38.327Z - Established a connection. Number of open connections: 2
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:38.334Z - Closed a connection. Number of open connections: 1
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:38.338Z - [REQUEST]: Client Request for Project Graph Received
Time for 'plugin worker 9924 code loading' 26.4649
Time for 'plugin worker 8068 code loading' 18.361900000000006
Time for 'plugin worker 17392 code loading' 21.442399999999992
Time for 'plugin worker 18256 code loading' 32.56230000000001
Time for 'plugin worker 4628 code loading' 23.212300000000013
Time for 'plugin worker 13232 code loading' 22.86529999999999
Time for 'plugin worker 15224 code loading' 27.7299
Time for 'plugin worker 6100 code loading' 26.583399999999997
Time for 'plugin worker 14704 code loading' 20.64460000000001
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:39.675Z - Time taken for 'Load Nx Plugin: C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\node_modules\nx\src\plugins\package-json' 1312.2094000000002ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:39.692Z - Time taken for 'Load Nx Plugin: C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\node_modules\nx\src\plugins\project-json\build-nodes\project-json' 1328.6414ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:39.805Z - Time taken for 'loadDefaultNxPlugins' 1444.3729ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.057Z - Time taken for 'Load Nx Plugin: @nx/eslint/plugin' 1710.9043000000001ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.095Z - Time taken for 'Load Nx Plugin: @nx/js/typescript' 1754.2918ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.121Z - Time taken for 'Load Nx Plugin: @nx/jest/plugin' 1764.2780000000002ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.304Z - Time taken for 'Load Nx Plugin: @nx/playwright/plugin' 1950.8693999999998ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.307Z - Time taken for 'Load Nx Plugin: @nx/react/router-plugin' 1963.8811ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.318Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.318Z - [REQUEST]: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.318Z - [REQUEST]: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.331Z - No files changed, but no in-memory cached project graph found. Recomputing it...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.353Z - Time taken for 'loadSpecifiedNxPlugins' 1975.9148ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.354Z - Established a connection. Number of open connections: 2
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.356Z - Established a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.357Z - Closed a connection. Number of open connections: 2
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.358Z - Closed a connection. Number of open connections: 1
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.359Z - Established a connection. Number of open connections: 2
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.359Z - Established a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.360Z - Established a connection. Number of open connections: 4
Time for 'nx/core/project-json:createNodes' 0.6329000000000633
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.376Z - [REQUEST]: Responding to the client. handleHashMultiGlob
Time for 'nx/core/package-json:createNodes' 27.562599999999975
Time for '@nx/react/router-plugin:createNodes' 44.21370000000002
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.385Z - [REQUEST]: Responding to the client. handleGlob
Time for 'total for sendMessageToDaemon()' 20.78449999999998
Time for 'deserialize daemon response' 0.0706000000000131
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.387Z - Closed a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.389Z - Established a connection. Number of open connections: 4
Time for 'total for sendMessageToDaemon()' 28.11180000000013
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.389Z - Established a connection. Number of open connections: 5
Time for 'deserialize daemon response' 0.08670000000006439
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.390Z - Established a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.391Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.391Z - Handled HASH_GLOB. Handling time: 1. Response time: 15.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.391Z - Established a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.392Z - Done responding to the client handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.392Z - Handled GLOB. Handling time: 2. Response time: 7.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.392Z - Closed a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.393Z - Closed a connection. Number of open connections: 5
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.394Z - Established a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.403Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.410Z - [REQUEST]: Responding to the client. handleHashGlob
Time for 'nx/js/dependencies-and-lockfile:createNodes' 55.163299999999936
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.425Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.439Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.440Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.440Z - Handled HASH_GLOB. Handling time: 4. Response time: 37.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.440Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.440Z - Handled HASH_GLOB. Handling time: 1. Response time: 30.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.448Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.449Z - Handled HASH_GLOB. Handling time: 7. Response time: 24.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.450Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:40.451Z - Handled HASH_GLOB. Handling time: 1. Response time: 12.
Time for 'total for sendMessageToDaemon()' 54.256399999999985
Time for 'deserialize daemon response' 0.08639999999991232
Time for '@nx/js/typescript:createNodes' 721.7195999999999
Time for '@nx/vite/plugin:createNodes' 1241.3629
Time for 'total for sendMessageToDaemon()' 15.232500000000073
Time for 'deserialize daemon response' 0.03809999999998581
Time for '@nx/jest/plugin:createNodes' 2531.2715
Time for 'total for sendMessageToDaemon()' 57.40690000000018
Time for 'deserialize daemon response' 0.06120000000009895
(node:13232) [DEP0180] DeprecationWarning: fs.Stats constructor is deprecated.
(Use `node --trace-deprecation ...` to show where the warning was created)
Time for 'total for sendMessageToDaemon()' 42.60480000000007
Time for 'deserialize daemon response' 0.08819999999991524
(node:4628) [DEP0180] DeprecationWarning: fs.Stats constructor is deprecated.
(Use `node --trace-deprecation ...` to show where the warning was created)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:44.674Z - [REQUEST]: Responding to the client. handleNxWorkspaceFiles
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:44.675Z - Done responding to the client handleNxWorkspaceFiles
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:44.675Z - Handled GET_FILES_IN_DIRECTORY. Handling time: 1. Response time: 1.
Time for '@nx/playwright/plugin:createNodes' 4340.2976
Time for 'total for sendMessageToDaemon()' 8.509799999999814
Time for 'deserialize daemon response' 0.03780000000006112
Time for '@nx/eslint/plugin:createNodes' 5510.566
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:45.926Z - Time taken for 'build-project-configs' 5542.679300000001ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:45.930Z - Time taken for '@nx/js/typescript:createDependencies' 17.97359999999935ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:45.931Z - Time taken for '@nx/vite/plugin:createDependencies' 13.704900000000634ms
Time for 'build typescript dependencies' 52.84619999999995
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:46.156Z - Wrote project graph cache to C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\.nx\workspace-data\project-graph.json
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:46.156Z - [SYNC]: collect registered sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:46.159Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:46.159Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:46.160Z - Time taken for 'total for creating and serializing project graph' 7820.8163ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:46.163Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:46.163Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 7821. Response time: 4.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:46.265Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:46.862Z - Established a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:46.863Z - Established a connection. Number of open connections: 8
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:46.863Z - Closed a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:46.867Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:46.868Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:46.869Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:46.870Z - Time taken for 'total for creating and serializing project graph' 1.5451999999986583ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:46.888Z - [SYNC]: @nx/js:typescript-sync changes: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:46.888Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 622.1058000000012ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:46.889Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:46.889Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 20.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:47.151Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:47.152Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:47.152Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:47.153Z - Time taken for 'total for creating and serializing project graph' 1.2299999999995634ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:47.155Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:47.155Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 3.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:47.167Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:47.168Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:47.168Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:47.169Z - Time taken for 'total for creating and serializing project graph' 1.0936000000001513ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:47.171Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:47.171Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 3.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:48.387Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:48.388Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:48.388Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:48.390Z - Time taken for 'total for creating and serializing project graph' 1.001299999999901ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:48.391Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:48.391Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 3.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:48.429Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:48.431Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:48.431Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:48.434Z - Time taken for 'total for creating and serializing project graph' 2.035599999999249ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:48.435Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:48.435Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 4.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:48.576Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:48.577Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:48.578Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:48.581Z - Time taken for 'total for creating and serializing project graph' 1.2183999999997468ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:48.583Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:48.583Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 5.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:49.299Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:49.301Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:49.301Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:49.302Z - Time taken for 'total for creating and serializing project graph' 1.1838000000007014ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:49.303Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:49.303Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 2.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:49.417Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:49.418Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:49.418Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:49.419Z - Time taken for 'total for creating and serializing project graph' 1.306300000000192ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:49.420Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:49.420Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:49.475Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:49.476Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:49.477Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:49.478Z - Time taken for 'total for creating and serializing project graph' 1.3258999999998196ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:49.479Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:49.479Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 2.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:49.807Z - Closed a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:49.837Z - [WATCHER]: 0 file(s) created or restored, 18 file(s) modified, 0 file(s) deleted
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:49.841Z - [WATCHER]: Processing file changes in outputs
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:49.951Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:49.951Z - [REQUEST]: apps/web-superadmin/jest.config.ts,apps/web-superadmin/vite.config.ts,apps/web-superadmin/src/main.tsx,apps/web-superadmin/src/styles.css,apps/web-superadmin/src/app/app.spec.tsx,apps/web-superadmin/src/app/app.tsx,apps/web-superadmin/tsconfig.app.json,apps/web-superadmin/eslint.config.mjs,tsconfig.json,apps/web-superadmin/tsconfig.spec.json,apps/web-superadmin/index.html,apps/web-superadmin/package.json,apps/web-superadmin/src/assets/.gitkeep,apps/web-superadmin/tailwind.config.js,apps/web-superadmin/src/app/nx-welcome.tsx,apps/web-superadmin/postcss.config.js,apps/web-superadmin/public/favicon.ico,apps/web-superadmin/tsconfig.json
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:49.951Z - [REQUEST]: 
Time for 'nx/core/project-json:createNodes' 0.9249999999992724
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:49.990Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:49.992Z - Time taken for 'hash changed files from watcher' 3.8075000000008004ms
Time for '@nx/playwright/plugin:createNodes' 15.57929999999942
Time for 'total for sendMessageToDaemon()' 10.473400000000765
Time for 'deserialize daemon response' 0.04550000000017462
Time for 'nx/core/package-json:createNodes' 10.166900000000169
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:50.004Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:50.008Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:50.009Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:50.010Z - Handled HASH_GLOB. Handling time: 3. Response time: 20.
Time for 'total for sendMessageToDaemon()' 20.281600000000253
Time for 'deserialize daemon response' 0.05769999999938591
Time for 'total for sendMessageToDaemon()' 32.47940000000017
Time for 'deserialize daemon response' 0.046000000000276486
Time for 'nx/js/dependencies-and-lockfile:createNodes' 21.315099999999802
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:50.014Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:50.016Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:50.016Z - Handled HASH_GLOB. Handling time: 5. Response time: 12.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:50.016Z - Done responding to the client handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:50.016Z - Handled GLOB. Handling time: 1. Response time: 8.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:50.019Z - [REQUEST]: Responding to the client. handleHashMultiGlob
Time for '@nx/react/router-plugin:createNodes' 55.29880000000048
Time for 'total for sendMessageToDaemon()' 18.717399999999543
Time for 'deserialize daemon response' 0.048199999999269494
Time for '@nx/js/typescript:createNodes' 61.06049999999959
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:50.037Z - [REQUEST]: Responding to the client. handleHashMultiGlob
Time for 'total for sendMessageToDaemon()' 27.08860000000095
Time for 'deserialize daemon response' 0.03470000000015716
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:50.049Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:50.050Z - Handled HASH_GLOB. Handling time: 2. Response time: 36.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:50.050Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:50.051Z - Handled HASH_GLOB. Handling time: 0. Response time: 32.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:50.051Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:50.051Z - Handled HASH_GLOB. Handling time: 15. Response time: 14.
Time for '@nx/jest/plugin:createNodes' 125.29269999999997
Time for 'total for sendMessageToDaemon()' 23.72490000000107
Time for 'deserialize daemon response' 0.053600000001097214
Time for '@nx/eslint/plugin:createNodes' 184.69989999999962
Time for '@nx/vite/plugin:createNodes' 214.07359999999971
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:50.232Z - Time taken for 'build-project-configs' 234.5722999999998ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:50.234Z - Time taken for '@nx/js/typescript:createDependencies' 17.386399999999412ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:50.235Z - Time taken for '@nx/vite/plugin:createDependencies' 13.800699999999779ms
Time for 'build typescript dependencies' 27.78769999999895
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:50.342Z - Wrote project graph cache to C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\.nx\workspace-data\project-graph.json
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:50.342Z - [SYNC]: collect registered sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:50.344Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:50.344Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:50.344Z - Time taken for 'total execution time for createProjectGraph()' 100.32679999999891ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:50.548Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:50.555Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:50.556Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:50.556Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:50.557Z - Time taken for 'total for creating and serializing project graph' 1.3058000000000902ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:50.579Z - [SYNC]: @nx/js:typescript-sync changes: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:50.579Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 30.286299999999756ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:50.580Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:05:50.580Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 24.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:01.864Z - [WATCHER]: package.json was modified
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:01.865Z - [WATCHER]: Processing file changes in outputs
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:01.980Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:01.980Z - [REQUEST]: package.json
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:01.980Z - [REQUEST]: 
Time for 'nx/core/project-json:createNodes' 0.4467000000004191
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:02.008Z - [REQUEST]: Responding to the client. handleHashMultiGlob
Time for '@nx/react/router-plugin:createNodes' 27.376399999993737
Time for 'total for sendMessageToDaemon()' 16.252700000011828
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:02.017Z - [REQUEST]: Responding to the client. handleGlob
Time for 'deserialize daemon response' 0.044800000003306195
Time for 'total for sendMessageToDaemon()' 20.11610000001383
Time for 'deserialize daemon response' 0.055600000006961636
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:02.022Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:02.023Z - Time taken for 'hash changed files from watcher' 0.9928999999974621ms
Time for 'nx/core/package-json:createNodes' 25.71020000000135
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:02.030Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:02.030Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:02.030Z - Handled HASH_GLOB. Handling time: 1. Response time: 22.
Time for '@nx/playwright/plugin:createNodes' 34.1024999999936
Time for 'total for sendMessageToDaemon()' 22.167300000000978
Time for 'deserialize daemon response' 0.03820000000996515
Time for '@nx/jest/plugin:createNodes' 34.37930000000051
Time for 'total for sendMessageToDaemon()' 22.41370000000461
Time for 'deserialize daemon response' 0.05040000000735745
Time for 'nx/js/dependencies-and-lockfile:createNodes' 24.207499999989523
Time for '@nx/js/typescript:createNodes' 50.453899999993155
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:02.039Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:02.040Z - Done responding to the client handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:02.040Z - Handled GLOB. Handling time: 4. Response time: 23.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:02.046Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:02.047Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:02.047Z - Handled HASH_GLOB. Handling time: 2. Response time: 25.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:02.049Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:02.049Z - Handled HASH_GLOB. Handling time: 3. Response time: 19.
Time for '@nx/vite/plugin:createNodes' 51.83430000000226
Time for 'total for sendMessageToDaemon()' 24.166400000001886
Time for 'total for sendMessageToDaemon()' 28.053100000004633
Time for 'deserialize daemon response' 0.03800000000046566
Time for 'deserialize daemon response' 0.06620000000111759
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:02.058Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:02.058Z - Handled HASH_GLOB. Handling time: 3. Response time: 19.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:02.058Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:02.059Z - Handled HASH_GLOB. Handling time: 4. Response time: 13.
Time for '@nx/eslint/plugin:createNodes' 185.44920000000275
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:02.218Z - Time taken for 'build-project-configs' 200.38289999999688ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:02.219Z - Time taken for '@nx/js/typescript:createDependencies' 19.468499999988126ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:02.225Z - Time taken for '@nx/vite/plugin:createDependencies' 21.13060000000405ms
Time for 'build typescript dependencies' 0.04969999998866115
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:02.323Z - Wrote project graph cache to C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\.nx\workspace-data\project-graph.json
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:02.324Z - [SYNC]: collect registered sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:02.325Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:02.326Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:02.326Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:02.326Z - Time taken for 'total execution time for createProjectGraph()' 91.38439999999537ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:02.729Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:02.736Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:02.737Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:02.738Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:02.740Z - Time taken for 'total for creating and serializing project graph' 1.743000000002212ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:02.765Z - [SYNC]: @nx/js:typescript-sync changes: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:02.765Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 34.77029999998922ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:02.765Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:02.766Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 28.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:03.437Z - [WATCHER]: Processing file changes in outputs
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:03.438Z - Closed a connection. Number of open connections: 5
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:03.438Z - Closed a connection. Number of open connections: 4
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:03.438Z - Closed a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:03.438Z - Closed a connection. Number of open connections: 2
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:03.439Z - Closed a connection. Number of open connections: 1
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:03.439Z - Closed a connection. Number of open connections: 0
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:03.439Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo (sources)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:03.441Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo (outputs)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:03.448Z - Server stopped because: "LOCK_FILES_CHANGED"
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:12.988Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\dc5fb78e19b1f076be7c\d.sock
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:12.994Z - [WATCHER]: Subscribed to changes within: C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo (native)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:13.003Z - Established a connection. Number of open connections: 1
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:13.006Z - Closed a connection. Number of open connections: 0
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:13.007Z - Established a connection. Number of open connections: 1
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:13.011Z - [REQUEST]: Client Request for Project Graph Received
Time for 'plugin worker 18888 code loading' 23.1554
Time for 'plugin worker 18004 code loading' 19.004400000000004
Time for 'plugin worker 18384 code loading' 27.116500000000002
Time for 'plugin worker 16900 code loading' 20.68289999999999
Time for 'plugin worker 16232 code loading' 18.655200000000008
Time for 'plugin worker 11228 code loading' 21.341499999999996
Time for 'plugin worker 9140 code loading' 37.83930000000001
Time for 'plugin worker 7416 code loading' 41.14280000000001
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:14.414Z - Time taken for 'Load Nx Plugin: C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\node_modules\nx\src\plugins\project-json\build-nodes\project-json' 1383.328ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:14.511Z - Time taken for 'Load Nx Plugin: C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\node_modules\nx\src\plugins\package-json' 1482.6202000000003ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:14.661Z - Time taken for 'loadDefaultNxPlugins' 1634.1113ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:14.984Z - Time taken for 'Load Nx Plugin: @nx/eslint/plugin' 1964.6896000000002ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.008Z - Time taken for 'Load Nx Plugin: @nx/js/typescript' 1994.5387999999998ms
Time for 'plugin worker 12644 code loading' 16.860799999999998
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.102Z - Time taken for 'Load Nx Plugin: @nx/jest/plugin' 2076.5791999999997ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.211Z - Time taken for 'Load Nx Plugin: @nx/react/router-plugin' 2193.615ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.218Z - Time taken for 'Load Nx Plugin: @nx/playwright/plugin' 2194.7057999999997ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.267Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.267Z - [REQUEST]: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.267Z - [REQUEST]: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.273Z - No files changed, but no in-memory cached project graph found. Recomputing it...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.300Z - Time taken for 'loadSpecifiedNxPlugins' 2252.7160999999996ms
Time for 'nx/core/project-json:createNodes' 0.6536000000000968
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.304Z - Established a connection. Number of open connections: 2
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.305Z - Established a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.306Z - Closed a connection. Number of open connections: 2
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.307Z - Established a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.319Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.320Z - Established a connection. Number of open connections: 4
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.321Z - Closed a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.322Z - Established a connection. Number of open connections: 4
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.323Z - Established a connection. Number of open connections: 5
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.324Z - Established a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.324Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.325Z - Handled HASH_GLOB. Handling time: 0. Response time: 6.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.325Z - Established a connection. Number of open connections: 7
Time for '@nx/react/router-plugin:createNodes' 42.15570000000025
Time for 'total for sendMessageToDaemon()' 18.604100000000017
Time for 'deserialize daemon response' 0.07139999999981228
Time for 'nx/core/package-json:createNodes' 27.854499999999916
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.330Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.330Z - Closed a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.331Z - Closed a connection. Number of open connections: 5
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.332Z - Established a connection. Number of open connections: 6
Time for 'total for sendMessageToDaemon()' 24.45739999999978
Time for 'deserialize daemon response' 0.08649999999988722
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.337Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.342Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.342Z - Established a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.344Z - Done responding to the client handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.344Z - Handled GLOB. Handling time: 2. Response time: 14.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.344Z - Closed a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.353Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.354Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.354Z - Handled HASH_GLOB. Handling time: 2. Response time: 17.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.354Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.354Z - Handled HASH_GLOB. Handling time: 2. Response time: 12.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.360Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.361Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.362Z - Handled HASH_GLOB. Handling time: 4. Response time: 9.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.362Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:15.362Z - Handled HASH_GLOB. Handling time: 2. Response time: 2.
Time for '@nx/jest/plugin:createNodes' 72.90859999999975
Time for 'total for sendMessageToDaemon()' 33.933300000000145
Time for 'deserialize daemon response' 0.09389999999984866
Time for 'total for sendMessageToDaemon()' 23.995100000000093
Time for 'deserialize daemon response' 0.13879999999971915
Time for 'nx/js/dependencies-and-lockfile:createNodes' 77.50329999999985
Time for '@nx/js/typescript:createNodes' 703.4121
Time for '@nx/vite/plugin:createNodes' 825.942
Time for 'total for sendMessageToDaemon()' 20.636399999999867
Time for 'deserialize daemon response' 0.06039999999984502
Time for 'total for sendMessageToDaemon()' 25.449099999999817
Time for 'deserialize daemon response' 0.06539999999995416
(node:16900) [DEP0180] DeprecationWarning: fs.Stats constructor is deprecated.
(Use `node --trace-deprecation ...` to show where the warning was created)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:18.755Z - [REQUEST]: Responding to the client. handleNxWorkspaceFiles
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:18.756Z - Done responding to the client handleNxWorkspaceFiles
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:18.756Z - Handled GET_FILES_IN_DIRECTORY. Handling time: 1. Response time: 1.
Time for '@nx/playwright/plugin:createNodes' 3474.3738
Time for 'total for sendMessageToDaemon()' 8.717900000000554
Time for 'deserialize daemon response' 0.03440000000045984
Time for '@nx/eslint/plugin:createNodes' 4397.2885
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:19.755Z - Time taken for 'build-project-configs' 4427.2537ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:19.756Z - Time taken for '@nx/js/typescript:createDependencies' 19.098899999999958ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:19.757Z - Time taken for '@nx/vite/plugin:createDependencies' 15.46180000000004ms
Time for 'build typescript dependencies' 0.5159000000003289
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:19.937Z - Wrote project graph cache to C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\.nx\workspace-data\project-graph.json
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:19.938Z - [SYNC]: collect registered sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:19.941Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:19.941Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:19.943Z - Time taken for 'total for creating and serializing project graph' 6929.7342ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:19.947Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:19.947Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 6930. Response time: 6.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:20.047Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:20.822Z - Established a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:20.822Z - Established a connection. Number of open connections: 8
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:20.832Z - Closed a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:20.837Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:20.838Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:20.838Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:20.839Z - Time taken for 'total for creating and serializing project graph' 1.1067999999995664ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:20.863Z - [SYNC]: @nx/js:typescript-sync changes: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:20.863Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 814.2134000000005ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:20.864Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:20.864Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 26.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:21.139Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:21.140Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:21.140Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:21.141Z - Time taken for 'total for creating and serializing project graph' 1.240299999999479ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:21.143Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:21.143Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 3.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:21.490Z - [REQUEST]: Responding to the client. handleMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:21.491Z - Done responding to the client handleMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:21.491Z - Handled MULTI_GLOB. Handling time: 1. Response time: 1.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:21.560Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:21.561Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:21.561Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:21.563Z - Time taken for 'total for creating and serializing project graph' 1.3656000000009954ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:21.564Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:21.565Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 4.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:21.623Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:21.624Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:21.624Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:21.626Z - Time taken for 'total for creating and serializing project graph' 1.0574999999989814ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:21.627Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:21.627Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 3.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:21.712Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:21.713Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:21.714Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:21.716Z - Time taken for 'total for creating and serializing project graph' 1.3052999999999884ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:21.717Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:21.717Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 3.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:23.100Z - [WATCHER]: 0 file(s) created or restored, 20 file(s) modified, 0 file(s) deleted
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:23.112Z - [WATCHER]: Processing file changes in outputs
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:23.115Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo (sources)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:23.117Z - Closed a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:23.118Z - Closed a connection. Number of open connections: 5
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:23.118Z - Closed a connection. Number of open connections: 4
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:23.118Z - Closed a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:23.118Z - Closed a connection. Number of open connections: 2
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:23.119Z - Closed a connection. Number of open connections: 1
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:23.119Z - Closed a connection. Number of open connections: 0
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:23.120Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo (outputs)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:07:23.137Z - Server stopped because: "Stopping the daemon the set of ignored files changed (native)"
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:18.434Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\dc5fb78e19b1f076be7c\d.sock
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:18.446Z - [WATCHER]: Subscribed to changes within: C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo (native)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:18.452Z - Established a connection. Number of open connections: 1
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:18.458Z - Closed a connection. Number of open connections: 0
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:18.459Z - Established a connection. Number of open connections: 1
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:18.464Z - [REQUEST]: Client Request for Project Graph Received
Time for 'plugin worker 2512 code loading' 32.0359
Time for 'plugin worker 13628 code loading' 25.258200000000002
Time for 'plugin worker 15688 code loading' 19.511700000000005
Time for 'plugin worker 14272 code loading' 25.400199999999998
Time for 'plugin worker 13208 code loading' 23.872400000000013
Time for 'plugin worker 15252 code loading' 22.0039
Time for 'plugin worker 13644 code loading' 22.737099999999998
Time for 'plugin worker 14212 code loading' 32.40899999999999
Time for 'plugin worker 9156 code loading' 103.9013
Time for 'plugin worker 16296 code loading' 47.837199999999996
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:20.425Z - Time taken for 'Load Nx Plugin: C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\node_modules\nx\src\plugins\package-json' 1932.2612ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:20.574Z - Time taken for 'Load Nx Plugin: C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\node_modules\nx\src\plugins\project-json\build-nodes\project-json' 2081.6519ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:20.771Z - Time taken for 'loadDefaultNxPlugins' 2278.3715ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.188Z - Time taken for 'Load Nx Plugin: @nx/eslint/plugin' 2715.8458ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.216Z - Time taken for 'Load Nx Plugin: @nx/jest/plugin' 2733.3315ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.294Z - Time taken for 'Load Nx Plugin: @nx/js/typescript' 2826.9048ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.539Z - Time taken for 'Load Nx Plugin: @nx/react/router-plugin' 3069.0022ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.541Z - Time taken for 'Load Nx Plugin: @nx/playwright/plugin' 3063.2039ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.574Z - Time taken for 'Load Nx Plugin: @nx/vite/plugin' 3099.4945ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.580Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.580Z - [REQUEST]: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.580Z - [REQUEST]: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.598Z - No files changed, but no in-memory cached project graph found. Recomputing it...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.641Z - Time taken for 'loadSpecifiedNxPlugins' 3110.8719ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.643Z - Established a connection. Number of open connections: 2
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.643Z - Established a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.644Z - Established a connection. Number of open connections: 4
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.645Z - Established a connection. Number of open connections: 5
Time for 'nx/core/project-json:createNodes' 0.7547999999997046
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.647Z - Closed a connection. Number of open connections: 4
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.648Z - Closed a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.663Z - [REQUEST]: Responding to the client. handleHashMultiGlob
Time for '@nx/react/router-plugin:createNodes' 58.215000000000146
Time for 'total for sendMessageToDaemon()' 32.17290000000003
Time for 'deserialize daemon response' 0.09919999999965512
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.676Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.679Z - Established a connection. Number of open connections: 4
Time for 'nx/core/package-json:createNodes' 37.24300000000039
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.681Z - Established a connection. Number of open connections: 5
Time for 'total for sendMessageToDaemon()' 35.009799999999814
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.682Z - Established a connection. Number of open connections: 6
Time for 'deserialize daemon response' 0.0909999999998945
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.683Z - Established a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.684Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.684Z - Handled HASH_GLOB. Handling time: 2. Response time: 21.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.685Z - Done responding to the client handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.685Z - Handled GLOB. Handling time: 7. Response time: 9.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.686Z - Closed a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.690Z - Closed a connection. Number of open connections: 5
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.695Z - Established a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.708Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.714Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.723Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.723Z - Established a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.725Z - Closed a connection. Number of open connections: 6
Time for '@nx/expo/plugin:createNodes' 88.3453999999997
Time for 'total for sendMessageToDaemon()' 57.67039999999997
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.727Z - Established a connection. Number of open connections: 7
Time for 'deserialize daemon response' 0.10030000000006112
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.727Z - Established a connection. Number of open connections: 8
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.728Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.728Z - Handled HASH_GLOB. Handling time: 7. Response time: 20.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.728Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.728Z - Handled HASH_GLOB. Handling time: 2. Response time: 14.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.728Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.729Z - Handled HASH_GLOB. Handling time: 5. Response time: 6.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.741Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.743Z - Closed a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.751Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.752Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.752Z - Handled HASH_GLOB. Handling time: 3. Response time: 11.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.753Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:21.754Z - Handled HASH_GLOB. Handling time: 1. Response time: 3.
Time for 'total for sendMessageToDaemon()' 61.164800000000014
Time for 'deserialize daemon response' 0.1032999999997628
Time for 'nx/js/dependencies-and-lockfile:createNodes' 117.80510000000004
Time for '@nx/js/typescript:createNodes' 1020.9849999999997
Time for '@nx/vite/plugin:createNodes' 1148.0898000000002
Time for 'total for sendMessageToDaemon()' 28.240099999999984
Time for 'deserialize daemon response' 0.05420000000003711
Time for '@nx/jest/plugin:createNodes' 2312.1795999999995
Time for 'total for sendMessageToDaemon()' 54.52419999999984
Time for 'deserialize daemon response' 0.07909999999992579
(node:15252) [DEP0180] DeprecationWarning: fs.Stats constructor is deprecated.
(Use `node --trace-deprecation ...` to show where the warning was created)
Time for 'total for sendMessageToDaemon()' 49.06010000000015
Time for 'deserialize daemon response' 0.08899999999994179
(node:13208) [DEP0180] DeprecationWarning: fs.Stats constructor is deprecated.
(Use `node --trace-deprecation ...` to show where the warning was created)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:26.219Z - [REQUEST]: Responding to the client. handleNxWorkspaceFiles
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:26.220Z - Done responding to the client handleNxWorkspaceFiles
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:26.220Z - Handled GET_FILES_IN_DIRECTORY. Handling time: 1. Response time: 1.
Time for '@nx/playwright/plugin:createNodes' 4606.3619
Time for 'total for sendMessageToDaemon()' 11.242500000000291
Time for 'deserialize daemon response' 0.04650000000037835
Time for '@nx/eslint/plugin:createNodes' 5771.2461
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:27.479Z - Time taken for 'build-project-configs' 5822.1801000000005ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:27.481Z - Time taken for '@nx/js/typescript:createDependencies' 20.723299999999654ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:27.483Z - Time taken for '@nx/vite/plugin:createDependencies' 17.425499999999374ms
Time for 'build typescript dependencies' 131.46739999999954
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:27.928Z - Wrote project graph cache to C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\.nx\workspace-data\project-graph.json
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:27.929Z - [SYNC]: collect registered sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:27.934Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:27.934Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:27.939Z - Time taken for 'total for creating and serializing project graph' 9470.215199999999ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:27.947Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:27.947Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 9471. Response time: 13.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:28.041Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:29.005Z - Established a connection. Number of open connections: 8
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:29.006Z - Established a connection. Number of open connections: 9
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:29.006Z - Closed a connection. Number of open connections: 8
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:29.018Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:29.022Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:29.022Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:29.024Z - Time taken for 'total for creating and serializing project graph' 3.898699999999735ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:29.072Z - [SYNC]: @nx/js:typescript-sync changes: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:29.072Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 1030.0190999999995ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:29.073Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:29.073Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 4. Response time: 51.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:29.751Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:29.753Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:29.753Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:29.755Z - Time taken for 'total for creating and serializing project graph' 1.5282999999999447ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:29.757Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:29.757Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 4.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:30.336Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:30.339Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:30.339Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:30.342Z - Time taken for 'total for creating and serializing project graph' 3.510499999998501ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:30.346Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:30.346Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 4. Response time: 7.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:30.476Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:30.478Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:30.478Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:30.480Z - Time taken for 'total for creating and serializing project graph' 1.758800000001429ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:30.483Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:30.483Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 5.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:30.634Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:30.637Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:30.637Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:30.640Z - Time taken for 'total for creating and serializing project graph' 2.919100000000981ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:30.643Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:30.643Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 3. Response time: 6.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:32.072Z - [WATCHER]: 0 file(s) created or restored, 20 file(s) modified, 0 file(s) deleted
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:32.076Z - [WATCHER]: Processing file changes in outputs
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:32.077Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo (sources)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:32.078Z - Closed a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:32.078Z - Closed a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:32.078Z - Closed a connection. Number of open connections: 5
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:32.078Z - Closed a connection. Number of open connections: 4
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:32.078Z - Closed a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:32.079Z - Closed a connection. Number of open connections: 2
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:32.079Z - Closed a connection. Number of open connections: 1
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:32.079Z - Closed a connection. Number of open connections: 0
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:32.079Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo (outputs)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:32.085Z - Server stopped because: "Stopping the daemon the set of ignored files changed (native)"
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:38.693Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\dc5fb78e19b1f076be7c\d.sock
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:38.700Z - [WATCHER]: Subscribed to changes within: C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo (native)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:38.705Z - Established a connection. Number of open connections: 1
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:38.707Z - Established a connection. Number of open connections: 2
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:38.710Z - Closed a connection. Number of open connections: 1
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:38.715Z - [REQUEST]: Client Request for Project Graph Received
Time for 'plugin worker 14064 code loading' 23.4827
Time for 'plugin worker 17272 code loading' 28.55210000000001
Time for 'plugin worker 13864 code loading' 21.7243
Time for 'plugin worker 2092 code loading' 23.769599999999997
Time for 'plugin worker 4788 code loading' 17.366
Time for 'plugin worker 13360 code loading' 18.0848
Time for 'plugin worker 10952 code loading' 19.292699999999996
Time for 'plugin worker 14124 code loading' 18.432599999999994
Time for 'plugin worker 13652 code loading' 19.680999999999997
Time for 'plugin worker 13324 code loading' 20.45779999999999
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:40.483Z - Time taken for 'Load Nx Plugin: C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\node_modules\nx\src\plugins\package-json' 1735.7678ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:40.663Z - Time taken for 'Load Nx Plugin: C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\node_modules\nx\src\plugins\project-json\build-nodes\project-json' 1923.2791000000002ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:40.877Z - Time taken for 'loadDefaultNxPlugins' 2139.7983ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.134Z - Time taken for 'Load Nx Plugin: @nx/eslint/plugin' 2409.92ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.279Z - Time taken for 'Load Nx Plugin: @nx/jest/plugin' 2546.7225000000003ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.281Z - Time taken for 'Load Nx Plugin: @nx/js/typescript' 2562.8663ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.454Z - Time taken for 'Load Nx Plugin: @nx/playwright/plugin' 2725.0376ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.634Z - Time taken for 'Load Nx Plugin: @nx/react/router-plugin' 2912.3786999999998ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.639Z - Time taken for 'Load Nx Plugin: @nx/vite/plugin' 2912.5401ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.673Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.673Z - [REQUEST]: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.673Z - [REQUEST]: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.682Z - No files changed, but no in-memory cached project graph found. Recomputing it...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.720Z - Time taken for 'loadSpecifiedNxPlugins' 2954.535ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.722Z - Established a connection. Number of open connections: 2
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.723Z - Established a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.724Z - Established a connection. Number of open connections: 4
Time for 'nx/core/project-json:createNodes' 0.6035999999999149
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.725Z - Established a connection. Number of open connections: 5
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.730Z - Closed a connection. Number of open connections: 4
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.731Z - Closed a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.734Z - Established a connection. Number of open connections: 4
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.740Z - [REQUEST]: Responding to the client. handleHashMultiGlob
Time for '@nx/react/router-plugin:createNodes' 50.80699999999979
Time for 'total for sendMessageToDaemon()' 31.966300000000047
Time for 'deserialize daemon response' 0.07979999999997744
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.751Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.752Z - Established a connection. Number of open connections: 5
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.752Z - Established a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.753Z - Closed a connection. Number of open connections: 5
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.754Z - Closed a connection. Number of open connections: 4
Time for 'nx/core/package-json:createNodes' 33.919900000000325
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.755Z - Established a connection. Number of open connections: 5
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.755Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.756Z - Handled HASH_GLOB. Handling time: 1. Response time: 16.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.756Z - Established a connection. Number of open connections: 6
Time for 'total for sendMessageToDaemon()' 32.50680000000011
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.757Z - Done responding to the client handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.757Z - Handled GLOB. Handling time: 4. Response time: 6.
Time for 'deserialize daemon response' 0.08809999999994034
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.758Z - Established a connection. Number of open connections: 7
Time for 'nx/js/dependencies-and-lockfile:createNodes' 31.36140000000023
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.763Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.764Z - Established a connection. Number of open connections: 8
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.766Z - Closed a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.766Z - Closed a connection. Number of open connections: 6
Time for '@nx/playwright/plugin:createNodes' 60.542399999999816
Time for 'total for sendMessageToDaemon()' 37.47890000000007
Time for 'deserialize daemon response' 0.10840000000007421
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.773Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.781Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.782Z - Established a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.786Z - [REQUEST]: Responding to the client. handleHashGlob
Time for 'total for sendMessageToDaemon()' 40.33460000000014
Time for 'deserialize daemon response' 0.059099999999943975
Time for '@nx/vite/plugin:createNodes' 85.28809999999976
Time for 'total for sendMessageToDaemon()' 37.00570000000016
Time for 'deserialize daemon response' 0.07999999999992724
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.799Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.799Z - Handled HASH_GLOB. Handling time: 2. Response time: 36.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.800Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.800Z - Handled HASH_GLOB. Handling time: 3. Response time: 27.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.801Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.801Z - Handled HASH_GLOB. Handling time: 4. Response time: 21.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.806Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.807Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.807Z - Handled HASH_GLOB. Handling time: 1. Response time: 21.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.811Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.814Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.814Z - Handled HASH_GLOB. Handling time: 3. Response time: 8.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.814Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:41.814Z - Handled HASH_GLOB. Handling time: 1. Response time: 4.
Time for '@nx/expo/plugin:createNodes' 103.21309999999994
Time for 'total for sendMessageToDaemon()' 22.19569999999976
Time for 'deserialize daemon response' 0.0340000000001055
Time for '@nx/js/typescript:createNodes' 715.6223
Time for 'total for sendMessageToDaemon()' 25.921200000000226
Time for 'deserialize daemon response' 0.06390000000010332
Time for '@nx/jest/plugin:createNodes' 1382.3914
Time for 'total for sendMessageToDaemon()' 49.65930000000026
Time for 'deserialize daemon response' 0.06950000000006185
(node:10952) [DEP0180] DeprecationWarning: fs.Stats constructor is deprecated.
(Use `node --trace-deprecation ...` to show where the warning was created)
Time for '@nx/eslint/plugin:createNodes' 3275.0708999999997
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:45.061Z - Time taken for 'build-project-configs' 3318.6841000000004ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:45.063Z - Time taken for '@nx/js/typescript:createDependencies' 17.875100000000202ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:45.065Z - Time taken for '@nx/vite/plugin:createDependencies' 14.63340000000062ms
Time for 'build typescript dependencies' 33.202399999999216
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:45.173Z - Wrote project graph cache to C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\.nx\workspace-data\project-graph.json
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:45.174Z - [SYNC]: collect registered sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:45.176Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:45.176Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:45.177Z - Time taken for 'total for creating and serializing project graph' 6461.4701ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:45.182Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:45.182Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 6462. Response time: 6.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:45.282Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:46.003Z - Established a connection. Number of open connections: 8
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:46.003Z - Established a connection. Number of open connections: 9
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:46.003Z - Closed a connection. Number of open connections: 8
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:46.006Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:46.007Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:46.007Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:46.009Z - Time taken for 'total for creating and serializing project graph' 1.0843999999997322ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:46.044Z - [SYNC]: @nx/js:typescript-sync changes: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:46.044Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 761.5578999999998ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:46.045Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:46.045Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 38.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.252Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.254Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.254Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.256Z - Time taken for 'total for creating and serializing project graph' 1.5970999999990454ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.259Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.259Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 5.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.312Z - [REQUEST]: Responding to the client. handleMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.314Z - Done responding to the client handleMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.315Z - Handled MULTI_GLOB. Handling time: 2. Response time: 3.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.392Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.393Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.393Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.395Z - Time taken for 'total for creating and serializing project graph' 1.2746999999999389ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.397Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.397Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 4.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.464Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.465Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.465Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.467Z - Time taken for 'total for creating and serializing project graph' 1.7440999999998894ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.468Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.468Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 3.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.521Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.522Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.522Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.523Z - Time taken for 'total for creating and serializing project graph' 0.9951000000000931ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.525Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.525Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 3.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.604Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.605Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.606Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.608Z - Time taken for 'total for creating and serializing project graph' 1.228900000000067ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.609Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.609Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 3.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.640Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.641Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.641Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.643Z - Time taken for 'total for creating and serializing project graph' 1.0270999999993364ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.645Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:48.645Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 4.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:49.602Z - [WATCHER]: 0 file(s) created or restored, 21 file(s) modified, 0 file(s) deleted
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:49.606Z - [WATCHER]: Processing file changes in outputs
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:49.844Z - Closed a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:49.844Z - Closed a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:49.845Z - Closed a connection. Number of open connections: 5
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:49.845Z - Closed a connection. Number of open connections: 4
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:49.845Z - Closed a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:49.845Z - Closed a connection. Number of open connections: 2
Time for 'plugin worker 8404 code loading' 29.5188
Time for 'plugin worker 15628 code loading' 30.143299999999996
Time for 'plugin worker 19080 code loading' 44.62819999999999
Time for 'plugin worker 19336 code loading' 56.44860000000001
Time for 'plugin worker 14976 code loading' 57.53060000000001
Time for 'plugin worker 17500 code loading' 56.28309999999999
Time for 'plugin worker 17168 code loading' 68.44109999999998
Time for 'plugin worker 14952 code loading' 38.23270000000002
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.231Z - Time taken for 'Load Nx Plugin: @nx/js/typescript' 2514.4411999999993ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.270Z - Time taken for 'Load Nx Plugin: @nx/eslint/plugin' 2548.8138000000017ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.297Z - Time taken for 'Load Nx Plugin: @nx/jest/plugin' 2564.9907000000003ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.546Z - Time taken for 'Load Nx Plugin: @nx/react/router-plugin' 2826.5018ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.687Z - Time taken for 'Load Nx Plugin: @nx/vite/plugin' 2965.4015999999992ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.736Z - Time taken for 'Load Nx Plugin: @nx/playwright/plugin' 3008.0944ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.739Z - Time taken for 'Load Nx Plugin: @nx/expo/plugin' 3006.1963000000014ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.802Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.802Z - [REQUEST]: apps/backend-e2e/eslint.config.mjs,apps/backend-e2e/package.json,apps/backend/tsconfig.json,apps/backend/tsconfig.spec.json,apps/backend-e2e/src/support/test-setup.ts,apps/backend/src/main.ts,apps/backend/package.json,apps/backend-e2e/src/support/global-setup.ts,apps/backend/webpack.config.js,apps/backend/eslint.config.mjs,apps/backend-e2e/src/support/global-teardown.ts,package.json,apps/backend-e2e/jest.config.ts,apps/backend/src/assets/.gitkeep,apps/backend-e2e/src/backend/backend.spec.ts,apps/backend/tsconfig.app.json,apps/backend/jest.config.ts,apps/backend-e2e/.spec.swcrc,tsconfig.json,nx.json,apps/backend-e2e/tsconfig.json
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.803Z - [REQUEST]: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.844Z - Time taken for 'loadSpecifiedNxPlugins' 3080.345000000001ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.846Z - Established a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.847Z - Established a connection. Number of open connections: 4
Time for 'nx/core/project-json:createNodes' 0.7580999999991036
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.849Z - Established a connection. Number of open connections: 5
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.850Z - Established a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.852Z - Closed a connection. Number of open connections: 5
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.852Z - Closed a connection. Number of open connections: 4
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.852Z - Closed a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.854Z - Established a connection. Number of open connections: 4
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.859Z - [REQUEST]: Responding to the client. handleHashMultiGlob
Time for '@nx/react/router-plugin:createNodes' 49.19950000000017
Time for 'total for sendMessageToDaemon()' 24.51949999999988
Time for 'deserialize daemon response' 0.08910000000014406
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.872Z - [REQUEST]: Responding to the client. handleGlob
Time for 'nx/core/package-json:createNodes' 24.284600000000864
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.873Z - Established a connection. Number of open connections: 5
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.873Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.873Z - Handled HASH_GLOB. Handling time: 1. Response time: 14.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.874Z - Established a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.874Z - Closed a connection. Number of open connections: 5
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.876Z - Established a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.877Z - Established a connection. Number of open connections: 7
Time for 'total for sendMessageToDaemon()' 29.118999999999687
Time for 'deserialize daemon response' 0.09789999999975407
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.884Z - Done responding to the client handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.884Z - Handled GLOB. Handling time: 6. Response time: 12.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.884Z - Established a connection. Number of open connections: 8
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.892Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.892Z - Established a connection. Number of open connections: 9
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.893Z - Closed a connection. Number of open connections: 8
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.894Z - Closed a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.900Z - [REQUEST]: Responding to the client. handleHashGlob
Time for 'nx/js/dependencies-and-lockfile:createNodes' 45.24990000000071
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.900Z - Established a connection. Number of open connections: 8
Time for 'total for sendMessageToDaemon()' 33.60330000000022
Time for 'deserialize daemon response' 0.08210000000008222
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.911Z - [REQUEST]: Responding to the client. handleHashMultiGlob
Time for '@nx/vite/plugin:createNodes' 86.72200000000021
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.918Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.918Z - Established a connection. Number of open connections: 9
Time for 'total for sendMessageToDaemon()' 32.68730000000005
Time for 'deserialize daemon response' 0.08869999999978972
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.919Z - Established a connection. Number of open connections: 10
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.919Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.920Z - Handled HASH_GLOB. Handling time: 4. Response time: 27.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.920Z - Closed a connection. Number of open connections: 9
Time for '@nx/playwright/plugin:createNodes' 92.51510000000007
Time for 'total for sendMessageToDaemon()' 72.65610000000015
Time for 'deserialize daemon response' 0.08760000000029322
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.932Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.932Z - Handled HASH_GLOB. Handling time: 2. Response time: 32.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.939Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.943Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.943Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.943Z - Handled HASH_GLOB. Handling time: 7. Response time: 32.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.944Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.944Z - Handled HASH_GLOB. Handling time: 3. Response time: 26.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.950Z - [REQUEST]: Responding to the client. handleHashGlob
Time for '@nx/expo/plugin:createNodes' 106.1594
Time for 'total for sendMessageToDaemon()' 40.64800000000014
Time for 'deserialize daemon response' 0.04640000000017608
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.953Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.953Z - Handled HASH_GLOB. Handling time: 4. Response time: 14.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.954Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.954Z - Handled HASH_GLOB. Handling time: 1. Response time: 11.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.954Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:52.955Z - Handled HASH_GLOB. Handling time: 3. Response time: 5.
Time for '@nx/js/typescript:createNodes' 934.3125
Time for 'total for sendMessageToDaemon()' 33.05439999999999
Time for 'deserialize daemon response' 0.041600000000016735
Time for '@nx/jest/plugin:createNodes' 1815.3788999999997
Time for 'total for sendMessageToDaemon()' 58.635899999999765
Time for 'deserialize daemon response' 0.06550000000015643
(node:14976) [DEP0180] DeprecationWarning: fs.Stats constructor is deprecated.
(Use `node --trace-deprecation ...` to show where the warning was created)
Time for '@nx/webpack/plugin:createNodes' 2497.1507
Time for 'total for sendMessageToDaemon()' 59.92210000000023
Time for 'deserialize daemon response' 0.09600000000000364
Time for '@nx/eslint/plugin:createNodes' 4577.620199999999
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:57.442Z - Time taken for 'build-project-configs' 4608.691699999999ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:57.450Z - Time taken for '@nx/js/typescript:createDependencies' 22.18579999999929ms
Time for 'build typescript dependencies' 65.10699999999997
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:57.622Z - Wrote project graph cache to C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\.nx\workspace-data\project-graph.json
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:57.622Z - [SYNC]: collect registered sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:57.624Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:57.624Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:57.625Z - Time taken for 'total execution time for createProjectGraph()' 160.0627999999997ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:57.832Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:57.839Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:57.841Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:57.841Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:57.843Z - Time taken for 'total for creating and serializing project graph' 1.3467000000018743ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:57.878Z - [SYNC]: @nx/js:typescript-sync changes: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:57.878Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 44.94550000000163ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:57.878Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:09:57.878Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 37.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:10:52.531Z - [WATCHER]: Processing file changes in outputs
[NX v21.3.9 Daemon Server] - 2025-07-30T12:10:52.532Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo (sources)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:10:52.532Z - Closed a connection. Number of open connections: 8
[NX v21.3.9 Daemon Server] - 2025-07-30T12:10:52.533Z - Closed a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T12:10:52.533Z - Closed a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T12:10:52.533Z - Closed a connection. Number of open connections: 5
[NX v21.3.9 Daemon Server] - 2025-07-30T12:10:52.533Z - Closed a connection. Number of open connections: 4
[NX v21.3.9 Daemon Server] - 2025-07-30T12:10:52.533Z - Closed a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T12:10:52.533Z - Closed a connection. Number of open connections: 2
[NX v21.3.9 Daemon Server] - 2025-07-30T12:10:52.533Z - Closed a connection. Number of open connections: 1
[NX v21.3.9 Daemon Server] - 2025-07-30T12:10:52.533Z - Closed a connection. Number of open connections: 0
[NX v21.3.9 Daemon Server] - 2025-07-30T12:10:52.534Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo (outputs)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:10:52.536Z - Server stopped because: "LOCK_FILES_CHANGED"
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:01.560Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\dc5fb78e19b1f076be7c\d.sock
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:01.568Z - [WATCHER]: Subscribed to changes within: C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo (native)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:01.572Z - Established a connection. Number of open connections: 1
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:01.573Z - Established a connection. Number of open connections: 2
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:01.576Z - Closed a connection. Number of open connections: 1
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:01.581Z - [REQUEST]: Client Request for Project Graph Received
Time for 'plugin worker 9020 code loading' 27.465100000000007
Time for 'plugin worker 19028 code loading' 30.957700000000003
Time for 'plugin worker 16624 code loading' 37.77799999999999
Time for 'plugin worker 18860 code loading' 22.45920000000001
Time for 'plugin worker 15956 code loading' 24.518699999999995
Time for 'plugin worker 16064 code loading' 29.60860000000001
Time for 'plugin worker 16332 code loading' 29.854600000000005
Time for 'plugin worker 9740 code loading' 19.3759
Time for 'plugin worker 14176 code loading' 19.091499999999996
Time for 'plugin worker 19188 code loading' 57.46430000000001
Time for 'plugin worker 18084 code loading' 130.12529999999998
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:03.145Z - Time taken for 'Load Nx Plugin: C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\node_modules\nx\src\plugins\package-json' 1539.6833000000001ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:03.479Z - Time taken for 'Load Nx Plugin: C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\node_modules\nx\src\plugins\project-json\build-nodes\project-json' 1873.4568ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:03.608Z - Time taken for 'loadDefaultNxPlugins' 2004.5059999999999ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:03.860Z - Time taken for 'Load Nx Plugin: @nx/jest/plugin' 2263.0733999999998ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:03.861Z - Time taken for 'Load Nx Plugin: @nx/eslint/plugin' 2272.1434ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:03.889Z - Time taken for 'Load Nx Plugin: @nx/js/typescript' 2305.6893ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.116Z - Time taken for 'Load Nx Plugin: @nx/playwright/plugin' 2522.4374000000003ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.133Z - Time taken for 'Load Nx Plugin: @nx/vite/plugin' 2541.6646ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.149Z - Time taken for 'Load Nx Plugin: @nx/react/router-plugin' 2562.8171ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.189Z - Time taken for 'Load Nx Plugin: @nx/expo/plugin' 2589.5179ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.191Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.191Z - [REQUEST]: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.191Z - [REQUEST]: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.205Z - No files changed, but no in-memory cached project graph found. Recomputing it...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.247Z - Time taken for 'loadSpecifiedNxPlugins' 2606.6697999999997ms
Time for 'nx/core/project-json:createNodes' 0.5898000000001957
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.260Z - Established a connection. Number of open connections: 2
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.266Z - Established a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.266Z - Established a connection. Number of open connections: 4
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.267Z - Closed a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.268Z - Closed a connection. Number of open connections: 2
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.269Z - Established a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.270Z - Established a connection. Number of open connections: 4
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.275Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.277Z - Established a connection. Number of open connections: 5
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.277Z - Established a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.279Z - Closed a connection. Number of open connections: 5
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.279Z - Closed a connection. Number of open connections: 4
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.280Z - Established a connection. Number of open connections: 5
Time for '@nx/react/router-plugin:createNodes' 63.52459999999974
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.285Z - Established a connection. Number of open connections: 6
Time for 'nx/core/package-json:createNodes' 33.63389999999981
Time for 'total for sendMessageToDaemon()' 39.55189999999993
Time for 'deserialize daemon response' 0.06919999999990978
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.290Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.291Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.291Z - Handled HASH_GLOB. Handling time: 1. Response time: 16.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.292Z - Established a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.292Z - Established a connection. Number of open connections: 8
Time for 'total for sendMessageToDaemon()' 41.822799999999916
Time for 'deserialize daemon response' 0.15839999999980137
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.297Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.298Z - Closed a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.298Z - Closed a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.299Z - Established a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.300Z - Established a connection. Number of open connections: 8
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.304Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.305Z - Done responding to the client handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.306Z - Handled GLOB. Handling time: 2. Response time: 16.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.306Z - Established a connection. Number of open connections: 9
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.311Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.329Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.335Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.337Z - Handled HASH_GLOB. Handling time: 2. Response time: 40.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.338Z - Closed a connection. Number of open connections: 8
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.350Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.351Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.351Z - Handled HASH_GLOB. Handling time: 1. Response time: 47.
Time for 'total for sendMessageToDaemon()' 40.015100000000075
Time for 'nx/js/dependencies-and-lockfile:createNodes' 91.48250000000007
Time for 'deserialize daemon response' 0.09740000000010696
Time for 'total for sendMessageToDaemon()' 67.46450000000004
Time for 'deserialize daemon response' 0.13879999999971915
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.383Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.383Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.383Z - Handled HASH_GLOB. Handling time: 2. Response time: 72.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.383Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.383Z - Handled HASH_GLOB. Handling time: 9. Response time: 54.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.384Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.384Z - Handled HASH_GLOB. Handling time: 4. Response time: 34.
Time for '@nx/jest/plugin:createNodes' 140.4919
Time for 'total for sendMessageToDaemon()' 102.84439999999995
Time for 'deserialize daemon response' 0.07490000000007058
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.402Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.402Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.402Z - Handled HASH_GLOB. Handling time: 28. Response time: 19.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.404Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:04.404Z - Handled HASH_GLOB. Handling time: 3. Response time: 2.
Time for '@nx/expo/plugin:createNodes' 160.91280000000006
Time for 'total for sendMessageToDaemon()' 47.69560000000001
Time for 'deserialize daemon response' 0.04739999999992506
Time for '@nx/js/typescript:createNodes' 833.8253
Time for '@nx/vite/plugin:createNodes' 936.3959
Time for 'total for sendMessageToDaemon()' 34.9985999999999
Time for 'deserialize daemon response' 0.15429999999969368
Time for '@nx/webpack/plugin:createNodes' 2900.2318999999998
Time for 'total for sendMessageToDaemon()' 30.461499999999887
Time for 'deserialize daemon response' 0.08629999999993743
Time for 'total for sendMessageToDaemon()' 53.14339999999993
Time for 'deserialize daemon response' 0.07989999999972497
(node:18860) [DEP0180] DeprecationWarning: fs.Stats constructor is deprecated.
(Use `node --trace-deprecation ...` to show where the warning was created)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:08.163Z - [REQUEST]: Responding to the client. handleNxWorkspaceFiles
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:08.164Z - Done responding to the client handleNxWorkspaceFiles
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:08.164Z - Handled GET_FILES_IN_DIRECTORY. Handling time: 1. Response time: 1.
Time for '@nx/playwright/plugin:createNodes' 3939.7674
Time for 'total for sendMessageToDaemon()' 7.126199999999699
Time for 'deserialize daemon response' 0.052400000000488944
Time for '@nx/eslint/plugin:createNodes' 4810.908600000001
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:09.125Z - Time taken for 'build-project-configs' 4862.681699999999ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:09.129Z - Time taken for '@nx/js/typescript:createDependencies' 18.737200000000485ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:09.132Z - Time taken for '@nx/vite/plugin:createDependencies' 18.394500000000335ms
Time for 'build typescript dependencies' 0.6165999999993801
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:09.365Z - Wrote project graph cache to C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\.nx\workspace-data\project-graph.json
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:09.365Z - [SYNC]: collect registered sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:09.368Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:09.368Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:09.370Z - Time taken for 'total for creating and serializing project graph' 7787.8858ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:09.374Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:09.374Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 7788. Response time: 6.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:09.481Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.068Z - Established a connection. Number of open connections: 9
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.075Z - Established a connection. Number of open connections: 10
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.076Z - Closed a connection. Number of open connections: 9
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.079Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.081Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.081Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.083Z - Time taken for 'total for creating and serializing project graph' 1.8350999999984197ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.120Z - [SYNC]: @nx/js:typescript-sync changes: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.120Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 638.0999999999985ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.120Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.120Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 39.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.387Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.388Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.388Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.390Z - Time taken for 'total for creating and serializing project graph' 1.2775000000001455ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.393Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.393Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 5.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.410Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.411Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.411Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.413Z - Time taken for 'total for creating and serializing project graph' 1.106100000000879ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.415Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.416Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 5.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.478Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.478Z - Done responding to the client handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.478Z - Handled GLOB. Handling time: 2. Response time: 0.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.491Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.492Z - Done responding to the client handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.492Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.506Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.507Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.507Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.509Z - Time taken for 'total for creating and serializing project graph' 1.0434999999997672ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.510Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.510Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 3.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.570Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.571Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.571Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.573Z - Time taken for 'total for creating and serializing project graph' 1.08849999999984ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.574Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.575Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 4.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.729Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.731Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.731Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.733Z - Time taken for 'total for creating and serializing project graph' 1.8191999999999098ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.734Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:10.735Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 4.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:11.319Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:11.320Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:11.320Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:11.322Z - Time taken for 'total for creating and serializing project graph' 1.0516999999999825ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:11.323Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:11.323Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 3.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:11.428Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:11.429Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:11.429Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:11.431Z - Time taken for 'total for creating and serializing project graph' 1.0593000000008033ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:11.433Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:11.433Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 4.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:11.523Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:11.524Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:11.525Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:11.527Z - Time taken for 'total for creating and serializing project graph' 1.4575999999997293ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:11.529Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:11.529Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 4.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:12.615Z - [WATCHER]: Processing file changes in outputs
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:12.619Z - [WATCHER]: 0 file(s) created or restored, 15 file(s) modified, 0 file(s) deleted
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:12.730Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:12.731Z - [REQUEST]: libs/ui/eslint.config.mjs,libs/ui/src/index.ts,libs/ui/jest.config.ts,libs/ui/tsconfig.json,libs/ui/vite.config.ts,libs/ui/.babelrc,libs/ui/src/lib/ui.spec.tsx,libs/ui/src/lib/ui.tsx,libs/ui/package.json,tsconfig.json,libs/ui/README.md,libs/ui/tsconfig.lib.json,nx.json,libs/ui/tsconfig.spec.json,package.json
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:12.731Z - [REQUEST]: 
Time for 'nx/core/project-json:createNodes' 0.7289000000000669
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:12.765Z - [REQUEST]: Responding to the client. handleHashMultiGlob
Time for '@nx/react/router-plugin:createNodes' 30.521099999999933
Time for 'total for sendMessageToDaemon()' 22.1229000000003
Time for 'deserialize daemon response' 0.05529999999998836
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:12.772Z - [REQUEST]: Responding to the client. handleGlob
Time for 'total for sendMessageToDaemon()' 24.172300000000178
Time for 'deserialize daemon response' 0.051500000001396984
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:12.777Z - [REQUEST]: Responding to the client. handleHashGlob
Time for 'nx/core/package-json:createNodes' 20.846299999999246
Time for '@nx/playwright/plugin:createNodes' 31.34230000000025
Time for 'total for sendMessageToDaemon()' 26.05659999999989
Time for 'deserialize daemon response' 0.0352999999995518
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:12.785Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:12.785Z - Time taken for 'hash changed files from watcher' 1.426099999998769ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:12.790Z - [REQUEST]: Responding to the client. handleHashGlob
Time for 'nx/js/dependencies-and-lockfile:createNodes' 29.48149999999987
Time for 'total for sendMessageToDaemon()' 29.66989999999896
Time for 'deserialize daemon response' 0.05229999999937718
Time for '@nx/webpack/plugin:createNodes' 40.01959999999963
Time for 'total for sendMessageToDaemon()' 29.93249999999898
Time for 'deserialize daemon response' 0.05169999999998254
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:12.797Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:12.798Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:12.798Z - Handled HASH_GLOB. Handling time: 3. Response time: 33.
Time for 'total for sendMessageToDaemon()' 35.85859999999957
Time for 'deserialize daemon response' 0.03919999999925494
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:12.804Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:12.805Z - Done responding to the client handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:12.805Z - Handled GLOB. Handling time: 1. Response time: 33.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:12.815Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:12.816Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:12.816Z - Handled HASH_GLOB. Handling time: 1. Response time: 39.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:12.816Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:12.817Z - Handled HASH_GLOB. Handling time: 2. Response time: 32.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:12.817Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:12.817Z - Handled HASH_GLOB. Handling time: 2. Response time: 27.
Time for 'total for sendMessageToDaemon()' 41.34909999999945
Time for 'deserialize daemon response' 0.03250000000116415
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:12.826Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:12.826Z - Handled HASH_GLOB. Handling time: 1. Response time: 29.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:12.833Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:12.833Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:12.833Z - Handled HASH_GLOB. Handling time: 3. Response time: 29.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:12.833Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:12.834Z - Handled HASH_GLOB. Handling time: 8. Response time: 19.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:12.834Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:12.835Z - Handled HASH_GLOB. Handling time: 2. Response time: 2.
Time for '@nx/expo/plugin:createNodes' 86.13070000000153
Time for 'total for sendMessageToDaemon()' 34.07619999999952
Time for 'deserialize daemon response' 0.04279999999926076
Time for '@nx/js/typescript:createNodes' 164.365600000001
[31mfailed to load config from C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\libs\ui\vite.config.ts[39m
Time for '@nx/vite/plugin:createNodes' 207.35050000000047
Time for '@nx/eslint/plugin:createNodes' 211.27340000000004
Time for '@nx/jest/plugin:createNodes' 1324.0244999999995
Time for 'total for sendMessageToDaemon()' 38.69369999999981
Time for 'deserialize daemon response' 0.0488999999997759
(node:16064) [DEP0180] DeprecationWarning: fs.Stats constructor is deprecated.
(Use `node --trace-deprecation ...` to show where the warning was created)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:14.110Z - Time taken for 'build-project-configs' 1349.964899999999ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:14.112Z - Time taken for '@nx/js/typescript:createDependencies' 16.451199999999517ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:14.116Z - Time taken for '@nx/vite/plugin:createDependencies' 15.569300000001022ms
Time for 'build typescript dependencies' 472.8703999999998
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:14.678Z - Wrote project graph cache to C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\.nx\workspace-data\project-graph.json with errors
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:14.678Z - Time taken for 'total execution time for createProjectGraph()' 550.6787999999997ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:38.981Z - [WATCHER]: Processing file changes in outputs
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:38.988Z - Closed a connection. Number of open connections: 8
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:38.988Z - Closed a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:38.988Z - Closed a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:38.988Z - Closed a connection. Number of open connections: 5
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:38.988Z - Closed a connection. Number of open connections: 4
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:38.988Z - Closed a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:38.988Z - Closed a connection. Number of open connections: 2
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:38.989Z - Closed a connection. Number of open connections: 1
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:38.989Z - Closed a connection. Number of open connections: 0
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:38.989Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo (sources)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:38.990Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo (outputs)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:11:38.992Z - Server stopped because: "LOCK_FILES_CHANGED"
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:00.162Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\dc5fb78e19b1f076be7c\d.sock
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:00.171Z - [WATCHER]: Subscribed to changes within: C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo (native)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:00.176Z - Established a connection. Number of open connections: 1
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:00.178Z - Established a connection. Number of open connections: 2
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:00.182Z - Closed a connection. Number of open connections: 1
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:00.186Z - [REQUEST]: Client Request for Project Graph Received
Time for 'plugin worker 18332 code loading' 22.397400000000005
Time for 'plugin worker 14124 code loading' 21.006
Time for 'plugin worker 18584 code loading' 31.936499999999995
Time for 'plugin worker 19344 code loading' 22.836200000000005
Time for 'plugin worker 15628 code loading' 27.646199999999993
Time for 'plugin worker 13696 code loading' 20.024699999999996
Time for 'plugin worker 10004 code loading' 36.44319999999999
Time for 'plugin worker 3920 code loading' 26.237399999999994
Time for 'plugin worker 2600 code loading' 17.823000000000008
Time for 'plugin worker 14436 code loading' 66.401
Time for 'plugin worker 5096 code loading' 30.969499999999982
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:01.847Z - Time taken for 'Load Nx Plugin: C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\node_modules\nx\src\plugins\package-json' 1637.2766000000001ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.142Z - Time taken for 'loadDefaultNxPlugins' 1934.3823000000002ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.204Z - Time taken for 'Load Nx Plugin: @nx/eslint/plugin' 2009.0782000000002ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.243Z - Time taken for 'Load Nx Plugin: @nx/js/typescript' 2053.9476ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.342Z - Time taken for 'Load Nx Plugin: @nx/jest/plugin' 2139.8489999999997ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.629Z - Time taken for 'Load Nx Plugin: @nx/react/router-plugin' 2436.9566ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.684Z - Time taken for 'Load Nx Plugin: @nx/webpack/plugin' 2479.3268ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.709Z - Time taken for 'Load Nx Plugin: @nx/playwright/plugin' 2509.8693000000003ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.714Z - Time taken for 'Load Nx Plugin: @nx/vite/plugin' 2517.5498ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.802Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.802Z - [REQUEST]: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.802Z - [REQUEST]: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.811Z - No files changed, but no in-memory cached project graph found. Recomputing it...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.849Z - Time taken for 'loadSpecifiedNxPlugins' 2612.7857ms
Time for 'nx/core/project-json:createNodes' 0.6174000000000888
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.854Z - Established a connection. Number of open connections: 2
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.855Z - Established a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.856Z - Established a connection. Number of open connections: 4
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.856Z - Established a connection. Number of open connections: 5
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.857Z - Closed a connection. Number of open connections: 4
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.857Z - Closed a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.858Z - Closed a connection. Number of open connections: 2
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.859Z - Established a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.871Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.879Z - [REQUEST]: Responding to the client. handleGlob
Time for '@nx/react/router-plugin:createNodes' 56.23350000000028
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.879Z - Established a connection. Number of open connections: 4
Time for 'total for sendMessageToDaemon()' 37.184200000000146
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.880Z - Established a connection. Number of open connections: 5
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.882Z - Established a connection. Number of open connections: 6
Time for 'deserialize daemon response' 0.0729999999998654
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.882Z - Established a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.883Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.883Z - Handled HASH_GLOB. Handling time: 1. Response time: 13.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.884Z - Closed a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.885Z - Closed a connection. Number of open connections: 5
Time for 'total for sendMessageToDaemon()' 24.93730000000005
Time for 'deserialize daemon response' 0.08740000000034343
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.889Z - Done responding to the client handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.889Z - Handled GLOB. Handling time: 4. Response time: 10.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.890Z - Established a connection. Number of open connections: 6
Time for 'nx/core/package-json:createNodes' 39.787300000000414
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.894Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.894Z - Established a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.895Z - Established a connection. Number of open connections: 8
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.900Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.901Z - Established a connection. Number of open connections: 9
Time for 'total for sendMessageToDaemon()' 36.490999999999985
Time for 'deserialize daemon response' 0.06320000000005166
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.912Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.913Z - Closed a connection. Number of open connections: 8
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.913Z - Closed a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.915Z - Established a connection. Number of open connections: 8
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.921Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.922Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.922Z - Handled HASH_GLOB. Handling time: 2. Response time: 28.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.923Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.923Z - Handled HASH_GLOB. Handling time: 1. Response time: 23.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.934Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.940Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.940Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.941Z - Handled HASH_GLOB. Handling time: 8. Response time: 29.
Time for '@nx/jest/plugin:createNodes' 99.03279999999995
Time for 'total for sendMessageToDaemon()' 51.1882999999998
Time for 'deserialize daemon response' 0.11889999999993961
Time for '@nx/expo/plugin:createNodes' 103.69720000000007
Time for 'nx/js/dependencies-and-lockfile:createNodes' 87.66790000000037
Time for 'total for sendMessageToDaemon()' 37.552200000000084
Time for 'deserialize daemon response' 0.05019999999967695
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.955Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.956Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.956Z - Handled HASH_GLOB. Handling time: 2. Response time: 35.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.956Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.956Z - Handled HASH_GLOB. Handling time: 7. Response time: 22.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.957Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.957Z - Handled HASH_GLOB. Handling time: 2. Response time: 17.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.973Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:02.973Z - Handled HASH_GLOB. Handling time: 10. Response time: 18.
Time for 'total for sendMessageToDaemon()' 70.53580000000011
Time for 'deserialize daemon response' 0.1613999999999578
Time for '@nx/js/typescript:createNodes' 815.2764999999999
Time for 'total for sendMessageToDaemon()' 28.775100000000293
Time for 'deserialize daemon response' 0.04739999999992506
Time for '@nx/webpack/plugin:createNodes' 2838.9523
Time for 'total for sendMessageToDaemon()' 54.103200000000015
Time for 'deserialize daemon response' 0.06920000000036453
Time for 'total for sendMessageToDaemon()' 34.753999999999905
Time for 'deserialize daemon response' 0.07029999999986103
(node:15628) [DEP0180] DeprecationWarning: fs.Stats constructor is deprecated.
(Use `node --trace-deprecation ...` to show where the warning was created)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:06.705Z - [REQUEST]: Responding to the client. handleNxWorkspaceFiles
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:06.706Z - Done responding to the client handleNxWorkspaceFiles
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:06.706Z - Handled GET_FILES_IN_DIRECTORY. Handling time: 1. Response time: 1.
Time for '@nx/playwright/plugin:createNodes' 3884.1914999999995
Time for 'total for sendMessageToDaemon()' 12.43159999999989
Time for 'deserialize daemon response' 0.04699999999957072
Time for '@nx/eslint/plugin:createNodes' 4970.8318
Time for '@nx/vite/plugin:createNodes' 6399.6808
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:09.319Z - Time taken for 'build-project-configs' 6454.0819ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:09.323Z - Time taken for '@nx/js/typescript:createDependencies' 20.85239999999976ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:09.325Z - Time taken for '@nx/vite/plugin:createDependencies' 17.326499999999214ms
Time for 'build typescript dependencies' 24.210399999999936
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:09.563Z - Wrote project graph cache to C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\.nx\workspace-data\project-graph.json
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:09.563Z - [SYNC]: collect registered sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:09.567Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:09.568Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:09.571Z - Time taken for 'total for creating and serializing project graph' 9381.3534ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:09.577Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:09.577Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 9382. Response time: 9.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:09.680Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:10.308Z - Established a connection. Number of open connections: 9
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:10.308Z - Established a connection. Number of open connections: 10
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:10.309Z - Closed a connection. Number of open connections: 9
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:10.312Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:10.313Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:10.313Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:10.315Z - Time taken for 'total for creating and serializing project graph' 1.4909999999999854ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:10.361Z - [SYNC]: @nx/js:typescript-sync changes: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:10.361Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 680.7153999999991ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:10.362Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:10.362Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 49.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:16.401Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:16.402Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:16.402Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:16.403Z - Time taken for 'total for creating and serializing project graph' 1.1491000000023632ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:16.405Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:16.405Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 3.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:16.444Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:16.445Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:16.445Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:16.448Z - Time taken for 'total for creating and serializing project graph' 1.1896000000015192ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:16.451Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:16.452Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 6.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:16.623Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:16.624Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:16.625Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:16.626Z - Time taken for 'total for creating and serializing project graph' 1.0126999999993131ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:16.628Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:16.628Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 3.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:18.467Z - [WATCHER]: Processing file changes in outputs
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:18.472Z - [WATCHER]: 0 file(s) created or restored, 15 file(s) modified, 0 file(s) deleted
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:18.584Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:18.584Z - [REQUEST]: libs/auth/src/index.ts,libs/auth/src/lib/auth.spec.ts,libs/auth/package.json,libs/auth/.spec.swcrc,libs/auth/tsconfig.lib.json,libs/auth/jest.config.ts,libs/auth/src/lib/auth.ts,libs/auth/tsconfig.json,libs/auth/.swcrc,package.json,libs/auth/eslint.config.mjs,tsconfig.json,libs/auth/tsconfig.spec.json,libs/auth/README.md,nx.json
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:18.584Z - [REQUEST]: 
Time for 'nx/core/project-json:createNodes' 0.8076000000000931
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:18.629Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:18.636Z - [REQUEST]: Responding to the client. handleGlob
Time for '@nx/react/router-plugin:createNodes' 39.15200000000186
Time for 'total for sendMessageToDaemon()' 26.769599999999627
Time for 'deserialize daemon response' 0.037900000002991874
Time for 'total for sendMessageToDaemon()' 20.841800000001967
Time for 'deserialize daemon response' 0.054500000002008164
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:18.641Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:18.641Z - Time taken for 'hash changed files from watcher' 1.2507000000005064ms
Time for '@nx/playwright/plugin:createNodes' 27.837099999997008
Time for 'total for sendMessageToDaemon()' 23.513300000002346
Time for 'deserialize daemon response' 0.042100000002392335
Time for 'nx/core/package-json:createNodes' 24.78150000000096
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:18.649Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:18.654Z - [REQUEST]: Responding to the client. handleHashGlob
Time for '@nx/vite/plugin:createNodes' 40.136500000000524
Time for 'total for sendMessageToDaemon()' 24.935999999997875
Time for 'deserialize daemon response' 0.052700000000186265
Time for '@nx/webpack/plugin:createNodes' 38.38270000000193
Time for 'total for sendMessageToDaemon()' 29.291000000001077
Time for 'deserialize daemon response' 0.04650000000037835
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:18.660Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:18.660Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:18.660Z - Handled HASH_GLOB. Handling time: 0. Response time: 31.
Time for 'total for sendMessageToDaemon()' 34.368500000000495
Time for 'deserialize daemon response' 0.0305000000007567
Time for 'nx/js/dependencies-and-lockfile:createNodes' 34.253200000002835
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:18.669Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:18.669Z - Done responding to the client handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:18.669Z - Handled GLOB. Handling time: 2. Response time: 33.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:18.684Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:18.685Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:18.685Z - Handled HASH_GLOB. Handling time: 2. Response time: 44.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:18.686Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:18.686Z - Handled HASH_GLOB. Handling time: 4. Response time: 37.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:18.686Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:18.686Z - Handled HASH_GLOB. Handling time: 1. Response time: 32.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:18.687Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:18.687Z - Handled HASH_GLOB. Handling time: 1. Response time: 27.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:18.692Z - [REQUEST]: Responding to the client. handleHashGlob
Time for 'total for sendMessageToDaemon()' 46.69720000000234
Time for 'deserialize daemon response' 0.03840000000127475
Time for '@nx/expo/plugin:createNodes' 80.5096999999987
Time for 'total for sendMessageToDaemon()' 31.36739999999918
Time for 'deserialize daemon response' 0.04200000000128057
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:18.706Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:18.707Z - Handled HASH_GLOB. Handling time: 6. Response time: 38.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:18.707Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:18.707Z - Handled HASH_GLOB. Handling time: 12. Response time: 23.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:18.712Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:18.712Z - Handled HASH_GLOB. Handling time: 1. Response time: 20.
Time for '@nx/js/typescript:createNodes' 137.97129999999743
Time for '@nx/eslint/plugin:createNodes' 174.28139999999985
Time for '@nx/jest/plugin:createNodes' 1134.9832000000024
Time for 'total for sendMessageToDaemon()' 35.22739999999976
Time for 'deserialize daemon response' 0.04400000000168802
(node:13696) [DEP0180] DeprecationWarning: fs.Stats constructor is deprecated.
(Use `node --trace-deprecation ...` to show where the warning was created)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:19.813Z - Time taken for 'build-project-configs' 1176.369200000001ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:19.817Z - Time taken for '@nx/js/typescript:createDependencies' 33.10780000000159ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:19.818Z - Time taken for '@nx/vite/plugin:createDependencies' 26.361400000001595ms
Time for 'build typescript dependencies' 22.958699999999226
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:19.966Z - Wrote project graph cache to C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\.nx\workspace-data\project-graph.json
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:19.966Z - [SYNC]: collect registered sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:19.968Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:19.968Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:19.968Z - Time taken for 'total execution time for createProjectGraph()' 153.1900999999998ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:20.182Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:20.188Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:20.189Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:20.189Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:20.191Z - Time taken for 'total for creating and serializing project graph' 1.1604999999981374ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:20.224Z - [SYNC]: @nx/js:typescript-sync changes: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:20.225Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 42.029999999998836ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:20.225Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:20.225Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 36.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:30.932Z - [WATCHER]: Processing file changes in outputs
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:30.938Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo (sources)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:30.939Z - Closed a connection. Number of open connections: 8
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:30.939Z - Closed a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:30.939Z - Closed a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:30.939Z - Closed a connection. Number of open connections: 5
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:30.939Z - Closed a connection. Number of open connections: 4
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:30.939Z - Closed a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:30.939Z - Closed a connection. Number of open connections: 2
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:30.939Z - Closed a connection. Number of open connections: 1
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:30.940Z - Closed a connection. Number of open connections: 0
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:30.940Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo (outputs)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:30.944Z - Server stopped because: "LOCK_FILES_CHANGED"
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:53.222Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\dc5fb78e19b1f076be7c\d.sock
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:53.229Z - [WATCHER]: Subscribed to changes within: C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo (native)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:53.235Z - Established a connection. Number of open connections: 1
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:53.237Z - Established a connection. Number of open connections: 2
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:53.241Z - Closed a connection. Number of open connections: 1
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:53.245Z - [REQUEST]: Client Request for Project Graph Received
Time for 'plugin worker 16176 code loading' 27.315299999999993
Time for 'plugin worker 7896 code loading' 25.011300000000006
Time for 'plugin worker 4320 code loading' 20.077300000000008
Time for 'plugin worker 11328 code loading' 17.726100000000002
Time for 'plugin worker 6532 code loading' 26.868700000000004
Time for 'plugin worker 17720 code loading' 30.2872
Time for 'plugin worker 17728 code loading' 27.12039999999999
Time for 'plugin worker 13272 code loading' 44.242000000000004
Time for 'plugin worker 12808 code loading' 25.800300000000007
Time for 'plugin worker 12200 code loading' 49.47900000000001
Time for 'plugin worker 19380 code loading' 21.076300000000003
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:54.567Z - Time taken for 'Load Nx Plugin: C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\node_modules\nx\src\plugins\project-json\build-nodes\project-json' 1290.7916ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:54.906Z - Time taken for 'Load Nx Plugin: C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\node_modules\nx\src\plugins\js' 1636.5200000000002ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:54.925Z - Time taken for 'loadDefaultNxPlugins' 1655.3881999999999ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.163Z - Time taken for 'Load Nx Plugin: @nx/js/typescript' 1914.2875000000004ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.176Z - Time taken for 'Load Nx Plugin: @nx/eslint/plugin' 1922.9708ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.393Z - Time taken for 'Load Nx Plugin: @nx/jest/plugin' 2132.1731ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.448Z - Time taken for 'Load Nx Plugin: @nx/react/router-plugin' 2196.102ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.481Z - Time taken for 'Load Nx Plugin: @nx/webpack/plugin' 2213.6748ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.518Z - Time taken for 'Load Nx Plugin: @nx/expo/plugin' 2254.0841ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.528Z - Time taken for 'Load Nx Plugin: @nx/playwright/plugin' 2268.9954000000002ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.561Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.561Z - [REQUEST]: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.561Z - [REQUEST]: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.573Z - No files changed, but no in-memory cached project graph found. Recomputing it...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.610Z - Time taken for 'loadSpecifiedNxPlugins' 2312.3896ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.612Z - Established a connection. Number of open connections: 2
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.612Z - Established a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.613Z - Established a connection. Number of open connections: 4
Time for 'nx/core/project-json:createNodes' 1.0895000000000437
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.616Z - Established a connection. Number of open connections: 5
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.618Z - Established a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.620Z - Closed a connection. Number of open connections: 5
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.620Z - Closed a connection. Number of open connections: 4
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.620Z - Closed a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.621Z - Established a connection. Number of open connections: 4
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.626Z - [REQUEST]: Responding to the client. handleHashMultiGlob
Time for '@nx/react/router-plugin:createNodes' 49.82979999999998
Time for 'total for sendMessageToDaemon()' 32.30560000000014
Time for 'deserialize daemon response' 0.06359999999995125
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.636Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.642Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.643Z - Established a connection. Number of open connections: 5
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.644Z - Established a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.644Z - Established a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.644Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.645Z - Handled HASH_GLOB. Handling time: 0. Response time: 20.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.645Z - Established a connection. Number of open connections: 8
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.646Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.646Z - Handled HASH_GLOB. Handling time: 3. Response time: 10.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.648Z - Closed a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.648Z - Closed a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.648Z - Closed a connection. Number of open connections: 5
Time for 'total for sendMessageToDaemon()' 32.94200000000001
Time for 'deserialize daemon response' 0.09600000000000364
Time for 'nx/core/package-json:createNodes' 37.85050000000001
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.649Z - Done responding to the client handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.649Z - Handled GLOB. Handling time: 2. Response time: 7.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.650Z - Established a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.650Z - Established a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.650Z - Established a connection. Number of open connections: 8
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.655Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.656Z - Established a connection. Number of open connections: 9
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.666Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.667Z - Closed a connection. Number of open connections: 8
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.673Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.682Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.683Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.684Z - Handled HASH_GLOB. Handling time: 2. Response time: 29.
Time for 'total for sendMessageToDaemon()' 54.36210000000028
Time for 'deserialize daemon response' 0.0866000000000895
Time for 'total for sendMessageToDaemon()' 30.090099999999893
Time for 'deserialize daemon response' 0.07769999999982247
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.695Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.696Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.696Z - Handled HASH_GLOB. Handling time: 7. Response time: 30.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.697Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.697Z - Handled HASH_GLOB. Handling time: 2. Response time: 24.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.697Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.697Z - Handled HASH_GLOB. Handling time: 1. Response time: 16.
Time for '@nx/jest/plugin:createNodes' 98.1251000000002
Time for 'total for sendMessageToDaemon()' 48.207200000000284
Time for 'deserialize daemon response' 0.10840000000007421
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.707Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.708Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.708Z - Handled HASH_GLOB. Handling time: 8. Response time: 13.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.709Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:55.709Z - Handled HASH_GLOB. Handling time: 7. Response time: 2.
Time for '@nx/expo/plugin:createNodes' 108.61200000000008
Time for 'total for sendMessageToDaemon()' 23.02469999999994
Time for 'deserialize daemon response' 0.05279999999993379
Time for 'nx/js/dependencies-and-lockfile:createNodes' 92.23019999999997
Time for '@nx/js/typescript:createNodes' 763.7156
Time for 'total for sendMessageToDaemon()' 19.615800000000036
Time for 'deserialize daemon response' 0.06199999999989814
Time for '@nx/webpack/plugin:createNodes' 2434.6851
Time for 'total for sendMessageToDaemon()' 41.83510000000024
Time for 'deserialize daemon response' 0.11570000000028813
Time for 'total for sendMessageToDaemon()' 25.203199999999924
Time for 'deserialize daemon response' 0.05580000000009022
(node:6532) [DEP0180] DeprecationWarning: fs.Stats constructor is deprecated.
(Use `node --trace-deprecation ...` to show where the warning was created)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:58.843Z - [REQUEST]: Responding to the client. handleNxWorkspaceFiles
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:58.844Z - Done responding to the client handleNxWorkspaceFiles
[NX v21.3.9 Daemon Server] - 2025-07-30T12:12:58.844Z - Handled GET_FILES_IN_DIRECTORY. Handling time: 0. Response time: 1.
Time for '@nx/playwright/plugin:createNodes' 3257.4509000000003
Time for 'total for sendMessageToDaemon()' 7.821199999999408
Time for 'deserialize daemon response' 0.03659999999945285
Time for '@nx/eslint/plugin:createNodes' 4233.2858
Time for '@nx/vite/plugin:createNodes' 5589.482599999999
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:01.296Z - Time taken for 'build-project-configs' 5645.3824ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:01.298Z - Time taken for '@nx/js/typescript:createDependencies' 20.654099999999744ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:01.302Z - Time taken for '@nx/vite/plugin:createDependencies' 18.08650000000125ms
Time for 'build typescript dependencies' 0.6018000000003667
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:01.524Z - Wrote project graph cache to C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\.nx\workspace-data\project-graph.json
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:01.524Z - [SYNC]: collect registered sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:01.527Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:01.528Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:01.530Z - Time taken for 'total for creating and serializing project graph' 8282.9221ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:01.535Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:01.535Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 8283. Response time: 7.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:01.631Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:02.768Z - Established a connection. Number of open connections: 9
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:02.771Z - Established a connection. Number of open connections: 10
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:02.777Z - Closed a connection. Number of open connections: 9
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:02.804Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:02.810Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:02.811Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:02.820Z - Time taken for 'total for creating and serializing project graph' 6.67410000000018ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:03.080Z - [SYNC]: @nx/js:typescript-sync changes: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:03.081Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 1447.2362999999987ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:03.084Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:03.084Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 8. Response time: 273.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:03.501Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:03.503Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:03.503Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:03.505Z - Time taken for 'total for creating and serializing project graph' 1.5655000000006112ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:03.508Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:03.509Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 6.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:03.556Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:03.557Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:03.558Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:03.559Z - Time taken for 'total for creating and serializing project graph' 1.2214999999996508ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:03.561Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:03.562Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 4.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:03.746Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:03.748Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:03.748Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:03.750Z - Time taken for 'total for creating and serializing project graph' 1.86620000000039ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:03.751Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:03.751Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 3.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:06.064Z - [WATCHER]: Processing file changes in outputs
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:06.069Z - [WATCHER]: 0 file(s) created or restored, 13 file(s) modified, 0 file(s) deleted
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:06.184Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:06.184Z - [REQUEST]: tsconfig.json,libs/models/src/index.ts,libs/models/src/lib/models.spec.ts,libs/models/.spec.swcrc,libs/models/tsconfig.spec.json,libs/models/.swcrc,libs/models/jest.config.ts,libs/models/src/lib/models.ts,libs/models/tsconfig.json,libs/models/tsconfig.lib.json,libs/models/package.json,libs/models/README.md,libs/models/eslint.config.mjs
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:06.184Z - [REQUEST]: 
Time for 'nx/core/project-json:createNodes' 0.6018999999996595
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:06.227Z - [REQUEST]: Responding to the client. handleHashMultiGlob
Time for '@nx/react/router-plugin:createNodes' 38.79830000000038
Time for 'total for sendMessageToDaemon()' 28.09100000000035
Time for 'deserialize daemon response' 0.04750000000058208
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:06.238Z - [REQUEST]: Responding to the client. handleGlob
Time for 'total for sendMessageToDaemon()' 26.604699999999866
Time for 'deserialize daemon response' 0.0761000000002241
Time for 'nx/core/package-json:createNodes' 26.306400000001304
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:06.252Z - [REQUEST]: Responding to the client. handleHashGlob
Time for '@nx/playwright/plugin:createNodes' 45.05779999999868
Time for 'total for sendMessageToDaemon()' 37.278299999999945
Time for 'nx/js/dependencies-and-lockfile:createNodes' 28.93239999999969
Time for 'deserialize daemon response' 0.04260000000067521
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:06.260Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:06.260Z - Time taken for 'hash changed files from watcher' 1.3389999999999418ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:06.266Z - [REQUEST]: Responding to the client. handleHashGlob
Time for '@nx/vite/plugin:createNodes' 54.994699999999284
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:06.268Z - Done responding to the client handleHashMultiGlob
Time for 'total for sendMessageToDaemon()' 41.38900000000103
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:06.268Z - Handled HASH_GLOB. Handling time: 0. Response time: 41.
Time for 'deserialize daemon response' 0.04940000000169675
Time for '@nx/webpack/plugin:createNodes' 51.11750000000029
Time for 'total for sendMessageToDaemon()' 42.75540000000001
Time for 'deserialize daemon response' 0.052999999999883585
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:06.272Z - [REQUEST]: Responding to the client. handleHashGlob
Time for 'total for sendMessageToDaemon()' 45.181700000001
Time for 'deserialize daemon response' 0.04230000000097789
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:06.280Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:06.281Z - Done responding to the client handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:06.281Z - Handled GLOB. Handling time: 3. Response time: 43.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:06.293Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:06.294Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:06.294Z - Handled HASH_GLOB. Handling time: 1. Response time: 42.
Time for 'total for sendMessageToDaemon()' 53.5533000000014
Time for 'deserialize daemon response' 0.030700000001161243
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:06.304Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:06.304Z - Handled HASH_GLOB. Handling time: 4. Response time: 44.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:06.305Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:06.305Z - Handled HASH_GLOB. Handling time: 1. Response time: 39.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:06.306Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:06.306Z - Handled HASH_GLOB. Handling time: 1. Response time: 34.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:06.312Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:06.312Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:06.312Z - Handled HASH_GLOB. Handling time: 5. Response time: 32.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:06.312Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:06.312Z - Handled HASH_GLOB. Handling time: 9. Response time: 19.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:06.314Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:06.314Z - Handled HASH_GLOB. Handling time: 2. Response time: 2.
Time for '@nx/expo/plugin:createNodes' 101.69470000000001
Time for 'total for sendMessageToDaemon()' 38.433100000000195
Time for 'deserialize daemon response' 0.04370000000017171
Time for '@nx/js/typescript:createNodes' 167.39910000000054
Time for '@nx/eslint/plugin:createNodes' 185.81690000000162
Time for '@nx/jest/plugin:createNodes' 1236.9049999999988
Time for 'total for sendMessageToDaemon()' 51.09430000000066
Time for 'deserialize daemon response' 0.060800000001108856
(node:17728) [DEP0180] DeprecationWarning: fs.Stats constructor is deprecated.
(Use `node --trace-deprecation ...` to show where the warning was created)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:07.493Z - Time taken for 'build-project-configs' 1271.0872ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:07.494Z - Time taken for '@nx/js/typescript:createDependencies' 21.513100000000122ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:07.495Z - Time taken for '@nx/vite/plugin:createDependencies' 17.08679999999913ms
Time for 'build typescript dependencies' 28.316799999998693
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:07.609Z - Wrote project graph cache to C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\.nx\workspace-data\project-graph.json
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:07.610Z - [SYNC]: collect registered sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:07.611Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:07.612Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:07.612Z - Time taken for 'total execution time for createProjectGraph()' 108.85260000000017ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:07.813Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:07.820Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:07.821Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:07.821Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:07.823Z - Time taken for 'total for creating and serializing project graph' 1.0360000000000582ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:07.863Z - [SYNC]: @nx/js:typescript-sync changes: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:07.864Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 49.82430000000022ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:07.865Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:07.865Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 44.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:17.366Z - [WATCHER]: Processing file changes in outputs
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:17.367Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo (sources)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:17.367Z - Closed a connection. Number of open connections: 8
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:17.368Z - Closed a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:17.368Z - Closed a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:17.368Z - Closed a connection. Number of open connections: 5
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:17.368Z - Closed a connection. Number of open connections: 4
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:17.368Z - Closed a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:17.368Z - Closed a connection. Number of open connections: 2
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:17.369Z - Closed a connection. Number of open connections: 1
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:17.369Z - Closed a connection. Number of open connections: 0
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:17.369Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo (outputs)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:17.372Z - Server stopped because: "LOCK_FILES_CHANGED"
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:26.175Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\dc5fb78e19b1f076be7c\d.sock
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:26.183Z - [WATCHER]: Subscribed to changes within: C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo (native)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:26.188Z - Established a connection. Number of open connections: 1
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:26.189Z - Established a connection. Number of open connections: 2
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:26.192Z - Closed a connection. Number of open connections: 1
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:26.196Z - [REQUEST]: Client Request for Project Graph Received
Time for 'plugin worker 14104 code loading' 25.619299999999996
Time for 'plugin worker 7892 code loading' 30.7868
Time for 'plugin worker 2600 code loading' 29.332399999999993
Time for 'plugin worker 3920 code loading' 15.157399999999996
Time for 'plugin worker 15628 code loading' 18.567499999999995
Time for 'plugin worker 7424 code loading' 53.0737
Time for 'plugin worker 19156 code loading' 15.899799999999999
Time for 'plugin worker 8716 code loading' 19.114199999999997
Time for 'plugin worker 5308 code loading' 40.16569999999999
Time for 'plugin worker 19196 code loading' 39.1772
Time for 'plugin worker 13596 code loading' 22.19109999999999
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:27.726Z - Time taken for 'Load Nx Plugin: C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\node_modules\nx\src\plugins\project-json\build-nodes\project-json' 1502.6167ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:27.739Z - Time taken for 'Load Nx Plugin: C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\node_modules\nx\src\plugins\package-json' 1516.5810000000001ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:27.876Z - Time taken for 'loadDefaultNxPlugins' 1655.3462ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.296Z - Time taken for 'Load Nx Plugin: @nx/js/typescript' 2096.5744ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.419Z - Time taken for 'Load Nx Plugin: @nx/eslint/plugin' 2214.1395ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.436Z - Time taken for 'Load Nx Plugin: @nx/jest/plugin' 2222.3363999999997ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.529Z - Time taken for 'Load Nx Plugin: @nx/react/router-plugin' 2326.2492ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.535Z - Time taken for 'Load Nx Plugin: @nx/vite/plugin' 2327.9327ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.583Z - Time taken for 'Load Nx Plugin: @nx/expo/plugin' 2367.0108ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.586Z - Time taken for 'Load Nx Plugin: @nx/playwright/plugin' 2376.2621000000004ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.628Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.629Z - [REQUEST]: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.629Z - [REQUEST]: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.638Z - No files changed, but no in-memory cached project graph found. Recomputing it...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.687Z - Time taken for 'loadSpecifiedNxPlugins' 2428.8039ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.689Z - Established a connection. Number of open connections: 2
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.689Z - Established a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.690Z - Established a connection. Number of open connections: 4
Time for 'nx/core/project-json:createNodes' 0.6009000000003653
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.692Z - Established a connection. Number of open connections: 5
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.693Z - Closed a connection. Number of open connections: 4
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.693Z - Closed a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.694Z - Established a connection. Number of open connections: 4
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.701Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.701Z - Established a connection. Number of open connections: 5
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.709Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.710Z - Established a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.711Z - Closed a connection. Number of open connections: 5
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.711Z - Closed a connection. Number of open connections: 4
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.712Z - Established a connection. Number of open connections: 5
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.713Z - Established a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.714Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.714Z - Handled HASH_GLOB. Handling time: 1. Response time: 13.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.714Z - Established a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.715Z - Done responding to the client handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.715Z - Handled GLOB. Handling time: 3. Response time: 6.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.715Z - Established a connection. Number of open connections: 8
Time for 'total for sendMessageToDaemon()' 28.983000000000175
Time for 'deserialize daemon response' 0.08500000000003638
Time for '@nx/react/router-plugin:createNodes' 59.98610000000008
Time for 'total for sendMessageToDaemon()' 33.95900000000029
Time for 'deserialize daemon response' 0.07840000000032887
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.721Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.722Z - Closed a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.722Z - Closed a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.729Z - [REQUEST]: Responding to the client. handleHashMultiGlob
Time for 'nx/core/package-json:createNodes' 47.233999999999924
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.736Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.737Z - Established a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.754Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.755Z - Established a connection. Number of open connections: 8
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.756Z - Established a connection. Number of open connections: 9
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.757Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.757Z - Handled HASH_GLOB. Handling time: 3. Response time: 36.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.757Z - Closed a connection. Number of open connections: 8
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.758Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.759Z - Handled HASH_GLOB. Handling time: 3. Response time: 30.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.760Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.760Z - Handled HASH_GLOB. Handling time: 1. Response time: 24.
Time for 'total for sendMessageToDaemon()' 35.07369999999992
Time for 'deserialize daemon response' 0.08109999999987849
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.769Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.769Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.769Z - Handled HASH_GLOB. Handling time: 14. Response time: 15.
Time for 'total for sendMessageToDaemon()' 54.392399999999725
Time for 'deserialize daemon response' 0.1619999999998072
Time for 'nx/js/dependencies-and-lockfile:createNodes' 85.93340000000035
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.785Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.786Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.786Z - Handled HASH_GLOB. Handling time: 4. Response time: 17.
Time for '@nx/jest/plugin:createNodes' 112.14660000000003
Time for 'total for sendMessageToDaemon()' 64.47690000000011
Time for 'deserialize daemon response' 0.06900000000041473
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.818Z - [REQUEST]: Responding to the client. handleHashGlob
Time for '@nx/expo/plugin:createNodes' 142.6550000000002
Time for 'total for sendMessageToDaemon()' 47.14930000000004
Time for 'deserialize daemon response' 0.044100000000071304
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.833Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.833Z - Handled HASH_GLOB. Handling time: 12. Response time: 48.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.835Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:28.836Z - Handled HASH_GLOB. Handling time: 29. Response time: 18.
Time for '@nx/js/typescript:createNodes' 769.4792000000002
Time for 'total for sendMessageToDaemon()' 40.040700000000015
Time for 'deserialize daemon response' 0.05500000000029104
Time for '@nx/webpack/plugin:createNodes' 2645.5946999999996
Time for 'total for sendMessageToDaemon()' 34.9704999999999
Time for 'deserialize daemon response' 0.0747999999998683
Time for 'total for sendMessageToDaemon()' 27.996300000000247
Time for 'deserialize daemon response' 0.06919999999990978
(node:3920) [DEP0180] DeprecationWarning: fs.Stats constructor is deprecated.
(Use `node --trace-deprecation ...` to show where the warning was created)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:32.220Z - [REQUEST]: Responding to the client. handleNxWorkspaceFiles
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:32.221Z - Done responding to the client handleNxWorkspaceFiles
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:32.221Z - Handled GET_FILES_IN_DIRECTORY. Handling time: 1. Response time: 1.
Time for '@nx/playwright/plugin:createNodes' 3561.0736999999995
Time for 'total for sendMessageToDaemon()' 14.332100000000537
Time for 'deserialize daemon response' 0.049500000000080036
Time for '@nx/eslint/plugin:createNodes' 4598.722400000001
Time for '@nx/vite/plugin:createNodes' 5827.8035
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:34.587Z - Time taken for 'build-project-configs' 5895.795ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:34.588Z - Time taken for '@nx/js/typescript:createDependencies' 17.889799999999013ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:34.591Z - Time taken for '@nx/vite/plugin:createDependencies' 16.97559999999976ms
Time for 'build typescript dependencies' 0.595500000001266
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:34.822Z - Wrote project graph cache to C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\.nx\workspace-data\project-graph.json
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:34.823Z - [SYNC]: collect registered sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:34.826Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:34.826Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:34.827Z - Time taken for 'total for creating and serializing project graph' 8629.9782ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:34.834Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:34.834Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 8630. Response time: 8.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:34.929Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:35.548Z - Established a connection. Number of open connections: 9
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:35.548Z - Established a connection. Number of open connections: 10
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:35.549Z - Closed a connection. Number of open connections: 9
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:35.554Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:35.555Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:35.556Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:35.559Z - Time taken for 'total for creating and serializing project graph' 1.7602000000006228ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:35.611Z - [SYNC]: @nx/js:typescript-sync changes: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:35.611Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 681.1446999999989ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:35.612Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:35.612Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 56.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:35.820Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:35.821Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:35.821Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:35.823Z - Time taken for 'total for creating and serializing project graph' 1.0290999999997439ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:35.825Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:35.825Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 4.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:35.874Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:35.876Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:35.876Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:35.878Z - Time taken for 'total for creating and serializing project graph' 1.4213999999992666ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:35.882Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:35.882Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 6.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:36.055Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:36.056Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:36.056Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:36.057Z - Time taken for 'total for creating and serializing project graph' 0.9205000000001746ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:36.059Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:36.059Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 3.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:37.081Z - [WATCHER]: Processing file changes in outputs
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:37.086Z - [WATCHER]: 0 file(s) created or restored, 13 file(s) modified, 0 file(s) deleted
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:37.199Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:37.199Z - [REQUEST]: tsconfig.json,libs/utils/README.md,libs/utils/src/lib/utils.spec.ts,libs/utils/.swcrc,libs/utils/eslint.config.mjs,libs/utils/tsconfig.spec.json,libs/utils/tsconfig.json,libs/utils/src/index.ts,libs/utils/tsconfig.lib.json,libs/utils/src/lib/utils.ts,libs/utils/package.json,libs/utils/.spec.swcrc,libs/utils/jest.config.ts
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:37.199Z - [REQUEST]: 
Time for 'nx/core/project-json:createNodes' 0.76759999999922
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:37.276Z - [REQUEST]: Responding to the client. handleHashMultiGlob
Time for '@nx/react/router-plugin:createNodes' 65.67289999999957
Time for 'total for sendMessageToDaemon()' 55.97960000000057
Time for 'deserialize daemon response' 0.08920000000034634
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:37.286Z - [REQUEST]: Responding to the client. handleGlob
Time for 'total for sendMessageToDaemon()' 46.8716000000004
Time for 'deserialize daemon response' 0.06430000000000291
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:37.291Z - [REQUEST]: Responding to the client. handleHashGlob
Time for '@nx/playwright/plugin:createNodes' 55.18459999999868
Time for 'total for sendMessageToDaemon()' 48.640699999999924
Time for 'deserialize daemon response' 0.04589999999916472
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:37.301Z - [REQUEST]: Responding to the client. handleHashMultiGlob
Time for 'nx/core/package-json:createNodes' 31.62970000000132
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:37.306Z - [REQUEST]: Responding to the client. handleHashGlob
Time for 'nx/js/dependencies-and-lockfile:createNodes' 38.00039999999899
Time for '@nx/vite/plugin:createNodes' 70.11110000000008
Time for 'total for sendMessageToDaemon()' 51.09469999999965
Time for 'total for sendMessageToDaemon()' 41.68870000000061
Time for 'deserialize daemon response' 0.05230000000119617
Time for 'deserialize daemon response' 0.04629999999997381
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:37.313Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:37.321Z - Time taken for 'hash changed files from watcher' 1.5098000000016327ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:37.323Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:37.323Z - Handled HASH_GLOB. Handling time: 1. Response time: 47.
Time for '@nx/webpack/plugin:createNodes' 65.88670000000093
Time for 'total for sendMessageToDaemon()' 44.475899999999456
Time for 'deserialize daemon response' 0.06819999999970605
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:37.336Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:37.337Z - Done responding to the client handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:37.337Z - Handled GLOB. Handling time: 2. Response time: 51.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:37.350Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:37.351Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:37.351Z - Handled HASH_GLOB. Handling time: 1. Response time: 60.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:37.351Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:37.352Z - Handled HASH_GLOB. Handling time: 3. Response time: 51.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:37.352Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:37.352Z - Handled HASH_GLOB. Handling time: 2. Response time: 46.
Time for 'total for sendMessageToDaemon()' 62.628099999999904
Time for 'deserialize daemon response' 0.043300000001181616
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:37.368Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:37.369Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:37.369Z - Handled HASH_GLOB. Handling time: 3. Response time: 56.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:37.370Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:37.370Z - Handled HASH_GLOB. Handling time: 10. Response time: 34.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:37.371Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:37.371Z - Handled HASH_GLOB. Handling time: 10. Response time: 21.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:37.371Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:37.371Z - Handled HASH_GLOB. Handling time: 2. Response time: 3.
Time for '@nx/expo/plugin:createNodes' 120.11349999999948
Time for 'total for sendMessageToDaemon()' 59.90909999999894
Time for 'deserialize daemon response' 0.09609999999884167
Time for '@nx/js/typescript:createNodes' 209.04169999999976
Time for '@nx/eslint/plugin:createNodes' 250.42399999999907
Time for '@nx/jest/plugin:createNodes' 1104.8935999999994
Time for 'total for sendMessageToDaemon()' 54.02590000000055
Time for 'deserialize daemon response' 0.04960000000028231
(node:7424) [DEP0180] DeprecationWarning: fs.Stats constructor is deprecated.
(Use `node --trace-deprecation ...` to show where the warning was created)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:38.396Z - Time taken for 'build-project-configs' 1160.2258000000002ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:38.398Z - Time taken for '@nx/js/typescript:createDependencies' 21.489199999999983ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:38.400Z - Time taken for '@nx/vite/plugin:createDependencies' 16.289199999999255ms
Time for 'build typescript dependencies' 21.383499999999913
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:38.501Z - Wrote project graph cache to C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\.nx\workspace-data\project-graph.json
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:38.502Z - [SYNC]: collect registered sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:38.503Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:38.503Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:38.503Z - Time taken for 'total execution time for createProjectGraph()' 100.30500000000029ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:38.709Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:38.717Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:38.718Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:38.718Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:38.720Z - Time taken for 'total for creating and serializing project graph' 1.1718000000000757ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:38.751Z - [SYNC]: @nx/js:typescript-sync changes: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:38.752Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 42.044299999999566ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:38.752Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:38.752Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 34.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:48.918Z - [WATCHER]: Processing file changes in outputs
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:48.919Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo (sources)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:48.919Z - Closed a connection. Number of open connections: 8
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:48.919Z - Closed a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:48.920Z - Closed a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:48.920Z - Closed a connection. Number of open connections: 5
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:48.920Z - Closed a connection. Number of open connections: 4
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:48.920Z - Closed a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:48.920Z - Closed a connection. Number of open connections: 2
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:48.920Z - Closed a connection. Number of open connections: 1
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:48.920Z - Closed a connection. Number of open connections: 0
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:48.920Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo (outputs)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:13:48.922Z - Server stopped because: "LOCK_FILES_CHANGED"
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:30.051Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\dc5fb78e19b1f076be7c\d.sock
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:30.057Z - [WATCHER]: Subscribed to changes within: C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo (native)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:30.067Z - Established a connection. Number of open connections: 1
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:30.072Z - Closed a connection. Number of open connections: 0
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:30.073Z - Established a connection. Number of open connections: 1
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:30.077Z - [REQUEST]: Client Request for Project Graph Received
Time for 'plugin worker 16504 code loading' 17.712699999999998
Time for 'plugin worker 17672 code loading' 17.009599999999992
Time for 'plugin worker 16468 code loading' 28.804899999999996
Time for 'plugin worker 10376 code loading' 18.620900000000006
Time for 'plugin worker 10560 code loading' 23.736199999999997
Time for 'plugin worker 14312 code loading' 22.444599999999994
Time for 'plugin worker 17228 code loading' 18.39110000000001
Time for 'plugin worker 14228 code loading' 27.611100000000008
Time for 'plugin worker 11332 code loading' 18.402
Time for 'plugin worker 3128 code loading' 17.4366
Time for 'plugin worker 14172 code loading' 17.920500000000004
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:31.471Z - Time taken for 'Load Nx Plugin: C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\node_modules\nx\src\plugins\project-json\build-nodes\project-json' 1370.6617ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:31.573Z - Time taken for 'loadDefaultNxPlugins' 1476.0537999999997ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:31.980Z - Time taken for 'Load Nx Plugin: @nx/js/typescript' 1900.5772000000002ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:31.995Z - Time taken for 'Load Nx Plugin: @nx/jest/plugin' 1903.5230999999999ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.052Z - Time taken for 'Load Nx Plugin: @nx/eslint/plugin' 1967.2408ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.130Z - Time taken for 'Load Nx Plugin: @nx/react/router-plugin' 2047.2455000000002ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.154Z - Time taken for 'Load Nx Plugin: @nx/playwright/plugin' 2064.404ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.171Z - Time taken for 'Load Nx Plugin: @nx/expo/plugin' 2078.1673ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.210Z - Time taken for 'Load Nx Plugin: @nx/vite/plugin' 2123.4827999999998ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.254Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.254Z - [REQUEST]: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.254Z - [REQUEST]: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.262Z - No files changed, but no in-memory cached project graph found. Recomputing it...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.296Z - Time taken for 'loadSpecifiedNxPlugins' 2174.6564ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.299Z - Established a connection. Number of open connections: 2
Time for 'nx/core/project-json:createNodes' 0.7106999999998607
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.300Z - Established a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.301Z - Established a connection. Number of open connections: 4
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.302Z - Closed a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.303Z - Closed a connection. Number of open connections: 2
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.318Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.319Z - Established a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.320Z - Established a connection. Number of open connections: 4
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.321Z - Closed a connection. Number of open connections: 3
Time for '@nx/react/router-plugin:createNodes' 50.3411000000001
Time for 'total for sendMessageToDaemon()' 35.158800000000156
Time for 'deserialize daemon response' 0.07250000000021828
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.323Z - Established a connection. Number of open connections: 4
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.324Z - Established a connection. Number of open connections: 5
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.325Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.325Z - Handled HASH_GLOB. Handling time: 1. Response time: 7.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.325Z - Established a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.332Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.332Z - Established a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.337Z - Closed a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.337Z - Closed a connection. Number of open connections: 5
Time for 'total for sendMessageToDaemon()' 32.13239999999996
Time for 'deserialize daemon response' 0.08919999999989159
Time for 'nx/core/package-json:createNodes' 44.33619999999996
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.342Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.342Z - Established a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.343Z - Established a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.349Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.350Z - Established a connection. Number of open connections: 8
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.351Z - Done responding to the client handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.352Z - Handled GLOB. Handling time: 3. Response time: 20.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.352Z - Established a connection. Number of open connections: 9
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.368Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.368Z - Closed a connection. Number of open connections: 8
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.369Z - Closed a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.370Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.370Z - Handled HASH_GLOB. Handling time: 1. Response time: 28.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.371Z - Established a connection. Number of open connections: 8
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.371Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.371Z - Handled HASH_GLOB. Handling time: 3. Response time: 22.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.377Z - [REQUEST]: Responding to the client. handleHashGlob
Time for 'total for sendMessageToDaemon()' 42.14730000000009
Time for 'deserialize daemon response' 0.17160000000012587
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.385Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.386Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.387Z - Handled HASH_GLOB. Handling time: 13. Response time: 19.
Time for 'nx/js/dependencies-and-lockfile:createNodes' 83.67380000000003
Time for 'total for sendMessageToDaemon()' 59.33209999999963
Time for 'deserialize daemon response' 0.4519000000000233
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.425Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.425Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.425Z - Handled HASH_GLOB. Handling time: 2. Response time: 48.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.426Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.426Z - Handled HASH_GLOB. Handling time: 3. Response time: 41.
Time for '@nx/jest/plugin:createNodes' 140.3204999999998
Time for 'total for sendMessageToDaemon()' 98.2518
Time for 'deserialize daemon response' 0.07760000000007494
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.434Z - [REQUEST]: Responding to the client. handleHashGlob
Time for '@nx/expo/plugin:createNodes' 148.08260000000018
Time for 'total for sendMessageToDaemon()' 44.714100000000144
Time for 'deserialize daemon response' 0.059900000000197906
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.446Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.447Z - Handled HASH_GLOB. Handling time: 34. Response time: 22.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.448Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:32.448Z - Handled HASH_GLOB. Handling time: 5. Response time: 14.
Time for '@nx/js/typescript:createNodes' 688.1109000000001
Time for 'total for sendMessageToDaemon()' 31.192399999999907
Time for 'deserialize daemon response' 0.05180000000018481
Time for '@nx/webpack/plugin:createNodes' 2294.0295
Time for 'total for sendMessageToDaemon()' 52.10060000000021
Time for 'deserialize daemon response' 0.08539999999993597
Time for 'total for sendMessageToDaemon()' 36.86519999999973
Time for 'deserialize daemon response' 0.09420000000000073
(node:10560) [DEP0180] DeprecationWarning: fs.Stats constructor is deprecated.
(Use `node --trace-deprecation ...` to show where the warning was created)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:35.262Z - [REQUEST]: Responding to the client. handleNxWorkspaceFiles
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:35.263Z - Done responding to the client handleNxWorkspaceFiles
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:35.264Z - Handled GET_FILES_IN_DIRECTORY. Handling time: 0. Response time: 2.
Time for '@nx/playwright/plugin:createNodes' 2994.7652000000003
Time for 'total for sendMessageToDaemon()' 14.774899999999434
Time for 'deserialize daemon response' 0.04519999999956781
Time for '@nx/eslint/plugin:createNodes' 3991.1038999999996
Time for '@nx/vite/plugin:createNodes' 5208.146000000001
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:37.589Z - Time taken for 'build-project-configs' 5267.519700000001ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:37.591Z - Time taken for '@nx/js/typescript:createDependencies' 21.380599999999504ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:37.596Z - Time taken for '@nx/vite/plugin:createDependencies' 21.07430000000022ms
Time for 'build typescript dependencies' 0.565799999999399
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:37.812Z - Wrote project graph cache to C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\.nx\workspace-data\project-graph.json
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:37.813Z - [SYNC]: collect registered sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:37.817Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:37.817Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:37.819Z - Time taken for 'total for creating and serializing project graph' 7740.889700000001ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:37.824Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:37.825Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 7741. Response time: 8.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:37.901Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:37.902Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:37.902Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:37.904Z - Time taken for 'total for creating and serializing project graph' 1.0142999999989115ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:37.907Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:37.907Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 5.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:37.929Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.478Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.479Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.480Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.487Z - [WATCHER]: 0 file(s) created or restored, 6 file(s) modified, 0 file(s) deleted
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.489Z - [WATCHER]: Processing file changes in outputs
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.490Z - Established a connection. Number of open connections: 9
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.490Z - Established a connection. Number of open connections: 10
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.491Z - Time taken for 'total for creating and serializing project graph' 1.6561000000001513ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.491Z - Closed a connection. Number of open connections: 9
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.492Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.493Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 13.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.496Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.498Z - Recomputing project graph because of 6 updated and 0 deleted files.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.502Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.502Z - [REQUEST]: static/favicon.ico,static/runtime.js,static/environment.js,static/styles.js,static/main.js,static/styles.css
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.502Z - [REQUEST]: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.566Z - Time taken for 'hash changed files from watcher' 3.763300000000527ms
Time for 'nx/core/project-json:createNodes' 0.8189999999995052
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.574Z - [REQUEST]: Responding to the client. handleHashMultiGlob
Time for '@nx/react/router-plugin:createNodes' 55.31960000000072
Time for 'total for sendMessageToDaemon()' 47.059400000000096
Time for 'deserialize daemon response' 0.05730000000039581
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.584Z - [REQUEST]: Responding to the client. handleGlob
Time for 'total for sendMessageToDaemon()' 35.74399999999878
Time for 'deserialize daemon response' 0.0698999999985972
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.590Z - [REQUEST]: Responding to the client. handleHashGlob
Time for '@nx/playwright/plugin:createNodes' 44.69809999999961
Time for 'total for sendMessageToDaemon()' 37.74459999999999
Time for 'deserialize daemon response' 0.04199999999946158
Time for 'nx/core/package-json:createNodes' 34.61230000000069
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.601Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.607Z - [REQUEST]: Responding to the client. handleHashGlob
Time for '@nx/vite/plugin:createNodes' 60.86110000000008
Time for 'total for sendMessageToDaemon()' 43.15110000000095
Time for 'deserialize daemon response' 0.08790000000044529
Time for '@nx/webpack/plugin:createNodes' 53.28199999999924
Time for 'total for sendMessageToDaemon()' 37.651199999998425
Time for 'deserialize daemon response' 0.0569999999988795
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.614Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.614Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.614Z - Handled HASH_GLOB. Handling time: 1. Response time: 40.
Time for 'nx/js/dependencies-and-lockfile:createNodes' 44.4408999999996
Time for 'total for sendMessageToDaemon()' 43.60190000000148
Time for 'deserialize daemon response' 0.05250000000160071
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.629Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.630Z - Done responding to the client handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.630Z - Handled GLOB. Handling time: 4. Response time: 46.
Time for '@nx/jest/plugin:createNodes' 80.76569999999992
Time for 'total for sendMessageToDaemon()' 53.787500000000364
Time for 'deserialize daemon response' 0.05019999999967695
Time for '@nx/js/typescript:createNodes' 122.92450000000099
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.654Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.655Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.655Z - Handled HASH_GLOB. Handling time: 3. Response time: 65.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.655Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.656Z - Handled HASH_GLOB. Handling time: 7. Response time: 55.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.656Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.656Z - Handled HASH_GLOB. Handling time: 2. Response time: 49.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.657Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.657Z - Handled HASH_GLOB. Handling time: 3. Response time: 43.
Time for 'total for sendMessageToDaemon()' 67.87950000000092
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.662Z - [REQUEST]: Responding to the client. handleHashGlob
Time for 'deserialize daemon response' 0.03870000000097207
Time for '@nx/expo/plugin:createNodes' 111.97890000000007
Time for 'total for sendMessageToDaemon()' 46.117400000001
Time for 'deserialize daemon response' 0.03800000000046566
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.676Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.676Z - Handled HASH_GLOB. Handling time: 7. Response time: 47.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.677Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.677Z - Handled HASH_GLOB. Handling time: 20. Response time: 23.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.678Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.679Z - Handled HASH_GLOB. Handling time: 2. Response time: 17.
Time for '@nx/eslint/plugin:createNodes' 242.52509999999893
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.842Z - Time taken for 'build-project-configs' 284.6100000000006ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.844Z - Time taken for '@nx/js/typescript:createDependencies' 32.48790000000008ms
Time for 'build typescript dependencies' 0.06799999999930151
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.933Z - Wrote project graph cache to C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\.nx\workspace-data\project-graph.json
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.933Z - [SYNC]: collect registered sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.935Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.935Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.935Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.935Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.937Z - Time taken for 'total for creating and serializing project graph' 439.3334999999988ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.980Z - [SYNC]: @nx/js:typescript-sync changes: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.981Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 1049.8886000000002ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.981Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:38.982Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 439. Response time: 47.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:39.149Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:39.155Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:39.156Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:39.156Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:39.158Z - Time taken for 'total for creating and serializing project graph' 1.238999999999578ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:39.197Z - [SYNC]: @nx/js:typescript-sync changes: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:39.198Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 47.92360000000008ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:39.199Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:39.199Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 43.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:40.181Z - [REQUEST]: Responding to the client. handleContextFileData
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:40.182Z - Done responding to the client handleContextFileData
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:40.182Z - Handled GET_CONTEXT_FILE_DATA. Handling time: 1. Response time: 1.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:40.749Z - Closed a connection. Number of open connections: 8
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:40.784Z - [WATCHER]: Processing file changes in outputs
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:40.788Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:40.906Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:40.906Z - [REQUEST]: dependency-graph.html,static/environment.js
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:40.906Z - [REQUEST]: 
Time for 'nx/core/project-json:createNodes' 0.5740999999998166
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:40.967Z - [REQUEST]: Responding to the client. handleHashMultiGlob
Time for '@nx/react/router-plugin:createNodes' 60.25720000000001
Time for 'total for sendMessageToDaemon()' 41.78469999999834
Time for 'deserialize daemon response' 0.0970999999990454
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:40.986Z - [REQUEST]: Responding to the client. handleGlob
Time for 'total for sendMessageToDaemon()' 48.72370000000046
Time for 'deserialize daemon response' 0.05470000000059372
Time for 'nx/core/package-json:createNodes' 25.140299999999115
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:40.993Z - [REQUEST]: Responding to the client. handleHashGlob
Time for '@nx/playwright/plugin:createNodes' 58.4434999999994
Time for 'total for sendMessageToDaemon()' 51.36189999999988
Time for 'deserialize daemon response' 0.041800000000876025
Time for 'nx/js/dependencies-and-lockfile:createNodes' 41.685999999999694
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.028Z - [REQUEST]: Responding to the client. handleHashMultiGlob
Time for '@nx/vite/plugin:createNodes' 100.66129999999976
Time for 'total for sendMessageToDaemon()' 82.75580000000082
Time for 'deserialize daemon response' 0.04860000000007858
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.046Z - [REQUEST]: Responding to the client. handleHashMultiGlob
Time for '@nx/js/typescript:createNodes' 128.01040000000103
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.054Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.054Z - Time taken for 'hash changed files from watcher' 7.597700000000259ms
Time for '@nx/jest/plugin:createNodes' 111.34940000000097
Time for 'total for sendMessageToDaemon()' 93.25740000000042
Time for 'deserialize daemon response' 0.03660000000127184
Time for 'total for sendMessageToDaemon()' 90.95160000000033
Time for 'deserialize daemon response' 0.060500000001411536
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.059Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.059Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.059Z - Handled HASH_GLOB. Handling time: 1. Response time: 92.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.060Z - Done responding to the client handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.060Z - Handled GLOB. Handling time: 14. Response time: 74.
Time for '@nx/webpack/plugin:createNodes' 108.09770000000026
Time for 'total for sendMessageToDaemon()' 97.439699999999
Time for 'deserialize daemon response' 0.059299999998984276
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.074Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.075Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.076Z - Handled HASH_GLOB. Handling time: 3. Response time: 83.
Time for 'total for sendMessageToDaemon()' 86.53280000000086
Time for 'deserialize daemon response' 0.030000000000654836
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.085Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.085Z - Handled HASH_GLOB. Handling time: 32. Response time: 57.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.086Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.086Z - Handled HASH_GLOB. Handling time: 12. Response time: 40.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.087Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.087Z - Handled HASH_GLOB. Handling time: 4. Response time: 33.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.091Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.092Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.092Z - Handled HASH_GLOB. Handling time: 2. Response time: 33.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.093Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.093Z - Handled HASH_GLOB. Handling time: 12. Response time: 19.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.100Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.101Z - Handled HASH_GLOB. Handling time: 1. Response time: 9.
Time for '@nx/expo/plugin:createNodes' 149.35089999999946
Time for 'total for sendMessageToDaemon()' 36.272899999999936
Time for 'deserialize daemon response' 0.036699999998745625
Time for '@nx/eslint/plugin:createNodes' 251.5694999999996
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.226Z - Time taken for 'build-project-configs' 277.8129000000008ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.228Z - Time taken for '@nx/js/typescript:createDependencies' 22.44759999999951ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.229Z - Time taken for '@nx/vite/plugin:createDependencies' 17.155399999999645ms
Time for 'build typescript dependencies' 0.042099999998754356
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.328Z - Wrote project graph cache to C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\.nx\workspace-data\project-graph.json
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.328Z - [SYNC]: collect registered sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.330Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.331Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.331Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.331Z - Time taken for 'total execution time for createProjectGraph()' 87.57250000000022ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.731Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.736Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.736Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.737Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.738Z - Time taken for 'total for creating and serializing project graph' 0.8523000000004686ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.770Z - [SYNC]: @nx/js:typescript-sync changes: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.771Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 39.2531999999992ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.771Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:15:41.771Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 34.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:50.746Z - [WATCHER]: Processing file changes in outputs
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:50.751Z - [WATCHER]: .github/workflows/pr-checks.yml was modified
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:50.866Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:50.866Z - [REQUEST]: .github/workflows/pr-checks.yml
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:50.866Z - [REQUEST]: 
Time for 'nx/core/project-json:createNodes' 0.6875
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:50.950Z - [REQUEST]: Responding to the client. handleHashMultiGlob
Time for '@nx/react/router-plugin:createNodes' 49.631299999993644
Time for 'total for sendMessageToDaemon()' 42.31099999998696
Time for 'deserialize daemon response' 0.031099999992875382
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:50.958Z - [REQUEST]: Responding to the client. handleGlob
Time for 'total for sendMessageToDaemon()' 24.385800000003655
Time for 'deserialize daemon response' 0.04809999999997672
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:50.963Z - [REQUEST]: Responding to the client. handleHashGlob
Time for '@nx/playwright/plugin:createNodes' 32.73500000000058
Time for 'total for sendMessageToDaemon()' 25.45669999999518
Time for 'deserialize daemon response' 0.0400999999983469
Time for 'nx/core/package-json:createNodes' 28.816200000001118
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:50.973Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:50.973Z - Time taken for 'hash changed files from watcher' 0.8390000000072177ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:50.980Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:50.981Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:50.981Z - Handled HASH_GLOB. Handling time: 0. Response time: 31.
Time for '@nx/vite/plugin:createNodes' 48.585599999991246
Time for 'total for sendMessageToDaemon()' 31.963000000003376
Time for 'deserialize daemon response' 0.037599999996018596
Time for '@nx/webpack/plugin:createNodes' 42.698199999998906
Time for 'total for sendMessageToDaemon()' 31.772500000006403
Time for 'deserialize daemon response' 0.04170000000158325
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:50.987Z - [REQUEST]: Responding to the client. handleHashGlob
Time for 'nx/js/dependencies-and-lockfile:createNodes' 34.27820000000065
Time for 'total for sendMessageToDaemon()' 36.51219999999739
Time for 'deserialize daemon response' 0.04469999999855645
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:51.003Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:51.003Z - Done responding to the client handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:51.003Z - Handled GLOB. Handling time: 3. Response time: 45.
Time for '@nx/jest/plugin:createNodes' 69.30259999999544
Time for 'total for sendMessageToDaemon()' 48.01940000000468
Time for 'deserialize daemon response' 0.036699999996926636
Time for '@nx/js/typescript:createNodes' 112.93150000000605
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:51.036Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:51.036Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:51.036Z - Handled HASH_GLOB. Handling time: 2. Response time: 73.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:51.037Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:51.037Z - Handled HASH_GLOB. Handling time: 4. Response time: 64.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:51.038Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:51.038Z - Handled HASH_GLOB. Handling time: 3. Response time: 58.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:51.038Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:51.038Z - Handled HASH_GLOB. Handling time: 2. Response time: 51.
Time for 'total for sendMessageToDaemon()' 76.5518000000011
Time for 'deserialize daemon response' 0.03919999999925494
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:51.052Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:51.052Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:51.052Z - Handled HASH_GLOB. Handling time: 12. Response time: 49.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:51.053Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:51.053Z - Handled HASH_GLOB. Handling time: 30. Response time: 17.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:51.055Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:51.055Z - Handled HASH_GLOB. Handling time: 2. Response time: 3.
Time for '@nx/expo/plugin:createNodes' 117.99989999999525
Time for 'total for sendMessageToDaemon()' 63.592499999998836
Time for 'deserialize daemon response' 0.04820000000472646
Time for '@nx/eslint/plugin:createNodes' 216.69959999999264
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:51.190Z - Time taken for 'build-project-configs' 257.71809999999823ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:51.191Z - Time taken for '@nx/js/typescript:createDependencies' 25.21909999998752ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:51.196Z - Time taken for '@nx/vite/plugin:createDependencies' 22.424900000012713ms
Time for 'build typescript dependencies' 0.07730000000447035
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:51.306Z - Wrote project graph cache to C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\.nx\workspace-data\project-graph.json
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:51.306Z - [SYNC]: collect registered sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:51.308Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:51.308Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:51.308Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:51.308Z - Time taken for 'total execution time for createProjectGraph()' 106.74469999999565ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:52.120Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:52.124Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:52.125Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:52.125Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:52.126Z - Time taken for 'total for creating and serializing project graph' 0.8619999999937136ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:52.157Z - [SYNC]: @nx/js:typescript-sync changes: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:52.158Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 37.48549999999523ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:52.158Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:16:52.158Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 33.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.463Z - [WATCHER]: Processing file changes in outputs
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.466Z - [WATCHER]: firebase.json was modified
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.570Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.570Z - [REQUEST]: firebase.json
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.570Z - [REQUEST]: 
Time for 'nx/core/project-json:createNodes' 0.1448999999993248
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.627Z - [REQUEST]: Responding to the client. handleHashMultiGlob
Time for '@nx/react/router-plugin:createNodes' 40.4884999999922
Time for 'total for sendMessageToDaemon()' 26.489300000001094
Time for 'deserialize daemon response' 0.04099999999743886
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.641Z - [REQUEST]: Responding to the client. handleGlob
Time for 'total for sendMessageToDaemon()' 30.292300000000978
Time for 'deserialize daemon response' 0.08139999999548309
Time for 'nx/core/package-json:createNodes' 32.23359999999229
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.667Z - [REQUEST]: Responding to the client. handleHashGlob
Time for '@nx/playwright/plugin:createNodes' 59.23300000000745
Time for 'total for sendMessageToDaemon()' 53.247400000007474
Time for 'deserialize daemon response' 0.10330000000249129
Time for 'nx/js/dependencies-and-lockfile:createNodes' 41.68790000000445
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.684Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.685Z - Time taken for 'hash changed files from watcher' 0.8346999999921536ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.686Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.686Z - Handled HASH_GLOB. Handling time: 0. Response time: 59.
Time for '@nx/vite/plugin:createNodes' 79.1244000000006
Time for 'total for sendMessageToDaemon()' 66.68310000000929
Time for 'deserialize daemon response' 0.033000000010360964
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.691Z - [REQUEST]: Responding to the client. handleHashGlob
Time for 'total for sendMessageToDaemon()' 63.911100000012084
Time for 'deserialize daemon response' 0.046900000001187436
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.705Z - [REQUEST]: Responding to the client. handleHashMultiGlob
Time for '@nx/jest/plugin:createNodes' 92.7502999999997
Time for 'total for sendMessageToDaemon()' 75.3122000000003
Time for 'deserialize daemon response' 0.04320000000006985
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.713Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.713Z - Done responding to the client handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.713Z - Handled GLOB. Handling time: 6. Response time: 73.
Time for '@nx/webpack/plugin:createNodes' 97.78069999998843
Time for 'total for sendMessageToDaemon()' 79.87970000000496
Time for 'deserialize daemon response' 0.03179999999701977
Time for '@nx/js/typescript:createNodes' 130.16249999999127
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.731Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.731Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.731Z - Handled HASH_GLOB. Handling time: 12. Response time: 64.
Time for 'total for sendMessageToDaemon()' 88.43579999999201
Time for 'deserialize daemon response' 0.030299999998533167
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.739Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.740Z - Handled HASH_GLOB. Handling time: 13. Response time: 56.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.741Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.741Z - Handled HASH_GLOB. Handling time: 3. Response time: 50.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.747Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.748Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.748Z - Handled HASH_GLOB. Handling time: 11. Response time: 43.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.750Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.750Z - Handled HASH_GLOB. Handling time: 5. Response time: 37.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.752Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.752Z - Handled HASH_GLOB. Handling time: 15. Response time: 21.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.754Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.754Z - Handled HASH_GLOB. Handling time: 2. Response time: 7.
Time for '@nx/expo/plugin:createNodes' 137.73540000000503
Time for 'total for sendMessageToDaemon()' 55.183700000008685
Time for 'deserialize daemon response' 0.03500000000349246
Time for '@nx/eslint/plugin:createNodes' 234.85580000000482
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.883Z - Time taken for 'build-project-configs' 261.3110000000015ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.887Z - Time taken for '@nx/js/typescript:createDependencies' 23.819000000003143ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.888Z - Time taken for '@nx/vite/plugin:createDependencies' 19.137099999992643ms
Time for 'build typescript dependencies' 0.043900000004214235
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.988Z - Wrote project graph cache to C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\.nx\workspace-data\project-graph.json
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.989Z - [SYNC]: collect registered sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.990Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.991Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.991Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:09.991Z - Time taken for 'total execution time for createProjectGraph()' 96.05799999998999ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:11.596Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:11.602Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:11.603Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:11.603Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:11.605Z - Time taken for 'total for creating and serializing project graph' 1.018899999995483ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:11.639Z - [SYNC]: @nx/js:typescript-sync changes: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:11.639Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 42.15929999999935ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:11.639Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:11.639Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 36.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.428Z - [WATCHER]: Processing file changes in outputs
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.433Z - [WATCHER]: firestore.rules was modified
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.549Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.549Z - [REQUEST]: firestore.rules
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.550Z - [REQUEST]: 
Time for 'nx/core/project-json:createNodes' 0.56600000000617
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.605Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.616Z - [REQUEST]: Responding to the client. handleGlob
Time for 'total for sendMessageToDaemon()' 23.301699999996345
Time for 'deserialize daemon response' 0.05649999999150168
Time for '@nx/react/router-plugin:createNodes' 50.032600000005914
Time for 'total for sendMessageToDaemon()' 31.97830000000249
Time for 'deserialize daemon response' 0.0595000000030268
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.627Z - [REQUEST]: Responding to the client. handleHashGlob
Time for 'nx/core/package-json:createNodes' 24.161800000001676
Time for '@nx/playwright/plugin:createNodes' 41.63610000000335
Time for 'total for sendMessageToDaemon()' 29.286800000001676
Time for 'deserialize daemon response' 0.036300000007031485
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.641Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.641Z - Time taken for 'hash changed files from watcher' 0.6322999999974854ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.642Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.642Z - Handled HASH_GLOB. Handling time: 0. Response time: 37.
Time for 'nx/js/dependencies-and-lockfile:createNodes' 34.443299999999
Time for '@nx/vite/plugin:createNodes' 53.64619999998831
Time for 'total for sendMessageToDaemon()' 40.20899999998801
Time for 'deserialize daemon response' 0.042599999986123294
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.650Z - [REQUEST]: Responding to the client. handleHashGlob
Time for '@nx/webpack/plugin:createNodes' 52.21400000000722
Time for 'total for sendMessageToDaemon()' 42.830000000001746
Time for 'deserialize daemon response' 0.11159999998926651
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.655Z - [REQUEST]: Responding to the client. handleHashGlob
Time for 'total for sendMessageToDaemon()' 46.71329999998852
Time for 'deserialize daemon response' 0.034899999998742715
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.669Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.669Z - Done responding to the client handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.669Z - Handled GLOB. Handling time: 7. Response time: 53.
Time for '@nx/jest/plugin:createNodes' 76.02090000000317
Time for 'total for sendMessageToDaemon()' 54.468800000002375
Time for 'deserialize daemon response' 0.035799999997834675
Time for '@nx/js/typescript:createNodes' 104.00389999999607
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.699Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.700Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.700Z - Handled HASH_GLOB. Handling time: 7. Response time: 73.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.701Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.701Z - Handled HASH_GLOB. Handling time: 8. Response time: 60.
Time for 'total for sendMessageToDaemon()' 82.16899999999441
Time for 'deserialize daemon response' 0.03569999999308493
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.721Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.721Z - Handled HASH_GLOB. Handling time: 4. Response time: 71.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.722Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.722Z - Handled HASH_GLOB. Handling time: 1. Response time: 67.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.727Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.728Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.728Z - Handled HASH_GLOB. Handling time: 11. Response time: 59.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.730Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.730Z - Handled HASH_GLOB. Handling time: 27. Response time: 31.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.732Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.732Z - Handled HASH_GLOB. Handling time: 1. Response time: 5.
Time for '@nx/expo/plugin:createNodes' 136.28979999999865
Time for 'total for sendMessageToDaemon()' 71.6875999999902
Time for 'deserialize daemon response' 0.05009999999310821
Time for '@nx/eslint/plugin:createNodes' 214.13060000000405
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.849Z - Time taken for 'build-project-configs' 246.03680000000168ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.852Z - Time taken for '@nx/js/typescript:createDependencies' 27.918499999999767ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.857Z - Time taken for '@nx/vite/plugin:createDependencies' 25.691700000010314ms
Time for 'build typescript dependencies' 0.05400000000372529
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.951Z - Wrote project graph cache to C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\.nx\workspace-data\project-graph.json
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.952Z - [SYNC]: collect registered sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.953Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.954Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.954Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:17.954Z - Time taken for 'total execution time for createProjectGraph()' 98.11149999999907ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:21.162Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:21.167Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:21.168Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:21.168Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:21.170Z - Time taken for 'total for creating and serializing project graph' 0.8932999999960884ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:21.201Z - [SYNC]: @nx/js:typescript-sync changes: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:21.201Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 38.778900000004796ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:21.201Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:21.202Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 34.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:22.952Z - [WATCHER]: .firebaserc was modified
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:22.953Z - [WATCHER]: Processing file changes in outputs
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.061Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.063Z - [REQUEST]: .firebaserc
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.063Z - [REQUEST]: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.140Z - [REQUEST]: Responding to the client. handleHashMultiGlob
Time for 'nx/core/project-json:createNodes' 0.33150000000023283
Time for '@nx/react/router-plugin:createNodes' 43.28740000000107
Time for 'total for sendMessageToDaemon()' 35.047400000010384
Time for 'deserialize daemon response' 0.16770000000542495
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.152Z - [REQUEST]: Responding to the client. handleGlob
Time for 'total for sendMessageToDaemon()' 28.850500000000466
Time for 'deserialize daemon response' 0.05779999999504071
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.157Z - [REQUEST]: Responding to the client. handleHashGlob
Time for '@nx/playwright/plugin:createNodes' 35.742700000002515
Time for 'total for sendMessageToDaemon()' 29.24409999999625
Time for 'deserialize daemon response' 0.04290000000037253
Time for 'nx/core/package-json:createNodes' 26.452999999994063
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.168Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.173Z - [REQUEST]: Responding to the client. handleHashGlob
Time for 'nx/js/dependencies-and-lockfile:createNodes' 27.049700000003213
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.174Z - Time taken for 'hash changed files from watcher' 1.4373999999952503ms
Time for 'total for sendMessageToDaemon()' 37.63069999999425
Time for 'deserialize daemon response' 0.03919999999925494
Time for '@nx/vite/plugin:createNodes' 52.51720000000205
Time for 'total for sendMessageToDaemon()' 37.04759999999078
Time for 'deserialize daemon response' 0.051800000001094304
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.180Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.180Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.180Z - Handled HASH_GLOB. Handling time: 0. Response time: 40.
Time for '@nx/webpack/plugin:createNodes' 49.958599999998114
Time for 'total for sendMessageToDaemon()' 40.45650000000023
Time for 'deserialize daemon response' 0.04370000000926666
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.189Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.189Z - Done responding to the client handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.189Z - Handled GLOB. Handling time: 7. Response time: 37.
Time for '@nx/jest/plugin:createNodes' 63.54690000000119
Time for 'total for sendMessageToDaemon()' 43.510699999998906
Time for 'deserialize daemon response' 0.04589999999734573
Time for '@nx/js/typescript:createNodes' 112.4207000000024
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.240Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.240Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.240Z - Handled HASH_GLOB. Handling time: 2. Response time: 83.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.241Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.241Z - Handled HASH_GLOB. Handling time: 9. Response time: 73.
Time for 'total for sendMessageToDaemon()' 86.79970000000321
Time for 'deserialize daemon response' 0.04550000000745058
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.251Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.251Z - Handled HASH_GLOB. Handling time: 2. Response time: 78.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.256Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.257Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.257Z - Handled HASH_GLOB. Handling time: 3. Response time: 77.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.257Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.257Z - Handled HASH_GLOB. Handling time: 6. Response time: 68.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.259Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.259Z - Handled HASH_GLOB. Handling time: 48. Response time: 19.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.260Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.260Z - Handled HASH_GLOB. Handling time: 1. Response time: 4.
Time for '@nx/expo/plugin:createNodes' 132.44150000000081
Time for 'total for sendMessageToDaemon()' 81.92280000000028
Time for 'deserialize daemon response' 0.02920000000449363
Time for '@nx/eslint/plugin:createNodes' 228.64359999999579
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.410Z - Time taken for 'build-project-configs' 260.9850999999908ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.412Z - Time taken for '@nx/js/typescript:createDependencies' 41.52430000000459ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.415Z - Time taken for '@nx/vite/plugin:createDependencies' 36.098299999997835ms
Time for 'build typescript dependencies' 0.04839999999967404
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.520Z - Wrote project graph cache to C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\.nx\workspace-data\project-graph.json
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.520Z - [SYNC]: collect registered sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.522Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.522Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.522Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:23.523Z - Time taken for 'total execution time for createProjectGraph()' 113.38460000000487ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:29.924Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:29.931Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:29.932Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:29.932Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:29.934Z - Time taken for 'total for creating and serializing project graph' 1.0457000000023982ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:29.960Z - [SYNC]: @nx/js:typescript-sync changes: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:29.961Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 36.267399999997ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:29.961Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:29.961Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 29.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:40.913Z - [WATCHER]: Processing file changes in outputs
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:40.917Z - [WATCHER]: .github/workflows/deploy-preview.yml was modified
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.031Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.032Z - [REQUEST]: .github/workflows/deploy-preview.yml
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.032Z - [REQUEST]: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.084Z - [REQUEST]: Responding to the client. handleHashMultiGlob
Time for 'nx/core/project-json:createNodes' 0.6732999999949243
Time for '@nx/react/router-plugin:createNodes' 35.86789999999746
Time for 'total for sendMessageToDaemon()' 29.214899999991758
Time for 'deserialize daemon response' 0.05749999999534339
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.097Z - [REQUEST]: Responding to the client. handleGlob
Time for 'total for sendMessageToDaemon()' 29.314200000007986
Time for 'deserialize daemon response' 0.12989999999990687
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.103Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.103Z - Time taken for 'hash changed files from watcher' 0.693200000008801ms
Time for 'nx/core/package-json:createNodes' 24.032600000005914
Time for '@nx/playwright/plugin:createNodes' 36.59459999999672
Time for 'total for sendMessageToDaemon()' 28.51940000000468
Time for 'deserialize daemon response' 0.03659999999217689
Time for 'nx/js/dependencies-and-lockfile:createNodes' 25.83880000001227
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.121Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.122Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.122Z - Handled HASH_GLOB. Handling time: 0. Response time: 38.
Time for '@nx/vite/plugin:createNodes' 57.94650000000547
Time for 'total for sendMessageToDaemon()' 41.63779999999679
Time for 'deserialize daemon response' 0.044200000003911555
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.131Z - [REQUEST]: Responding to the client. handleHashGlob
Time for 'total for sendMessageToDaemon()' 43.212100000004284
Time for 'deserialize daemon response' 0.03620000000228174
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.139Z - [REQUEST]: Responding to the client. handleHashGlob
Time for '@nx/webpack/plugin:createNodes' 62.35689999999886
Time for 'total for sendMessageToDaemon()' 49.39749999999185
Time for 'deserialize daemon response' 0.03919999999925494
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.154Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.154Z - Done responding to the client handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.155Z - Handled GLOB. Handling time: 6. Response time: 57.
Time for '@nx/jest/plugin:createNodes' 83.3076000000001
Time for 'total for sendMessageToDaemon()' 58.170899999997346
Time for 'deserialize daemon response' 0.03979999999864958
Time for '@nx/js/typescript:createNodes' 119.04839999999967
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.180Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.180Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.180Z - Handled HASH_GLOB. Handling time: 2. Response time: 77.
Time for 'total for sendMessageToDaemon()' 80.69750000000931
Time for 'deserialize daemon response' 0.04610000000684522
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.192Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.192Z - Handled HASH_GLOB. Handling time: 14. Response time: 71.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.192Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.192Z - Handled HASH_GLOB. Handling time: 6. Response time: 61.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.196Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.196Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.196Z - Handled HASH_GLOB. Handling time: 5. Response time: 57.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.197Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.197Z - Handled HASH_GLOB. Handling time: 12. Response time: 43.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.198Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.198Z - Handled HASH_GLOB. Handling time: 23. Response time: 18.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.199Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.199Z - Handled HASH_GLOB. Handling time: 2. Response time: 3.
Time for '@nx/expo/plugin:createNodes' 126.85670000000391
Time for 'total for sendMessageToDaemon()' 64.18589999999676
Time for 'deserialize daemon response' 0.033499999990453944
Time for '@nx/eslint/plugin:createNodes' 223.89459999999963
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.328Z - Time taken for 'build-project-configs' 249.3179999999993ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.333Z - Time taken for '@nx/js/typescript:createDependencies' 28.60439999998198ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.334Z - Time taken for '@nx/vite/plugin:createDependencies' 23.499800000019604ms
Time for 'build typescript dependencies' 0.03949999998440035
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.424Z - Wrote project graph cache to C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\.nx\workspace-data\project-graph.json
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.424Z - [SYNC]: collect registered sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.426Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.426Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.427Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:41.427Z - Time taken for 'total execution time for createProjectGraph()' 82.94190000000526ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:47.838Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:47.842Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:47.843Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:47.843Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:47.844Z - Time taken for 'total for creating and serializing project graph' 0.8577999999979511ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:47.872Z - [SYNC]: @nx/js:typescript-sync changes: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:47.872Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 34.35719999999856ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:47.873Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:47.873Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 30.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:53.880Z - [WATCHER]: Processing file changes in outputs
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:53.885Z - [WATCHER]: .github/workflows/build-mobile.yml was modified
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:53.992Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:53.992Z - [REQUEST]: .github/workflows/build-mobile.yml
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:53.992Z - [REQUEST]: 
Time for 'nx/core/project-json:createNodes' 0.2146000000066124
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:54.054Z - [REQUEST]: Responding to the client. handleHashMultiGlob
Time for '@nx/react/router-plugin:createNodes' 36.88509999998496
Time for 'total for sendMessageToDaemon()' 28.253800000005867
Time for 'deserialize daemon response' 0.04799999998067506
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:54.061Z - [REQUEST]: Responding to the client. handleGlob
Time for 'total for sendMessageToDaemon()' 24.518800000019837
Time for 'deserialize daemon response' 0.04370000000926666
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:54.069Z - [REQUEST]: Responding to the client. handleHashGlob
Time for 'nx/core/package-json:createNodes' 24.346099999995204
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:54.073Z - Time taken for 'hash changed files from watcher' 0.6357000000134576ms
Time for '@nx/playwright/plugin:createNodes' 41.86180000001332
Time for 'total for sendMessageToDaemon()' 30.490200000000186
Time for 'deserialize daemon response' 0.0348000000230968
Time for 'nx/js/dependencies-and-lockfile:createNodes' 29.076399999990826
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:54.088Z - [REQUEST]: Responding to the client. handleHashGlob
Time for 'total for sendMessageToDaemon()' 38.70240000000922
Time for 'deserialize daemon response' 0.0328999999910593
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:54.091Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:54.092Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:54.092Z - Handled HASH_GLOB. Handling time: 0. Response time: 38.
Time for '@nx/webpack/plugin:createNodes' 52.33729999998468
Time for 'total for sendMessageToDaemon()' 38.05139999999665
Time for 'deserialize daemon response' 0.035099999979138374
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:54.110Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:54.110Z - Done responding to the client handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:54.111Z - Handled GLOB. Handling time: 3. Response time: 50.
Time for '@nx/jest/plugin:createNodes' 75.0789999999979
Time for 'total for sendMessageToDaemon()' 54.98709999999846
Time for 'deserialize daemon response' 0.03740000000107102
Time for '@nx/js/typescript:createNodes' 106.19500000000698
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:54.137Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:54.141Z - [REQUEST]: Responding to the client. handleHashMultiGlob
Time for 'total for sendMessageToDaemon()' 75.00750000000698
Time for 'deserialize daemon response' 0.03320000000530854
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:54.142Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:54.142Z - Handled HASH_GLOB. Handling time: 4. Response time: 73.
Time for '@nx/vite/plugin:createNodes' 111.8416000000143
Time for 'total for sendMessageToDaemon()' 75.57709999999497
Time for 'deserialize daemon response' 0.035499999998137355
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:54.156Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:54.156Z - Handled HASH_GLOB. Handling time: 10. Response time: 68.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:54.160Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:54.161Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:54.161Z - Handled HASH_GLOB. Handling time: 1. Response time: 70.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:54.161Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:54.161Z - Handled HASH_GLOB. Handling time: 14. Response time: 51.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:54.163Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:54.163Z - Handled HASH_GLOB. Handling time: 23. Response time: 26.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:54.163Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:54.163Z - Handled HASH_GLOB. Handling time: 1. Response time: 22.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:54.166Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:54.167Z - Handled HASH_GLOB. Handling time: 1. Response time: 7.
Time for '@nx/expo/plugin:createNodes' 126.13790000000154
Time for 'total for sendMessageToDaemon()' 72.0692000000272
Time for 'deserialize daemon response' 0.03820000000996515
Time for '@nx/eslint/plugin:createNodes' 217.95079999999143
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:54.294Z - Time taken for 'build-project-configs' 241.0622999999905ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:54.296Z - Time taken for '@nx/js/typescript:createDependencies' 28.283600000024308ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:54.300Z - Time taken for '@nx/vite/plugin:createDependencies' 25.068499999993946ms
Time for 'build typescript dependencies' 0.060200000007171184
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:54.383Z - Wrote project graph cache to C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\.nx\workspace-data\project-graph.json
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:54.384Z - [SYNC]: collect registered sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:54.386Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:54.386Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:54.386Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.3.9 Daemon Server] - 2025-07-30T12:17:54.386Z - Time taken for 'total execution time for createProjectGraph()' 84.64749999999185ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:00.795Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:00.801Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:00.801Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:00.802Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:00.803Z - Time taken for 'total for creating and serializing project graph' 0.9187000000092667ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:00.838Z - [SYNC]: @nx/js:typescript-sync changes: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:00.838Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 42.98640000002342ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:00.839Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:00.839Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 37.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:06.613Z - [WATCHER]: Processing file changes in outputs
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:06.618Z - [WATCHER]: .github/workflows/deploy-production.yml was modified
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:06.733Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:06.733Z - [REQUEST]: .github/workflows/deploy-production.yml
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:06.733Z - [REQUEST]: 
Time for 'nx/core/project-json:createNodes' 0.17090000002644956
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:06.796Z - [REQUEST]: Responding to the client. handleHashMultiGlob
Time for 'nx/core/package-json:createNodes' 20.924300000013318
Time for '@nx/react/router-plugin:createNodes' 60.37010000000009
Time for 'total for sendMessageToDaemon()' 31.293900000018766
Time for 'deserialize daemon response' 0.04469999999855645
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:06.825Z - [REQUEST]: Responding to the client. handleGlob
Time for 'total for sendMessageToDaemon()' 48.31170000002021
Time for 'deserialize daemon response' 0.04810000001452863
Time for 'nx/js/dependencies-and-lockfile:createNodes' 38.923299999994924
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:06.837Z - [REQUEST]: Responding to the client. handleHashGlob
Time for '@nx/playwright/plugin:createNodes' 62.45670000000973
Time for 'total for sendMessageToDaemon()' 56.18530000001192
Time for 'deserialize daemon response' 0.06280000001424924
Time for '@nx/js/typescript:createNodes' 88.37400000001071
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:06.852Z - [REQUEST]: Responding to the client. handleHashMultiGlob
Time for '@nx/vite/plugin:createNodes' 79.60659999999916
Time for 'total for sendMessageToDaemon()' 67.62630000000354
Time for 'deserialize daemon response' 0.036900000006426126
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:06.859Z - [REQUEST]: Responding to the client. handleHashGlob
Time for '@nx/webpack/plugin:createNodes' 79.38260000001173
Time for 'total for sendMessageToDaemon()' 70.37049999998999
Time for 'deserialize daemon response' 0.03839999999036081
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:06.866Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:06.866Z - Time taken for 'hash changed files from watcher' 0.8847999999998137ms
Time for 'total for sendMessageToDaemon()' 76.27069999999367
Time for 'deserialize daemon response' 0.032700000010663643
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:06.874Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:06.875Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:06.875Z - Handled HASH_GLOB. Handling time: 0. Response time: 80.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:06.875Z - Done responding to the client handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:06.876Z - Handled GLOB. Handling time: 15. Response time: 51.
Time for '@nx/jest/plugin:createNodes' 96.26000000000931
Time for 'total for sendMessageToDaemon()' 80.63169999999809
Time for 'deserialize daemon response' 0.0298999999940861
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:06.885Z - [REQUEST]: Responding to the client. handleHashMultiGlob
Time for 'total for sendMessageToDaemon()' 59.22959999999148
Time for 'deserialize daemon response' 0.036300000007031485
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:06.895Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:06.895Z - Handled HASH_GLOB. Handling time: 7. Response time: 58.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:06.897Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:06.897Z - Handled HASH_GLOB. Handling time: 12. Response time: 45.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:06.898Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:06.898Z - Handled HASH_GLOB. Handling time: 4. Response time: 39.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:06.898Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:06.898Z - Handled HASH_GLOB. Handling time: 4. Response time: 32.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:06.901Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:06.901Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:06.901Z - Handled HASH_GLOB. Handling time: 4. Response time: 27.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:06.902Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:06.902Z - Handled HASH_GLOB. Handling time: 7. Response time: 17.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:06.904Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:06.905Z - Handled HASH_GLOB. Handling time: 1. Response time: 4.
Time for '@nx/expo/plugin:createNodes' 125.64900000000489
Time for 'total for sendMessageToDaemon()' 34.66349999999511
Time for 'deserialize daemon response' 0.03250000000116415
Time for '@nx/eslint/plugin:createNodes' 213.15770000001066
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:07.031Z - Time taken for 'build-project-configs' 240.19940000001225ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:07.033Z - Time taken for '@nx/js/typescript:createDependencies' 26.68890000000829ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:07.034Z - Time taken for '@nx/vite/plugin:createDependencies' 20.000299999985145ms
Time for 'build typescript dependencies' 0.052899999980581924
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:07.124Z - Wrote project graph cache to C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\.nx\workspace-data\project-graph.json
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:07.124Z - [SYNC]: collect registered sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:07.126Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:07.126Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:07.126Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:07.126Z - Time taken for 'total execution time for createProjectGraph()' 88.40929999999935ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:13.527Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:13.532Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:13.532Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:13.532Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:13.534Z - Time taken for 'total for creating and serializing project graph' 0.8136999999987893ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:13.562Z - [SYNC]: @nx/js:typescript-sync changes: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:13.563Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 35.33299999998417ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:13.564Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:13.565Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 33.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:18.786Z - [WATCHER]: Processing file changes in outputs
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:18.790Z - [WATCHER]: nx.json was modified
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:18.903Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:18.903Z - [REQUEST]: nx.json
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:18.903Z - [REQUEST]: 
Time for 'nx/core/project-json:createNodes' 0.16640000001643784
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:18.976Z - [REQUEST]: Responding to the client. handleHashMultiGlob
Time for '@nx/react/router-plugin:createNodes' 65.09750000000349
Time for 'total for sendMessageToDaemon()' 53.00500000000466
Time for 'deserialize daemon response' 0.03969999999389984
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:18.986Z - [REQUEST]: Responding to the client. handleGlob
Time for 'total for sendMessageToDaemon()' 52.03880000000936
Time for 'deserialize daemon response' 0.051699999981792644
Time for 'nx/core/package-json:createNodes' 23.47689999997965
Time for 'nx/js/dependencies-and-lockfile:createNodes' 36.7670999999973
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:19.002Z - [REQUEST]: Responding to the client. handleHashGlob
Time for '@nx/playwright/plugin:createNodes' 69.5402999999933
Time for 'total for sendMessageToDaemon()' 64.25849999999627
Time for 'deserialize daemon response' 0.038499999995110556
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:19.009Z - [REQUEST]: Responding to the client. handleHashGlob
Time for 'total for sendMessageToDaemon()' 60.14589999997406
Time for 'deserialize daemon response' 0.036299999977927655
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:19.019Z - [REQUEST]: Responding to the client. handleHashMultiGlob
Time for '@nx/jest/plugin:createNodes' 80.92990000001737
Time for 'total for sendMessageToDaemon()' 64.69789999999921
Time for 'deserialize daemon response' 0.03519999998388812
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:19.025Z - [REQUEST]: Responding to the client. handleHashGlob
Time for '@nx/webpack/plugin:createNodes' 77.03880000000936
Time for 'total for sendMessageToDaemon()' 68.20110000000568
Time for 'deserialize daemon response' 0.037899999995715916
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:19.043Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:19.044Z - Time taken for 'hash changed files from watcher' 0.6981000000087079ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:19.044Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:19.045Z - Handled HASH_GLOB. Handling time: 0. Response time: 69.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:19.045Z - Done responding to the client handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:19.045Z - Handled GLOB. Handling time: 5. Response time: 59.
Time for '@nx/vite/plugin:createNodes' 114.37690000000293
Time for 'total for sendMessageToDaemon()' 82.56719999999041
Time for 'deserialize daemon response' 0.03760000001057051
Time for '@nx/js/typescript:createNodes' 139.88259999998263
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:19.061Z - [REQUEST]: Responding to the client. handleHashMultiGlob
Time for 'total for sendMessageToDaemon()' 73.80329999999958
Time for 'deserialize daemon response' 0.04230000000097789
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:19.071Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:19.071Z - Handled HASH_GLOB. Handling time: 12. Response time: 69.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:19.072Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:19.072Z - Handled HASH_GLOB. Handling time: 4. Response time: 63.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:19.076Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:19.076Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:19.076Z - Handled HASH_GLOB. Handling time: 6. Response time: 57.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:19.077Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:19.077Z - Handled HASH_GLOB. Handling time: 4. Response time: 52.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:19.078Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:19.078Z - Handled HASH_GLOB. Handling time: 12. Response time: 35.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:19.082Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:19.082Z - Handled HASH_GLOB. Handling time: 12. Response time: 21.
Time for '@nx/expo/plugin:createNodes' 140.6132000000216
Time for 'total for sendMessageToDaemon()' 65.6541000000143
Time for 'deserialize daemon response' 0.03540000002249144
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:19.085Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:19.085Z - Handled HASH_GLOB. Handling time: 2. Response time: 9.
Time for '@nx/eslint/plugin:createNodes' 234.1015999999945
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:19.209Z - Time taken for 'build-project-configs' 262.0780000000086ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:19.211Z - Time taken for '@nx/js/typescript:createDependencies' 22.703200000018114ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:19.214Z - Time taken for '@nx/vite/plugin:createDependencies' 19.66709999999148ms
Time for 'build typescript dependencies' 0.0918999999994412
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:19.306Z - Wrote project graph cache to C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\.nx\workspace-data\project-graph.json
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:19.306Z - [SYNC]: collect registered sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:19.308Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:19.308Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:19.308Z - Time taken for 'total execution time for createProjectGraph()' 84.18829999997979ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:25.722Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:25.726Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:25.727Z - Reusing in-memory cached project graph because no files changed.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:25.727Z - [REQUEST]: Responding to the client. project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:25.729Z - Time taken for 'total for creating and serializing project graph' 0.8193999999784864ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:25.757Z - [SYNC]: @nx/js:typescript-sync changes: 
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:25.758Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 35.35679999997956ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:25.758Z - Done responding to the client project-graph
[NX v21.3.9 Daemon Server] - 2025-07-30T12:18:25.758Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 31.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:48.596Z - [WATCHER]: Processing file changes in outputs
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:48.601Z - [WATCHER]: package.json was modified
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:48.707Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:48.707Z - [REQUEST]: package.json
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:48.707Z - [REQUEST]: 
Time for 'nx/core/project-json:createNodes' 0.22070000000530854
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:48.743Z - [REQUEST]: Responding to the client. handleHashMultiGlob
Time for '@nx/react/router-plugin:createNodes' 32.43060000002151
Time for 'total for sendMessageToDaemon()' 23.43410000001313
Time for 'deserialize daemon response' 0.09090000001015142
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:48.751Z - [REQUEST]: Responding to the client. handleGlob
Time for 'total for sendMessageToDaemon()' 22.882299999997485
Time for 'deserialize daemon response' 0.05420000001322478
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:48.766Z - [REQUEST]: Responding to the client. handleHashGlob
Time for '@nx/playwright/plugin:createNodes' 40.68349999998463
Time for 'total for sendMessageToDaemon()' 34.85079999995651
Time for 'deserialize daemon response' 0.03779999999096617
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:48.774Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:48.774Z - Time taken for 'hash changed files from watcher' 0.7566000000224449ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:48.775Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:48.775Z - Handled HASH_GLOB. Handling time: 0. Response time: 32.
Time for 'nx/core/package-json:createNodes' 36.47100000001956
Time for '@nx/vite/plugin:createNodes' 51.01870000001509
Time for 'total for sendMessageToDaemon()' 38.23910000000615
Time for 'deserialize daemon response' 0.053699999989476055
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:48.780Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:48.781Z - Done responding to the client handleGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:48.781Z - Handled GLOB. Handling time: 3. Response time: 30.
Time for '@nx/webpack/plugin:createNodes' 49.59240000002319
Time for 'total for sendMessageToDaemon()' 33.56599999999162
Time for 'deserialize daemon response' 0.03889999998500571
Time for 'nx/js/dependencies-and-lockfile:createNodes' 45.68920000002254
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:48.796Z - [REQUEST]: Responding to the client. handleHashMultiGlob
Time for 'total for sendMessageToDaemon()' 44.13400000002002
Time for 'deserialize daemon response' 0.033500000019557774
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:48.805Z - [REQUEST]: Responding to the client. handleHashMultiGlob
Time for '@nx/jest/plugin:createNodes' 76.6938000000082
Time for 'total for sendMessageToDaemon()' 49.567800000018906
Time for 'deserialize daemon response' 0.034500000008847564
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:48.816Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:48.816Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:48.816Z - Handled HASH_GLOB. Handling time: 7. Response time: 50.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:48.816Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:48.816Z - Handled HASH_GLOB. Handling time: 5. Response time: 42.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:48.818Z - Done responding to the client handleHashGlob
Time for 'total for sendMessageToDaemon()' 50.06389999995008
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:48.818Z - Handled HASH_GLOB. Handling time: 2. Response time: 38.
Time for 'deserialize daemon response' 0.030399999988730997
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:48.831Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:48.831Z - Handled HASH_GLOB. Handling time: 12. Response time: 35.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:48.831Z - Done responding to the client handleHashMultiGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:48.831Z - Handled HASH_GLOB. Handling time: 6. Response time: 26.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:48.832Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:48.832Z - Handled HASH_GLOB. Handling time: 2. Response time: 16.
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:48.839Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:48.840Z - Done responding to the client handleHashGlob
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:48.840Z - Handled HASH_GLOB. Handling time: 3. Response time: 1.
Time for '@nx/js/typescript:createNodes' 128.43739999999525
Time for '@nx/expo/plugin:createNodes' 115.30410000000848
Time for 'total for sendMessageToDaemon()' 22.946300000010524
Time for 'deserialize daemon response' 0.08760000002803281
Time for '@nx/eslint/plugin:createNodes' 335.734300000011
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:49.115Z - Time taken for 'build-project-configs' 360.30689999996684ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:49.117Z - Time taken for '@nx/js/typescript:createDependencies' 33.34359999996377ms
Time for 'build typescript dependencies' 0.03539999999338761
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:49.199Z - Wrote project graph cache to C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo\.nx\workspace-data\project-graph.json
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:49.199Z - [SYNC]: collect registered sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:49.201Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:49.202Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:49.202Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:49.202Z - Time taken for 'total execution time for createProjectGraph()' 92.06020000000717ms
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:53.103Z - [WATCHER]: Processing file changes in outputs
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:53.116Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo (sources)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:53.118Z - Closed a connection. Number of open connections: 7
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:53.118Z - Closed a connection. Number of open connections: 6
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:53.118Z - Closed a connection. Number of open connections: 5
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:53.118Z - Closed a connection. Number of open connections: 4
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:53.119Z - Closed a connection. Number of open connections: 3
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:53.119Z - Closed a connection. Number of open connections: 2
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:53.119Z - Closed a connection. Number of open connections: 1
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:53.119Z - Closed a connection. Number of open connections: 0
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:53.121Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Desktop\nasaga limted\nasaga-monorepo (outputs)
[NX v21.3.9 Daemon Server] - 2025-07-30T12:20:53.127Z - Server stopped because: "LOCK_FILES_CHANGED"
