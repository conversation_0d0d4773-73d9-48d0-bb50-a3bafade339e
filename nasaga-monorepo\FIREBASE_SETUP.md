# Firebase Setup Guide

## Overview
This document provides instructions for completing the Firebase setup for the Nasaga monorepo project.

## Current Status
✅ Firebase CLI installed and configured
✅ Firebase project connected (nasaga-real-estate)
✅ Dependencies installed (firebase, firebase-admin, react-firebase-hooks)
✅ Environment files created for all apps
✅ Firebase configuration updated with all services
✅ Emulator configuration added

## Next Steps

### 1. Get Firebase Configuration Keys
1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Select your project: `nasaga-real-estate`
3. Go to Project Settings (gear icon)
4. In the "General" tab, scroll down to "Your apps"
5. If you don't have a web app, click "Add app" and select the web icon
6. Copy the config object values

### 2. Update Environment Files
Replace the placeholder values in the following files with your actual Firebase configuration:

#### Web Apps (.env.local files):
- `apps/web-admin/.env.local`
- `apps/web-superadmin/.env.local`

#### Mobile Apps (.env.local files):
- `apps/mobile-agent/.env.local`
- `apps/mobile-buyer/.env.local`

#### Backend App (.env.local file):
- `apps/backend/.env.local`

#### Required Keys:
- `FIREBASE_API_KEY`: Your Firebase API key
- `FIREBASE_AUTH_DOMAIN`: Usually `{project-id}.firebaseapp.com`
- `FIREBASE_PROJECT_ID`: Your Firebase project ID
- `FIREBASE_STORAGE_BUCKET`: Usually `{project-id}.appspot.com`
- `FIREBASE_MESSAGING_SENDER_ID`: Your messaging sender ID
- `FIREBASE_APP_ID`: Your Firebase app ID
- `FIREBASE_MEASUREMENT_ID`: Your Google Analytics measurement ID (optional)
- `FIREBASE_VAPID_KEY`: Your VAPID key for push notifications

### 3. Set Up Firebase Services

#### Enable Required Services:
1. **Authentication**: Go to Authentication > Sign-in method and enable desired providers
2. **Firestore**: Go to Firestore Database and create a database
3. **Cloud Functions**: Will be enabled automatically when you deploy functions
4. **Cloud Messaging**: Go to Cloud Messaging and generate VAPID keys
5. **Hosting**: Will be configured for your web apps

#### Generate VAPID Keys for Push Notifications:
1. In Firebase Console, go to Project Settings
2. Go to the "Cloud Messaging" tab
3. In the "Web configuration" section, generate a key pair
4. Copy the "Key pair" value and use it as `FIREBASE_VAPID_KEY`

### 4. Set Up Service Account (for Backend)
1. Go to Project Settings > Service Accounts
2. Click "Generate new private key"
3. Download the JSON file
4. Extract the following values and add to `apps/backend/.env.local`:
   - `FIREBASE_CLIENT_EMAIL`: client_email from JSON
   - `FIREBASE_PRIVATE_KEY`: private_key from JSON (keep the \\n characters)

### 5. Configure Mobile Apps
For the mobile apps, you'll also need to:

#### Android:
1. Go to Project Settings > General
2. Add an Android app if not already added
3. Download `google-services.json`
4. Place it in:
   - `apps/mobile-agent/google-services.json`
   - `apps/mobile-buyer/google-services.json`

#### iOS:
1. Add an iOS app in Project Settings
2. Download `GoogleService-Info.plist`
3. Add it to your iOS project when building for iOS

### 6. Test Firebase Connection
Run the following commands to test your Firebase setup:

```bash
# Test Firebase CLI connection
firebase projects:list

# Start Firebase emulators (for local development)
firebase emulators:start

# Build and serve web apps
nx serve web-admin
nx serve web-superadmin

# Build backend
nx build backend

# Run mobile apps
nx run mobile-agent:start
nx run mobile-buyer:start
```

### 7. Deploy to Firebase
Once everything is configured and tested locally:

```bash
# Build all apps
nx build web-admin
nx build web-superadmin
nx build backend

# Deploy to Firebase
firebase deploy --only hosting:web-admin
firebase deploy --only hosting:web-superadmin
firebase deploy --only functions
firebase deploy --only firestore:rules
```

## File Structure
```
├── .firebaserc                 # Firebase project configuration
├── firebase.json              # Firebase services configuration
├── firestore.rules           # Firestore security rules
├── firestore.indexes.json    # Firestore indexes
├── apps/
│   ├── web-admin/.env.local
│   ├── web-superadmin/.env.local
│   ├── mobile-agent/
│   │   ├── .env.local
│   │   └── app.config.js
│   ├── mobile-buyer/
│   │   ├── .env.local
│   │   └── app.config.js
│   └── backend/.env.local
└── libs/
    └── utils/src/lib/
        ├── firebase-config.ts      # Client-side Firebase configuration
        └── firebase-admin.ts       # Server-side Firebase Admin configuration
```

## Security Notes
- Keep your service account keys secure and never commit them to version control
- Use Firebase Security Rules to protect your data
- Enable App Check for additional security in production
- Regularly rotate your API keys and service account keys

## Support
- [Firebase Documentation](https://firebase.google.com/docs)
- [Firebase CLI Reference](https://firebase.google.com/docs/cli)
- [Firebase Emulator Suite](https://firebase.google.com/docs/emulator-suite)
