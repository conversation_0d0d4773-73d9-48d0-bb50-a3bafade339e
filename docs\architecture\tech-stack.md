# **Tech Stack**

This is the DEFINITIVE technology selection section. All development must use these exact versions.

## **Cloud Infrastructure**

* **Provider:** Google Cloud Platform (GCP) via Firebase  
* **Key Services:** Firebase Authentication, Firebase Firestore, Firebase Storage, Firebase Cloud Messaging (FCM), Google Cloud Functions  
* **Deployment Host and Regions:** Firebase (automatic hosting and scaling), specific regions can be configured.

## **Technology Stack Table**

| Category | Technology | Version | Purpose | Rationale |
| :---- | :---- | :---- | :---- | :---- |
| Frontend Language | TypeScript | Latest Stable (e.g., 5.x) | Primary development language for type safety and maintainability. | Modern, robust for building complex user interfaces, type safety for maintainability. |
| Frontend Framework (Web) | React | Latest Stable (e.g., 18.x) | Core library for building Admin and Super Admin web dashboards. | Modern, robust for building complex user interfaces, type safety for maintainability. |
| UI Component Library (Web) | N/A (to be decided, possibly Tailwind CSS or Material-UI) | N/A | To accelerate UI development and ensure consistency for web apps. | To be selected based on design preference and developer experience. |
| State Management (Web) | React Context API / Zustand (or similar lightweight) | N/A | For managing local and global state in web applications. | React Context for simpler global state, lightweight libraries for more complex scenarios. |
| Frontend Framework (Mobile) | React Native with Expo | Latest Stable SDK (e.g., 50.x) | For building cross-platform Agent and Buyer mobile apps from a single codebase. | Enables cross-platform mobile development from a single codebase, leveraging existing React knowledge. Expo simplifies the native build process. |
| UI Component Library (Mobile) | N/A (to be decided, Expo components, NativeBase, UI Kitten) | N/A | For building native-like UI components for mobile apps. | To be selected based on native UI needs and developer experience. |
| State Management (Mobile) | React Context API / Zustand (or similar lightweight) | N/A | For managing local and global state in mobile applications. | React Context for simpler global state, lightweight libraries for more complex scenarios. |
| Backend Language | TypeScript | Latest Stable (e.g., 5.x) | Primary development language for backend logic. | Consistency with frontend, strong typing. |
| Backend Runtime | Node.js | Latest LTS (e.g., 20.x) | JavaScript runtime for the backend API. | Popular, performant, and integrates well with Firebase Admin SDK for server-side logic and custom API endpoints. |
| Backend Framework | Express | Latest Stable (e.g., 4.x) | Minimalist web framework for backend API. | Popular, performant, and integrates well with Firebase Admin SDK for server-side logic and custom API endpoints. |
| API Style | REST | 1.0 | Standard API communication for custom backend logic not directly handled by Firebase SDKs. | Widely adopted, flexible for various client applications. |
| Database | Firebase Firestore | N/A | Scalable, real-time NoSQL database for structured data. | Offers real-time synchronization and scales with demand, crucial for live property updates. |
| Cache | N/A (Firestore caching / application-level) | N/A | Firestore has offline persistence. Application-level caching can be implemented for specific needs. | Firestore provides some built-in caching; specific caching layers will be implemented as performance needs arise. |
| File Storage | Firebase Storage | N/A | Secure and scalable file management for property images and documents. | Provides secure and scalable file management for property media. |
| Authentication | Firebase Authentication | N/A | Secure, role-based user authentication for all user types. | Providing seamless onboarding and secure, role-based access. |
| Frontend Testing | Jest / React Testing Library | Latest Stable | Unit and integration testing for React web and React Native components. | Standard for React ecosystems, provides robust testing utilities. |
| Backend Testing | Jest / Supertest | Latest Stable | Unit and integration testing for Node.js/Express backend. | Standard for Node.js testing, Supertest for API integration. |
| E2E Testing | Cypress / Playwright (with Nx integration) | Latest Stable | End-to-End testing for critical user flows across web and mobile. | Comprehensive testing strategy for secure, scalable, and maintainable codebase. |
| Build Tool | Nx | Latest Stable (e.g., 18.x) | Monorepo management, build optimization, task execution. | Comprehensive support for React, React Native with Expo, and Node.js/Express; computation caching, distributed task execution, code generation. |
| Bundler | Webpack (for web) / Metro (for React Native) | N/A | Bundling JavaScript applications. | Standard tools for React/React Native ecosystems. |
| IaC Tool | N/A (Firebase CLI / Google Cloud CLI for manual setup, or Terraform if more complex infrastructure is needed later) | N/A | For managing cloud resources. | Initial setup via Firebase CLI; scalable IaC solution will be introduced if non-Firebase GCP resources become significant. |
| CI/CD | GitHub Actions (with Nx Cloud integration) | N/A | Automated build, test, and deployment pipelines. | Nx's affected commands and distributed task execution optimize CI/CD. |
| Monitoring | Firebase Performance Monitoring, Google Cloud Monitoring | N/A | For application performance and infrastructure health. | Integrated with Firebase for mobile and web performance, GCP for broader service monitoring. |
| Logging | Google Cloud Logging (with Firebase Logging) | N/A | Centralized logging for all application components. | Integrated with GCP ecosystem for comprehensive log management. |
| CSS Framework | Tailwind CSS | Latest Stable | Utility-first CSS framework for rapid and consistent styling. | Highly customizable, efficient for responsive design. |
