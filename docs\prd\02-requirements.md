# Requirements

## Functional

1.  **FR1**: The platform shall enable secure, role-based authentication for Super Admins, Admins, Agents, and Buyers using Firebase Authentication (email/password, phone, social login).  
2.  **FR2**: Agents shall be able to create, edit, view, and delist property listings via the Agent Mobile App.  
3.  **FR3**: Agents shall be able to upload images and documents associated with properties to Firebase Storage via the Agent Mobile App.  
4.  **FR4**: Buyers shall be able to browse, search, and filter property listings via the Buyer Mobile App.  
5.  **FR5**: Buyers shall be able to "favorite" (save) properties for later viewing via the Buyer Mobile App.  
6.  **FR6**: <PERSON><PERSON> shall be able to manage user accounts (Super Admins, Admins, Agents, Buyers) via the Admin Web Dashboard.  
7.  **FR7**: <PERSON><PERSON> shall be able to view, approve, and reject property listings via the Admin Web Dashboard.  
8.  **FR8**: Super Admins shall have all Admin capabilities and additionally be able to manage other Admin accounts via the Super Admin Web Dashboard.  
9.  **FR9**: The platform shall send real-time push notifications for alerts and updates (e.g., new inquiry, property status change) using Firebase Cloud Messaging.  
10. **FR10**: The backend API (Node.js/Express) shall provide necessary endpoints for data operations not directly handled by Firebase SDKs, integrating with Firebase Firestore and Admin SDK.

## Non Functional

1.  **NFR1**: The platform shall be scalable to support a growing number of users, properties, and data transactions without significant performance degradation.  
2.  **NFR2**: The platform shall ensure data security and privacy for all user and property information, adhering to industry best practices and utilizing Firebase security features.  
3.  **NFR3**: The system shall provide real-time updates for property listings and notifications across all connected applications (web dashboards, mobile apps).  
4.  **NFR4**: The mobile applications (Agent and Buyer) shall provide a seamless and intuitive user experience.  
5.  **NFR5**: The platform shall maintain an uptime of 99.9% annually.  
6.  **NFR6**: The codebase shall be maintainable and adhere to defined coding standards (to be specified in Architecture).  
7.  **NFR7**: The application shall be compatible with the latest two stable versions of major web browsers (Chrome, Firefox, Safari, Edge) and mobile OS (iOS 15+, Android 10+).
