import React, { forwardRef } from 'react';

export interface InputFieldProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string;
  error?: string;
  helperText?: string;
  variant?: 'default' | 'mobile';
}

/**
 * Reusable input field component for forms
 * Supports both web and mobile styling variants
 */
export const InputField = forwardRef<HTMLInputElement, InputFieldProps>(
  ({ label, error, helperText, variant = 'default', className = '', ...props }, ref) => {
    const baseInputClasses = variant === 'mobile' 
      ? 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-gray-900'
      : 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500';
    
    const errorInputClasses = error 
      ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
      : '';

    const labelClasses = variant === 'mobile'
      ? 'block text-sm font-medium text-gray-700 mb-2'
      : 'block text-sm font-medium text-gray-700 mb-1';

    return (
      <div className="w-full">
        {label && (
          <label htmlFor={props.id} className={labelClasses}>
            {label}
            {props.required && <span className="text-red-500 ml-1">*</span>}
          </label>
        )}
        <input
          ref={ref}
          className={`${baseInputClasses} ${errorInputClasses} ${className}`}
          aria-invalid={error ? 'true' : 'false'}
          aria-describedby={error ? `${props.id}-error` : helperText ? `${props.id}-helper` : undefined}
          {...props}
        />
        {error && (
          <p id={`${props.id}-error`} className="mt-1 text-sm text-red-600" role="alert">
            {error}
          </p>
        )}
        {helperText && !error && (
          <p id={`${props.id}-helper`} className="mt-1 text-sm text-gray-500">
            {helperText}
          </p>
        )}
      </div>
    );
  }
);

InputField.displayName = 'InputField';
