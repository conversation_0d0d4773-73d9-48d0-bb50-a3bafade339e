import React from 'react';
import { ThemeProvider, CssBaseline } from '@mui/material';
import { theme } from '../theme';

export interface NasagaThemeProviderProps {
  children: React.ReactNode;
}

/**
 * NasagaThemeProvider wraps the application with the Nasaga design system theme
 * and provides baseline CSS normalization
 */
const NasagaThemeProvider: React.FC<NasagaThemeProviderProps> = ({ children }) => {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      {children}
    </ThemeProvider>
  );
};

export default NasagaThemeProvider;
