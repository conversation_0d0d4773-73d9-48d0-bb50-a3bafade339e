# Technical Assumptions

## Repository Structure: Monorepo

* **Repository Structure**: Monorepo.  
    * **Rationale**: As discussed, a monorepo (likely using Nx or Turborepo) will be used to manage the multiple applications (Admin Web, Super Admin Web, Agent Mobile, Buyer Mobile, Backend API) within a single repository to promote code sharing and streamline CI/CD.

## Service Architecture

* **Service Architecture**: Hybrid approach combining Firebase services (Firestore, Authentication, Storage, Cloud Messaging) with a Node.js/Express backend API.  
    * **Rationale**: Leveraging Firebase for core BaaS functionalities minimizes infrastructure overhead, while the Node.js/Express API will handle custom business logic and complex integrations not natively supported by Firebase SDKs.

## Testing Requirements

* **Testing Requirements**: Full Testing Pyramid.  
    * **Rationale**: To ensure a secure, scalable, and maintainable codebase, a comprehensive testing strategy including Unit, Integration, and End-to-End (E2E) tests will be implemented across both web and mobile applications. This aligns with the "Secure, scalable, and maintainable codebase" success criterion.

## Additional Technical Assumptions and Requests

* **Cloud Provider**: Google Cloud Platform (GCP) via Firebase.  
    * **Rationale**: Firebase is a Google product, and deeper integrations will naturally lean into GCP services.  
* **Authentication**: Firebase Authentication for all user types.  
    * **Rationale**: As specified in the Project Brief, providing seamless onboarding and secure, role-based access.  
* **Database**: Firebase Firestore for structured data.  
    * **Rationale**: Offers real-time synchronization and scales with demand, crucial for live property updates.  
* **Storage**: Firebase Storage for images and documents.  
    * **Rationale**: Provides secure and scalable file management for property media.  
* **Push Notifications**: Firebase Cloud Messaging.  
    * **Rationale**: Enables real-time alerts and updates to users.  
* **Web Frontend Framework**: React with TypeScript.  
    * **Rationale**: Modern, robust for building complex user interfaces, type safety for maintainability.  
* **Mobile Frontend Framework**: React Native with Expo.  
    * **Rationale**: Enables cross-platform mobile development from a single codebase, leveraging existing React knowledge. Expo simplifies the native build process.  
* **Backend Runtime/Framework**: Node.js/Express.  
    * **Rationale**: Popular, performant, and integrates well with Firebase Admin SDK for server-side logic and custom API endpoints.
