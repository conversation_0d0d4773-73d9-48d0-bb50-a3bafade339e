import React from 'react';
import { RegistrationPage } from '@nasaga-monorepo/ui';

export const RegisterScreen: React.FC<{ navigation: any }> = ({ navigation }) => {
  const handleSignIn = () => {
    navigation.navigate('Login');
  };

  const handleRegistrationSuccess = () => {
    // Redirect will be handled by the auth redirect hook
    console.log('Registration successful');
  };

  return (
    <RegistrationPage
      onSignIn={handleSignIn}
      onRegistrationSuccess={handleRegistrationSuccess}
      variant="mobile"
    />
  );
};
