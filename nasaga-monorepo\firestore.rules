rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    // Helper functions for role-based access control
    function isAuthenticated() {
      return request.auth != null;
    }

    function getUserRole() {
      return request.auth.token.role;
    }

    function isSuperAdmin() {
      return isAuthenticated() && getUserRole() == 'superAdmin';
    }

    function isAdmin() {
      return isAuthenticated() && (getUserRole() == 'admin' || getUserRole() == 'superAdmin');
    }

    function isAgent() {
      return isAuthenticated() && getUserRole() == 'agent';
    }

    function isBuyer() {
      return isAuthenticated() && getUserRole() == 'buyer';
    }

    function isOwner(uid) {
      return isAuthenticated() && request.auth.uid == uid;
    }

    function isOwnerOrAdmin(uid) {
      return isOwner(uid) || isAdmin();
    }

    // =============================================================================
    // USERS COLLECTION RULES
    // =============================================================================
    // Rule: /users/{uid}: read/write only owner; role field immutable except by superAdmin
    match /users/{uid} {
      // read/write only owner
      allow read: if isOwner(uid);
      allow create: if isOwner(uid);
      allow update: if isOwner(uid) &&
                     (!('role' in request.resource.data.diff(resource.data).affectedKeys()) || isSuperAdmin());
      allow delete: if isOwner(uid);
    }

    // =============================================================================
    // PROPERTIES COLLECTION RULES  
    // =============================================================================
    // Rule: /properties/{pid}: create by agents; pending properties read by admins; 
    //       only admins/superAdmins can set status != pending
    match /properties/{propertyId} {
      // Read: Pending properties can only be read by admins/superAdmins
      //       Other status properties can be read by anyone (public listings)
      allow read: if resource.data.status != 'pending' ||
                     isAdmin();

      // Create: Only agents can create properties, and status must be 'pending'
      allow create: if isAgent() &&
                       request.auth.uid == request.resource.data.agentUid &&
                       request.resource.data.status == 'pending';

      // Update: Agents can update their own properties but cannot change status from 'pending'
      //         Only admins/superAdmins can set status to anything other than 'pending'
      allow update: if (isAgent() &&
                       resource.data.agentUid == request.auth.uid &&
                       (request.resource.data.status == 'pending' || isAdmin())) ||
                      isAdmin();

      // Delete: Only admins/superAdmins can delete properties
      allow delete: if isAdmin();
    }

    // Property images subcollection (if using subcollections for images)
    match /properties/{propertyId}/images/{imageId} {
      allow read: if true; // Images are public once property is approved
      allow write: if isAgent() &&
                      get(/databases/$(database)/documents/properties/$(propertyId)).data.agentUid == request.auth.uid;
      allow delete: if isAgent() &&
                       get(/databases/$(database)/documents/properties/$(propertyId)).data.agentUid == request.auth.uid ||
                       isAdmin();
    }

    // Analytics collection (admin only)
    match /analytics/{document} {
      allow read, write: if isAdmin();
    }

    // System configuration (super admin only)
    match /config/{document} {
      allow read: if isAdmin();
      allow write: if isSuperAdmin();
    }

    // Audit logs (admin read only)
    match /audit_logs/{logId} {
      allow read: if isAdmin();
      allow create: if isAuthenticated(); // System can create audit logs
    }

    // Deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
