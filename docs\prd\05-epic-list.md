# Epic List

CRITICAL: Epics MUST be logically sequential following agile best practices:  
- Each epic should deliver a significant, end-to-end, fully deployable increment of testable functionality  
- Epic 1 must establish foundational project infrastructure (app setup, Git, CI/CD, core services) unless we are adding new functionality to an existing app, while also delivering an initial piece of functionality, even as simple as a health-check route or display of a simple canary page - remember this when we produce the stories for the first epic!  
- Each subsequent epic builds upon previous epics' functionality delivering major blocks of functionality that provide tangible value to users or business when deployed  
- Not every project needs multiple epics, an epic needs to deliver value. For example, an API completed can deliver value even if a UI is not complete and planned for a separate epic.  
- Err on the side of less epics, but let the user know your rationale and offer options for splitting them if it seems some are too large or focused on disparate things.  
- Cross Cutting Concerns should flow through epics and stories and not be final stories. For example, adding a logging framework as a last story of an epic, or at the end of a project as a final epic or story would be terrible as we would not have logging from the beginning.

* **Epic 1: Platform Foundation & Admin/Super Admin Management:** Establish core project infrastructure, Firebase integration, secure authentication for all roles, and develop the Admin and Super Admin web dashboards for user and basic platform management.  
* **Epic 2: Agent Property Listing & Management:** Develop the Agent Mobile App functionality for agents to create, manage, and upload media for their property listings.  
* **Epic 3: Buyer Property Discovery & Interaction:** Develop the Buyer Mobile App functionality for buyers to browse, search, favorite properties, and initiate inquiries.  
* **Epic 4: Real-time Notifications & Platform Enhancements:** Implement comprehensive push notification capabilities and refine platform features based on user feedback and initial analytics.
