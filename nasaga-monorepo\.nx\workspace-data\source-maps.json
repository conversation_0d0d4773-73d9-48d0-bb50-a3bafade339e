{"apps/backend": {"root": ["apps/backend/package.json", "nx/core/package-json"], "projectType": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets": ["apps/backend/tsconfig.app.json", "@nx/js/typescript"], "targets.typecheck": ["apps/backend/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.dependsOn": ["apps/backend/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.options": ["apps/backend/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.cache": ["nx.json", "nx/target-defaults"], "targets.typecheck.inputs": ["nx.json", "nx/target-defaults"], "targets.typecheck.outputs": ["apps/backend/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.syncGenerators": ["apps/backend/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata": ["apps/backend/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.executor": ["apps/backend/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.options.cwd": ["apps/backend/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.options.command": ["apps/backend/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.technologies": ["apps/backend/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.technologies.0": ["apps/backend/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.description": ["apps/backend/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.help": ["apps/backend/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.help.command": ["apps/backend/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.help.example": ["apps/backend/tsconfig.json", "@nx/js/typescript"], "targets.lint": ["apps/backend/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.cache": ["nx.json", "nx/target-defaults"], "targets.lint.options": ["apps/backend/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.inputs": ["nx.json", "nx/target-defaults"], "targets.lint.outputs": ["apps/backend/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata": ["apps/backend/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.executor": ["apps/backend/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.options.cwd": ["apps/backend/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.options.command": ["apps/backend/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.technologies": ["apps/backend/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.technologies.0": ["apps/backend/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.description": ["apps/backend/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help": ["apps/backend/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help.command": ["apps/backend/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help.example": ["apps/backend/eslint.config.mjs", "@nx/eslint/plugin"], "targets.test": ["apps/backend/package.json", "nx/core/package-json"], "targets.test.options": ["apps/backend/package.json", "nx/core/package-json"], "targets.test.metadata": ["apps/backend/jest.config.ts", "@nx/jest/plugin"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.inputs": ["nx.json", "nx/target-defaults"], "targets.test.outputs": ["nx.json", "nx/target-defaults"], "targets.test.executor": ["apps/backend/jest.config.ts", "@nx/jest/plugin"], "targets.test.options.cwd": ["apps/backend/jest.config.ts", "@nx/jest/plugin"], "targets.test.options.env": ["apps/backend/jest.config.ts", "@nx/jest/plugin"], "targets.test.options.command": ["apps/backend/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.technologies": ["apps/backend/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.technologies.0": ["apps/backend/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.description": ["apps/backend/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.help": ["apps/backend/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.help.command": ["apps/backend/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.help.example": ["apps/backend/jest.config.ts", "@nx/jest/plugin"], "targets.build": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.build.options": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.outputs": ["nx.json", "nx/target-defaults"], "targets.build.metadata": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.build.syncGenerators": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.build.executor": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.build.options.cwd": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.build.options.args": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.build.options.command": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.build.metadata.technologies": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.build.metadata.technologies.0": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.build.metadata.description": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.build.metadata.help": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.build.metadata.help.command": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.build.metadata.help.example": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.preview": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.continuous": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.options": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.metadata": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.syncGenerators": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.executor": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.options.cwd": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.options.args": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.options.command": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.metadata.technologies": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.metadata.technologies.0": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.metadata.description": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.metadata.help": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.metadata.help.command": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.metadata.help.example": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.build-deps": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.build-deps.dependsOn": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.watch-deps": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.watch-deps.continuous": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.watch-deps.dependsOn": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.watch-deps.executor": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.watch-deps.options": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.watch-deps.options.command": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "name": ["apps/backend/package.json", "nx/core/package-json"], "tags": ["apps/backend/package.json", "nx/core/package-json"], "tags.npm:private": ["apps/backend/package.json", "nx/core/package-json"], "metadata.targetGroups": ["apps/backend/package.json", "nx/core/package-json"], "metadata.js": ["apps/backend/package.json", "nx/core/package-json"], "metadata.js.packageName": ["apps/backend/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["apps/backend/package.json", "nx/core/package-json"], "targets.serve": ["apps/backend/package.json", "nx/core/package-json"], "targets.serve.continuous": ["apps/backend/package.json", "nx/core/package-json"], "targets.serve.executor": ["apps/backend/package.json", "nx/core/package-json"], "targets.serve.defaultConfiguration": ["apps/backend/package.json", "nx/core/package-json"], "targets.serve.dependsOn": ["apps/backend/package.json", "nx/core/package-json"], "targets.serve.options": ["apps/backend/package.json", "nx/core/package-json"], "targets.serve.configurations": ["apps/backend/package.json", "nx/core/package-json"], "targets.serve.options.buildTarget": ["apps/backend/package.json", "nx/core/package-json"], "targets.serve.options.runBuildTargetDependencies": ["apps/backend/package.json", "nx/core/package-json"], "targets.serve.configurations.development": ["apps/backend/package.json", "nx/core/package-json"], "targets.serve.configurations.development.buildTarget": ["apps/backend/package.json", "nx/core/package-json"], "targets.serve.configurations.production": ["apps/backend/package.json", "nx/core/package-json"], "targets.serve.configurations.production.buildTarget": ["apps/backend/package.json", "nx/core/package-json"], "targets.test.options.passWithNoTests": ["apps/backend/package.json", "nx/core/package-json"], "targets.typecheck.parallelism": ["nx.json", "nx/target-defaults"], "targets.lint.parallelism": ["nx.json", "nx/target-defaults"], "targets.test.dependsOn": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"]}, "apps/backend-e2e": {"root": ["apps/backend-e2e/package.json", "nx/core/package-json"], "projectType": ["apps/backend-e2e/tsconfig.json", "@nx/js/typescript"], "targets": ["apps/backend-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck": ["apps/backend-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.dependsOn": ["apps/backend-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.options": ["apps/backend-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.cache": ["nx.json", "nx/target-defaults"], "targets.typecheck.inputs": ["nx.json", "nx/target-defaults"], "targets.typecheck.outputs": ["apps/backend-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.syncGenerators": ["apps/backend-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata": ["apps/backend-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.executor": ["apps/backend-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.options.cwd": ["apps/backend-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.options.command": ["apps/backend-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.technologies": ["apps/backend-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.technologies.0": ["apps/backend-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.description": ["apps/backend-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.help": ["apps/backend-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.help.command": ["apps/backend-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.help.example": ["apps/backend-e2e/tsconfig.json", "@nx/js/typescript"], "targets.lint": ["apps/backend-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.cache": ["nx.json", "nx/target-defaults"], "targets.lint.options": ["apps/backend-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.inputs": ["nx.json", "nx/target-defaults"], "targets.lint.outputs": ["apps/backend-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata": ["apps/backend-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.executor": ["apps/backend-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.options.cwd": ["apps/backend-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.options.command": ["apps/backend-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.technologies": ["apps/backend-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.technologies.0": ["apps/backend-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.description": ["apps/backend-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help": ["apps/backend-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help.command": ["apps/backend-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help.example": ["apps/backend-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "name": ["apps/backend-e2e/package.json", "nx/core/package-json"], "tags": ["apps/backend-e2e/package.json", "nx/core/package-json"], "tags.npm:private": ["apps/backend-e2e/package.json", "nx/core/package-json"], "implicitDependencies": ["apps/backend-e2e/package.json", "nx/core/package-json"], "implicitDependencies.@nasaga-monorepo/backend": ["apps/backend-e2e/package.json", "nx/core/package-json"], "metadata.targetGroups": ["apps/backend-e2e/package.json", "nx/core/package-json"], "metadata.js": ["apps/backend-e2e/package.json", "nx/core/package-json"], "metadata.js.packageName": ["apps/backend-e2e/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["apps/backend-e2e/package.json", "nx/core/package-json"], "targets.e2e": ["apps/backend-e2e/package.json", "nx/core/package-json"], "targets.e2e.executor": ["apps/backend-e2e/package.json", "nx/core/package-json"], "targets.e2e.outputs": ["apps/backend-e2e/package.json", "nx/core/package-json"], "targets.e2e.options": ["apps/backend-e2e/package.json", "nx/core/package-json"], "targets.e2e.dependsOn": ["apps/backend-e2e/package.json", "nx/core/package-json"], "targets.e2e.options.jestConfig": ["apps/backend-e2e/package.json", "nx/core/package-json"], "targets.e2e.options.passWithNoTests": ["apps/backend-e2e/package.json", "nx/core/package-json"], "targets.typecheck.parallelism": ["nx.json", "nx/target-defaults"], "targets.lint.parallelism": ["nx.json", "nx/target-defaults"], "targets.e2e.cache": ["nx.json", "nx/target-defaults"], "targets.e2e.parallelism": ["nx.json", "nx/target-defaults"]}, "apps/mobile-agent": {"root": ["apps/mobile-agent/package.json", "nx/core/package-json"], "projectType": ["apps/mobile-agent/tsconfig.spec.json", "@nx/js/typescript"], "targets": ["apps/mobile-agent/tsconfig.app.json", "@nx/js/typescript"], "targets.typecheck": ["apps/mobile-agent/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.dependsOn": ["apps/mobile-agent/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.options": ["apps/mobile-agent/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.cache": ["nx.json", "nx/target-defaults"], "targets.typecheck.inputs": ["nx.json", "nx/target-defaults"], "targets.typecheck.outputs": ["apps/mobile-agent/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.syncGenerators": ["apps/mobile-agent/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata": ["apps/mobile-agent/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.executor": ["apps/mobile-agent/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.options.cwd": ["apps/mobile-agent/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.options.command": ["apps/mobile-agent/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.technologies": ["apps/mobile-agent/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.technologies.0": ["apps/mobile-agent/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.description": ["apps/mobile-agent/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.help": ["apps/mobile-agent/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.help.command": ["apps/mobile-agent/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.help.example": ["apps/mobile-agent/tsconfig.json", "@nx/js/typescript"], "targets.lint": ["apps/mobile-agent/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.cache": ["nx.json", "nx/target-defaults"], "targets.lint.options": ["apps/mobile-agent/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.inputs": ["nx.json", "nx/target-defaults"], "targets.lint.outputs": ["apps/mobile-agent/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata": ["apps/mobile-agent/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.executor": ["apps/mobile-agent/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.options.cwd": ["apps/mobile-agent/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.options.command": ["apps/mobile-agent/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.technologies": ["apps/mobile-agent/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.technologies.0": ["apps/mobile-agent/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.description": ["apps/mobile-agent/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help": ["apps/mobile-agent/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help.command": ["apps/mobile-agent/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help.example": ["apps/mobile-agent/eslint.config.mjs", "@nx/eslint/plugin"], "targets.test": ["apps/mobile-agent/jest.config.ts", "@nx/jest/plugin"], "targets.test.options": ["apps/mobile-agent/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata": ["apps/mobile-agent/jest.config.ts", "@nx/jest/plugin"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.inputs": ["nx.json", "nx/target-defaults"], "targets.test.outputs": ["nx.json", "nx/target-defaults"], "targets.test.executor": ["apps/mobile-agent/jest.config.ts", "@nx/jest/plugin"], "targets.test.options.cwd": ["apps/mobile-agent/jest.config.ts", "@nx/jest/plugin"], "targets.test.options.env": ["apps/mobile-agent/jest.config.ts", "@nx/jest/plugin"], "targets.test.options.command": ["apps/mobile-agent/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.technologies": ["apps/mobile-agent/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.technologies.0": ["apps/mobile-agent/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.description": ["apps/mobile-agent/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.help": ["apps/mobile-agent/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.help.command": ["apps/mobile-agent/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.help.example": ["apps/mobile-agent/jest.config.ts", "@nx/jest/plugin"], "targets.start": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.start.executor": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.start.continuous": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.serve": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.serve.continuous": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.serve.options": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.serve.executor": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.serve.options.cwd": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.serve.options.args": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.serve.options.command": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.run-ios": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.run-ios.continuous": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.run-ios.options": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.run-ios.executor": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.run-ios.options.cwd": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.run-ios.options.command": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.run-android": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.run-android.continuous": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.run-android.options": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.run-android.executor": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.run-android.options.cwd": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.run-android.options.command": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.export": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.export.options": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.export.cache": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.export.dependsOn": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.export.inputs": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.export.outputs": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.export.executor": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.export.options.cwd": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.export.options.args": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.export.options.command": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.install": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.install.executor": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.prebuild": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.prebuild.executor": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.build": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.build.executor": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.submit": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.submit.options": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.submit.executor": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.submit.options.cwd": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.submit.options.command": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.build-deps": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.build-deps.dependsOn": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.watch-deps": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.watch-deps.continuous": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.watch-deps.dependsOn": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.watch-deps.executor": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.watch-deps.options": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "targets.watch-deps.options.command": ["apps/mobile-agent/app.json", "@nx/expo/plugin"], "name": ["apps/mobile-agent/package.json", "nx/core/package-json"], "tags": ["apps/mobile-agent/package.json", "nx/core/package-json"], "tags.npm:private": ["apps/mobile-agent/package.json", "nx/core/package-json"], "metadata.targetGroups": ["apps/mobile-agent/package.json", "nx/core/package-json"], "metadata.js": ["apps/mobile-agent/package.json", "nx/core/package-json"], "metadata.js.packageName": ["apps/mobile-agent/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["apps/mobile-agent/package.json", "nx/core/package-json"], "targets.typecheck.parallelism": ["nx.json", "nx/target-defaults"], "targets.lint.parallelism": ["nx.json", "nx/target-defaults"], "targets.test.dependsOn": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.outputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"]}, "apps/mobile-buyer": {"root": ["apps/mobile-buyer/package.json", "nx/core/package-json"], "projectType": ["apps/mobile-buyer/tsconfig.spec.json", "@nx/js/typescript"], "targets": ["apps/mobile-buyer/tsconfig.app.json", "@nx/js/typescript"], "targets.typecheck": ["apps/mobile-buyer/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.dependsOn": ["apps/mobile-buyer/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.options": ["apps/mobile-buyer/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.cache": ["nx.json", "nx/target-defaults"], "targets.typecheck.inputs": ["nx.json", "nx/target-defaults"], "targets.typecheck.outputs": ["apps/mobile-buyer/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.syncGenerators": ["apps/mobile-buyer/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata": ["apps/mobile-buyer/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.executor": ["apps/mobile-buyer/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.options.cwd": ["apps/mobile-buyer/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.options.command": ["apps/mobile-buyer/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.technologies": ["apps/mobile-buyer/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.technologies.0": ["apps/mobile-buyer/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.description": ["apps/mobile-buyer/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.help": ["apps/mobile-buyer/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.help.command": ["apps/mobile-buyer/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.help.example": ["apps/mobile-buyer/tsconfig.json", "@nx/js/typescript"], "targets.lint": ["apps/mobile-buyer/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.cache": ["nx.json", "nx/target-defaults"], "targets.lint.options": ["apps/mobile-buyer/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.inputs": ["nx.json", "nx/target-defaults"], "targets.lint.outputs": ["apps/mobile-buyer/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata": ["apps/mobile-buyer/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.executor": ["apps/mobile-buyer/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.options.cwd": ["apps/mobile-buyer/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.options.command": ["apps/mobile-buyer/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.technologies": ["apps/mobile-buyer/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.technologies.0": ["apps/mobile-buyer/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.description": ["apps/mobile-buyer/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help": ["apps/mobile-buyer/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help.command": ["apps/mobile-buyer/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help.example": ["apps/mobile-buyer/eslint.config.mjs", "@nx/eslint/plugin"], "targets.test": ["apps/mobile-buyer/jest.config.ts", "@nx/jest/plugin"], "targets.test.options": ["apps/mobile-buyer/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata": ["apps/mobile-buyer/jest.config.ts", "@nx/jest/plugin"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.inputs": ["nx.json", "nx/target-defaults"], "targets.test.outputs": ["nx.json", "nx/target-defaults"], "targets.test.executor": ["apps/mobile-buyer/jest.config.ts", "@nx/jest/plugin"], "targets.test.options.cwd": ["apps/mobile-buyer/jest.config.ts", "@nx/jest/plugin"], "targets.test.options.env": ["apps/mobile-buyer/jest.config.ts", "@nx/jest/plugin"], "targets.test.options.command": ["apps/mobile-buyer/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.technologies": ["apps/mobile-buyer/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.technologies.0": ["apps/mobile-buyer/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.description": ["apps/mobile-buyer/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.help": ["apps/mobile-buyer/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.help.command": ["apps/mobile-buyer/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.help.example": ["apps/mobile-buyer/jest.config.ts", "@nx/jest/plugin"], "targets.start": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.start.executor": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.start.continuous": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.serve": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.serve.continuous": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.serve.options": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.serve.executor": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.serve.options.cwd": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.serve.options.args": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.serve.options.command": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.run-ios": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.run-ios.continuous": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.run-ios.options": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.run-ios.executor": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.run-ios.options.cwd": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.run-ios.options.command": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.run-android": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.run-android.continuous": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.run-android.options": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.run-android.executor": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.run-android.options.cwd": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.run-android.options.command": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.export": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.export.options": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.export.cache": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.export.dependsOn": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.export.inputs": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.export.outputs": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.export.executor": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.export.options.cwd": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.export.options.args": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.export.options.command": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.install": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.install.executor": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.prebuild": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.prebuild.executor": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.build": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.build.executor": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.submit": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.submit.options": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.submit.executor": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.submit.options.cwd": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.submit.options.command": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.build-deps": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.build-deps.dependsOn": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.watch-deps": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.watch-deps.continuous": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.watch-deps.dependsOn": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.watch-deps.executor": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.watch-deps.options": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "targets.watch-deps.options.command": ["apps/mobile-buyer/app.json", "@nx/expo/plugin"], "name": ["apps/mobile-buyer/package.json", "nx/core/package-json"], "tags": ["apps/mobile-buyer/package.json", "nx/core/package-json"], "tags.npm:private": ["apps/mobile-buyer/package.json", "nx/core/package-json"], "metadata.targetGroups": ["apps/mobile-buyer/package.json", "nx/core/package-json"], "metadata.js": ["apps/mobile-buyer/package.json", "nx/core/package-json"], "metadata.js.packageName": ["apps/mobile-buyer/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["apps/mobile-buyer/package.json", "nx/core/package-json"], "targets.typecheck.parallelism": ["nx.json", "nx/target-defaults"], "targets.lint.parallelism": ["nx.json", "nx/target-defaults"], "targets.test.dependsOn": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.outputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"]}, "apps/web-admin": {"root": ["apps/web-admin/package.json", "nx/core/package-json"], "projectType": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets": ["apps/web-admin/tsconfig.app.json", "@nx/js/typescript"], "targets.typecheck": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.dependsOn": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.options": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.cache": ["nx.json", "nx/target-defaults"], "targets.typecheck.inputs": ["nx.json", "nx/target-defaults"], "targets.typecheck.outputs": ["apps/web-admin/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.syncGenerators": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.executor": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.options.cwd": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.options.command": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata.technologies": ["apps/web-admin/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.technologies.0": ["apps/web-admin/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.description": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata.help": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata.help.command": ["apps/web-admin/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.help.example": ["apps/web-admin/tsconfig.json", "@nx/js/typescript"], "targets.lint": ["apps/web-admin/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.cache": ["nx.json", "nx/target-defaults"], "targets.lint.options": ["apps/web-admin/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.inputs": ["nx.json", "nx/target-defaults"], "targets.lint.outputs": ["apps/web-admin/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata": ["apps/web-admin/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.executor": ["apps/web-admin/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.options.cwd": ["apps/web-admin/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.options.command": ["apps/web-admin/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.technologies": ["apps/web-admin/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.technologies.0": ["apps/web-admin/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.description": ["apps/web-admin/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help": ["apps/web-admin/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help.command": ["apps/web-admin/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help.example": ["apps/web-admin/eslint.config.mjs", "@nx/eslint/plugin"], "targets.build": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.build.options": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.outputs": ["nx.json", "nx/target-defaults"], "targets.build.metadata": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.build.syncGenerators": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.build.executor": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.build.options.cwd": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.build.options.command": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.technologies": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.technologies.0": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.description": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.help": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.help.command": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.help.example": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.serve": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.serve.continuous": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.serve.options": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.serve.syncGenerators": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.serve.executor": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.serve.options.cwd": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.serve.options.command": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.technologies": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.technologies.0": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.description": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.help": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.help.command": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.help.example": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.deprecated": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.dev": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.dev.continuous": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.dev.options": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.dev.syncGenerators": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.dev.executor": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.dev.options.cwd": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.dev.options.command": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.technologies": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.technologies.0": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.description": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.help": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.help.command": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.help.example": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.preview": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.preview.continuous": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.preview.dependsOn": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.preview.options": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.preview.executor": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.preview.options.cwd": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.preview.options.command": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.technologies": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.technologies.0": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.description": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.help": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.help.command": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.help.example": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static.continuous": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static.executor": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static.options": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static.syncGenerators": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static.options.buildTarget": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static.options.spa": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata.technologies.1": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.build-deps": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.build-deps.dependsOn": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps.continuous": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps.dependsOn": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps.executor": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps.options": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps.options.command": ["apps/web-admin/vite.config.ts", "@nx/vite/plugin"], "targets.test": ["apps/web-admin/jest.config.ts", "@nx/jest/plugin"], "targets.test.options": ["apps/web-admin/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata": ["apps/web-admin/jest.config.ts", "@nx/jest/plugin"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.inputs": ["nx.json", "nx/target-defaults"], "targets.test.outputs": ["nx.json", "nx/target-defaults"], "targets.test.executor": ["apps/web-admin/jest.config.ts", "@nx/jest/plugin"], "targets.test.options.cwd": ["apps/web-admin/jest.config.ts", "@nx/jest/plugin"], "targets.test.options.env": ["apps/web-admin/jest.config.ts", "@nx/jest/plugin"], "targets.test.options.command": ["apps/web-admin/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.technologies": ["apps/web-admin/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.technologies.0": ["apps/web-admin/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.description": ["apps/web-admin/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.help": ["apps/web-admin/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.help.command": ["apps/web-admin/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.help.example": ["apps/web-admin/jest.config.ts", "@nx/jest/plugin"], "name": ["apps/web-admin/package.json", "nx/core/package-json"], "tags": ["apps/web-admin/package.json", "nx/core/package-json"], "tags.npm:private": ["apps/web-admin/package.json", "nx/core/package-json"], "metadata.targetGroups": ["apps/web-admin/package.json", "nx/core/package-json"], "metadata.js": ["apps/web-admin/package.json", "nx/core/package-json"], "metadata.js.packageName": ["apps/web-admin/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["apps/web-admin/package.json", "nx/core/package-json"], "targets.typecheck.parallelism": ["nx.json", "nx/target-defaults"], "targets.lint.parallelism": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.test.dependsOn": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"]}, "apps/web-admin-e2e": {"root": ["apps/web-admin-e2e/package.json", "nx/core/package-json"], "projectType": ["apps/web-admin-e2e/tsconfig.json", "@nx/js/typescript"], "targets": ["apps/web-admin-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck": ["apps/web-admin-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.dependsOn": ["apps/web-admin-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.options": ["apps/web-admin-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.cache": ["nx.json", "nx/target-defaults"], "targets.typecheck.inputs": ["nx.json", "nx/target-defaults"], "targets.typecheck.outputs": ["apps/web-admin-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.syncGenerators": ["apps/web-admin-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata": ["apps/web-admin-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.executor": ["apps/web-admin-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.options.cwd": ["apps/web-admin-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.options.command": ["apps/web-admin-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.technologies": ["apps/web-admin-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.technologies.0": ["apps/web-admin-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.description": ["apps/web-admin-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.help": ["apps/web-admin-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.help.command": ["apps/web-admin-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.help.example": ["apps/web-admin-e2e/tsconfig.json", "@nx/js/typescript"], "targets.lint": ["apps/web-admin-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.cache": ["nx.json", "nx/target-defaults"], "targets.lint.options": ["apps/web-admin-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.inputs": ["nx.json", "nx/target-defaults"], "targets.lint.outputs": ["apps/web-admin-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata": ["apps/web-admin-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.executor": ["apps/web-admin-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.options.cwd": ["apps/web-admin-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.options.command": ["apps/web-admin-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.technologies": ["apps/web-admin-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.technologies.0": ["apps/web-admin-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.description": ["apps/web-admin-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help": ["apps/web-admin-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help.command": ["apps/web-admin-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help.example": ["apps/web-admin-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "metadata.targetGroups": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "metadata.targetGroups.E2E (CI)": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "metadata.targetGroups.E2E (CI).0": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "metadata.targetGroups.E2E (CI).1": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.options": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.metadata": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.dependsOn": ["nx.json", "nx/target-defaults"], "targets.e2e.cache": ["nx.json", "nx/target-defaults"], "targets.e2e.inputs": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.outputs": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.executor": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.options.cwd": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.options.command": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.metadata.technologies": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.metadata.technologies.0": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.metadata.description": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.metadata.help": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.metadata.help.command": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.metadata.help.example": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.options": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.metadata": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.dependsOn": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.cache": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.inputs": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.outputs": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.executor": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.options.cwd": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.options.env": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.options.command": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.metadata.technologies": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.metadata.technologies.0": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.metadata.description": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.metadata.help": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.metadata.help.command": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.metadata.help.example": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.executor": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.cache": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.inputs": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.outputs": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.dependsOn": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.metadata": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.metadata.technologies": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.metadata.technologies.0": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.metadata.description": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.metadata.nonAtomizedTarget": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.metadata.help": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.metadata.help.command": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.metadata.help.example": ["apps/web-admin-e2e/playwright.config.ts", "@nx/playwright/plugin"], "name": ["apps/web-admin-e2e/package.json", "nx/core/package-json"], "tags": ["apps/web-admin-e2e/package.json", "nx/core/package-json"], "tags.npm:private": ["apps/web-admin-e2e/package.json", "nx/core/package-json"], "implicitDependencies": ["apps/web-admin-e2e/package.json", "nx/core/package-json"], "implicitDependencies.@nasaga-monorepo/web-admin": ["apps/web-admin-e2e/package.json", "nx/core/package-json"], "metadata.js": ["apps/web-admin-e2e/package.json", "nx/core/package-json"], "metadata.js.packageName": ["apps/web-admin-e2e/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["apps/web-admin-e2e/package.json", "nx/core/package-json"], "targets.typecheck.parallelism": ["nx.json", "nx/target-defaults"], "targets.lint.parallelism": ["nx.json", "nx/target-defaults"], "targets.e2e.parallelism": ["nx.json", "nx/target-defaults"]}, "apps/web-superadmin": {"root": ["apps/web-superadmin/package.json", "nx/core/package-json"], "projectType": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets": ["apps/web-superadmin/tsconfig.app.json", "@nx/js/typescript"], "targets.typecheck": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.dependsOn": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.options": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.cache": ["nx.json", "nx/target-defaults"], "targets.typecheck.inputs": ["nx.json", "nx/target-defaults"], "targets.typecheck.outputs": ["apps/web-superadmin/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.syncGenerators": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.executor": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.options.cwd": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.options.command": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata.technologies": ["apps/web-superadmin/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.technologies.0": ["apps/web-superadmin/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.description": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata.help": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata.help.command": ["apps/web-superadmin/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.help.example": ["apps/web-superadmin/tsconfig.json", "@nx/js/typescript"], "targets.lint": ["apps/web-superadmin/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.cache": ["nx.json", "nx/target-defaults"], "targets.lint.options": ["apps/web-superadmin/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.inputs": ["nx.json", "nx/target-defaults"], "targets.lint.outputs": ["apps/web-superadmin/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata": ["apps/web-superadmin/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.executor": ["apps/web-superadmin/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.options.cwd": ["apps/web-superadmin/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.options.command": ["apps/web-superadmin/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.technologies": ["apps/web-superadmin/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.technologies.0": ["apps/web-superadmin/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.description": ["apps/web-superadmin/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help": ["apps/web-superadmin/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help.command": ["apps/web-superadmin/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help.example": ["apps/web-superadmin/eslint.config.mjs", "@nx/eslint/plugin"], "targets.build": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.build.options": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.outputs": ["nx.json", "nx/target-defaults"], "targets.build.metadata": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.build.syncGenerators": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.build.executor": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.build.options.cwd": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.build.options.command": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.technologies": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.technologies.0": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.description": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.help": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.help.command": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.help.example": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.serve": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.serve.continuous": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.serve.options": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.serve.syncGenerators": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.serve.executor": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.serve.options.cwd": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.serve.options.command": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.technologies": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.technologies.0": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.description": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.help": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.help.command": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.help.example": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.deprecated": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.dev": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.dev.continuous": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.dev.options": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.dev.syncGenerators": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.dev.executor": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.dev.options.cwd": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.dev.options.command": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.technologies": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.technologies.0": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.description": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.help": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.help.command": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.help.example": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.preview": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.preview.continuous": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.preview.dependsOn": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.preview.options": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.preview.executor": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.preview.options.cwd": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.preview.options.command": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.technologies": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.technologies.0": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.description": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.help": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.help.command": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.help.example": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static.continuous": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static.executor": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static.options": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static.syncGenerators": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static.options.buildTarget": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static.options.spa": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata.technologies.1": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.build-deps": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.build-deps.dependsOn": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps.continuous": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps.dependsOn": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps.executor": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps.options": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps.options.command": ["apps/web-superadmin/vite.config.ts", "@nx/vite/plugin"], "targets.test": ["apps/web-superadmin/jest.config.ts", "@nx/jest/plugin"], "targets.test.options": ["apps/web-superadmin/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata": ["apps/web-superadmin/jest.config.ts", "@nx/jest/plugin"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.inputs": ["nx.json", "nx/target-defaults"], "targets.test.outputs": ["nx.json", "nx/target-defaults"], "targets.test.executor": ["apps/web-superadmin/jest.config.ts", "@nx/jest/plugin"], "targets.test.options.cwd": ["apps/web-superadmin/jest.config.ts", "@nx/jest/plugin"], "targets.test.options.env": ["apps/web-superadmin/jest.config.ts", "@nx/jest/plugin"], "targets.test.options.command": ["apps/web-superadmin/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.technologies": ["apps/web-superadmin/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.technologies.0": ["apps/web-superadmin/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.description": ["apps/web-superadmin/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.help": ["apps/web-superadmin/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.help.command": ["apps/web-superadmin/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.help.example": ["apps/web-superadmin/jest.config.ts", "@nx/jest/plugin"], "name": ["apps/web-superadmin/package.json", "nx/core/package-json"], "tags": ["apps/web-superadmin/package.json", "nx/core/package-json"], "tags.npm:private": ["apps/web-superadmin/package.json", "nx/core/package-json"], "metadata.targetGroups": ["apps/web-superadmin/package.json", "nx/core/package-json"], "metadata.js": ["apps/web-superadmin/package.json", "nx/core/package-json"], "metadata.js.packageName": ["apps/web-superadmin/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["apps/web-superadmin/package.json", "nx/core/package-json"], "targets.typecheck.parallelism": ["nx.json", "nx/target-defaults"], "targets.lint.parallelism": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.test.dependsOn": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"]}, "libs/auth": {"root": ["libs/auth/package.json", "nx/core/package-json"], "projectType": ["libs/auth/tsconfig.spec.json", "@nx/js/typescript"], "targets": ["libs/auth/tsconfig.json", "@nx/js/typescript"], "targets.typecheck": ["libs/auth/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.dependsOn": ["libs/auth/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.options": ["libs/auth/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.cache": ["nx.json", "nx/target-defaults"], "targets.typecheck.inputs": ["nx.json", "nx/target-defaults"], "targets.typecheck.outputs": ["libs/auth/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.syncGenerators": ["libs/auth/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata": ["libs/auth/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.executor": ["libs/auth/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.options.cwd": ["libs/auth/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.options.command": ["libs/auth/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.technologies": ["libs/auth/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.technologies.0": ["libs/auth/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.description": ["libs/auth/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.help": ["libs/auth/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.help.command": ["libs/auth/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.help.example": ["libs/auth/tsconfig.json", "@nx/js/typescript"], "targets.watch-deps": ["libs/auth/tsconfig.lib.json", "@nx/js/typescript"], "targets.watch-deps.continuous": ["libs/auth/tsconfig.lib.json", "@nx/js/typescript"], "targets.watch-deps.dependsOn": ["libs/auth/tsconfig.lib.json", "@nx/js/typescript"], "targets.watch-deps.executor": ["libs/auth/tsconfig.lib.json", "@nx/js/typescript"], "targets.watch-deps.options": ["libs/auth/tsconfig.lib.json", "@nx/js/typescript"], "targets.watch-deps.options.command": ["libs/auth/tsconfig.lib.json", "@nx/js/typescript"], "targets.lint": ["libs/auth/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.cache": ["nx.json", "nx/target-defaults"], "targets.lint.options": ["libs/auth/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.inputs": ["nx.json", "nx/target-defaults"], "targets.lint.outputs": ["libs/auth/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata": ["libs/auth/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.executor": ["libs/auth/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.options.cwd": ["libs/auth/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.options.command": ["libs/auth/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.technologies": ["libs/auth/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.technologies.0": ["libs/auth/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.description": ["libs/auth/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help": ["libs/auth/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help.command": ["libs/auth/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help.example": ["libs/auth/eslint.config.mjs", "@nx/eslint/plugin"], "targets.test": ["libs/auth/jest.config.ts", "@nx/jest/plugin"], "targets.test.options": ["libs/auth/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata": ["libs/auth/jest.config.ts", "@nx/jest/plugin"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.inputs": ["nx.json", "nx/target-defaults"], "targets.test.outputs": ["nx.json", "nx/target-defaults"], "targets.test.executor": ["libs/auth/jest.config.ts", "@nx/jest/plugin"], "targets.test.options.cwd": ["libs/auth/jest.config.ts", "@nx/jest/plugin"], "targets.test.options.env": ["libs/auth/jest.config.ts", "@nx/jest/plugin"], "targets.test.options.command": ["libs/auth/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.technologies": ["libs/auth/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.technologies.0": ["libs/auth/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.description": ["libs/auth/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.help": ["libs/auth/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.help.command": ["libs/auth/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.help.example": ["libs/auth/jest.config.ts", "@nx/jest/plugin"], "name": ["libs/auth/package.json", "nx/core/package-json"], "sourceRoot": ["libs/auth/package.json", "nx/core/package-json"], "tags": ["libs/auth/package.json", "nx/core/package-json"], "tags.npm:private": ["libs/auth/package.json", "nx/core/package-json"], "metadata.targetGroups": ["libs/auth/package.json", "nx/core/package-json"], "metadata.js": ["libs/auth/package.json", "nx/core/package-json"], "metadata.js.packageName": ["libs/auth/package.json", "nx/core/package-json"], "metadata.js.packageExports": ["libs/auth/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["libs/auth/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["libs/auth/package.json", "nx/core/package-json"], "targets.build": ["libs/auth/package.json", "nx/core/package-json"], "targets.build.executor": ["libs/auth/package.json", "nx/core/package-json"], "targets.build.outputs": ["libs/auth/package.json", "nx/core/package-json"], "targets.build.options": ["libs/auth/package.json", "nx/core/package-json"], "targets.build.options.outputPath": ["libs/auth/package.json", "nx/core/package-json"], "targets.build.options.main": ["libs/auth/package.json", "nx/core/package-json"], "targets.build.options.tsConfig": ["libs/auth/package.json", "nx/core/package-json"], "targets.build.options.skipTypeCheck": ["libs/auth/package.json", "nx/core/package-json"], "targets.build.options.stripLeadingPaths": ["libs/auth/package.json", "nx/core/package-json"], "targets.typecheck.parallelism": ["nx.json", "nx/target-defaults"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.lint.parallelism": ["nx.json", "nx/target-defaults"], "targets.test.dependsOn": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"]}, "libs/models": {"root": ["libs/models/package.json", "nx/core/package-json"], "projectType": ["libs/models/tsconfig.spec.json", "@nx/js/typescript"], "targets": ["libs/models/tsconfig.json", "@nx/js/typescript"], "targets.typecheck": ["libs/models/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.dependsOn": ["libs/models/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.options": ["libs/models/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.cache": ["nx.json", "nx/target-defaults"], "targets.typecheck.inputs": ["nx.json", "nx/target-defaults"], "targets.typecheck.outputs": ["libs/models/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.syncGenerators": ["libs/models/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata": ["libs/models/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.executor": ["libs/models/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.options.cwd": ["libs/models/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.options.command": ["libs/models/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.technologies": ["libs/models/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.technologies.0": ["libs/models/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.description": ["libs/models/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.help": ["libs/models/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.help.command": ["libs/models/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.help.example": ["libs/models/tsconfig.json", "@nx/js/typescript"], "targets.watch-deps": ["libs/models/tsconfig.lib.json", "@nx/js/typescript"], "targets.watch-deps.continuous": ["libs/models/tsconfig.lib.json", "@nx/js/typescript"], "targets.watch-deps.dependsOn": ["libs/models/tsconfig.lib.json", "@nx/js/typescript"], "targets.watch-deps.executor": ["libs/models/tsconfig.lib.json", "@nx/js/typescript"], "targets.watch-deps.options": ["libs/models/tsconfig.lib.json", "@nx/js/typescript"], "targets.watch-deps.options.command": ["libs/models/tsconfig.lib.json", "@nx/js/typescript"], "targets.lint": ["libs/models/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.cache": ["nx.json", "nx/target-defaults"], "targets.lint.options": ["libs/models/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.inputs": ["nx.json", "nx/target-defaults"], "targets.lint.outputs": ["libs/models/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata": ["libs/models/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.executor": ["libs/models/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.options.cwd": ["libs/models/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.options.command": ["libs/models/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.technologies": ["libs/models/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.technologies.0": ["libs/models/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.description": ["libs/models/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help": ["libs/models/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help.command": ["libs/models/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help.example": ["libs/models/eslint.config.mjs", "@nx/eslint/plugin"], "targets.test": ["libs/models/jest.config.ts", "@nx/jest/plugin"], "targets.test.options": ["libs/models/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata": ["libs/models/jest.config.ts", "@nx/jest/plugin"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.inputs": ["nx.json", "nx/target-defaults"], "targets.test.outputs": ["nx.json", "nx/target-defaults"], "targets.test.executor": ["libs/models/jest.config.ts", "@nx/jest/plugin"], "targets.test.options.cwd": ["libs/models/jest.config.ts", "@nx/jest/plugin"], "targets.test.options.env": ["libs/models/jest.config.ts", "@nx/jest/plugin"], "targets.test.options.command": ["libs/models/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.technologies": ["libs/models/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.technologies.0": ["libs/models/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.description": ["libs/models/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.help": ["libs/models/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.help.command": ["libs/models/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.help.example": ["libs/models/jest.config.ts", "@nx/jest/plugin"], "name": ["libs/models/package.json", "nx/core/package-json"], "sourceRoot": ["libs/models/package.json", "nx/core/package-json"], "tags": ["libs/models/package.json", "nx/core/package-json"], "tags.npm:private": ["libs/models/package.json", "nx/core/package-json"], "metadata.targetGroups": ["libs/models/package.json", "nx/core/package-json"], "metadata.js": ["libs/models/package.json", "nx/core/package-json"], "metadata.js.packageName": ["libs/models/package.json", "nx/core/package-json"], "metadata.js.packageExports": ["libs/models/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["libs/models/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["libs/models/package.json", "nx/core/package-json"], "targets.build": ["libs/models/package.json", "nx/core/package-json"], "targets.build.executor": ["libs/models/package.json", "nx/core/package-json"], "targets.build.outputs": ["libs/models/package.json", "nx/core/package-json"], "targets.build.options": ["libs/models/package.json", "nx/core/package-json"], "targets.build.options.outputPath": ["libs/models/package.json", "nx/core/package-json"], "targets.build.options.main": ["libs/models/package.json", "nx/core/package-json"], "targets.build.options.tsConfig": ["libs/models/package.json", "nx/core/package-json"], "targets.build.options.skipTypeCheck": ["libs/models/package.json", "nx/core/package-json"], "targets.build.options.stripLeadingPaths": ["libs/models/package.json", "nx/core/package-json"], "targets.typecheck.parallelism": ["nx.json", "nx/target-defaults"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.lint.parallelism": ["nx.json", "nx/target-defaults"], "targets.test.dependsOn": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"]}, "libs/ui": {"root": ["libs/ui/package.json", "nx/core/package-json"], "projectType": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets": ["libs/ui/tsconfig.json", "@nx/js/typescript"], "targets.typecheck": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.dependsOn": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.options": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.cache": ["nx.json", "nx/target-defaults"], "targets.typecheck.inputs": ["nx.json", "nx/target-defaults"], "targets.typecheck.outputs": ["libs/ui/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.syncGenerators": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.executor": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.options.cwd": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.options.command": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata.technologies": ["libs/ui/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.technologies.0": ["libs/ui/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.description": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata.help": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata.help.command": ["libs/ui/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.help.example": ["libs/ui/tsconfig.json", "@nx/js/typescript"], "targets.watch-deps": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps.continuous": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps.dependsOn": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps.executor": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps.options": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps.options.command": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.lint": ["libs/ui/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.cache": ["nx.json", "nx/target-defaults"], "targets.lint.options": ["libs/ui/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.inputs": ["nx.json", "nx/target-defaults"], "targets.lint.outputs": ["libs/ui/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata": ["libs/ui/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.executor": ["libs/ui/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.options.cwd": ["libs/ui/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.options.command": ["libs/ui/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.technologies": ["libs/ui/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.technologies.0": ["libs/ui/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.description": ["libs/ui/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help": ["libs/ui/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help.command": ["libs/ui/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help.example": ["libs/ui/eslint.config.mjs", "@nx/eslint/plugin"], "targets.build": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.build.options": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.outputs": ["nx.json", "nx/target-defaults"], "targets.build.metadata": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.build.syncGenerators": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.build.executor": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.build.options.cwd": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.build.options.command": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.technologies.1": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.description": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.help": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.serve": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.serve.continuous": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.serve.options": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.serve.syncGenerators": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.serve.executor": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.serve.options.cwd": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.serve.options.command": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.technologies": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.technologies.0": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.description": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.help": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.help.command": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.help.example": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.deprecated": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.dev": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.dev.continuous": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.dev.options": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.dev.syncGenerators": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.dev.executor": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.dev.options.cwd": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.dev.options.command": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.technologies": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.technologies.0": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.description": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.help": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.help.command": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.help.example": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.preview": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.preview.continuous": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.preview.dependsOn": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.preview.options": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.preview.executor": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.preview.options.cwd": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.preview.options.command": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.technologies": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.technologies.0": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.description": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.help": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.help.command": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.help.example": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static.continuous": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static.executor": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static.options": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static.syncGenerators": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static.options.buildTarget": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static.options.spa": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata.technologies.1": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.build-deps": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.build-deps.dependsOn": ["libs/ui/vite.config.ts", "@nx/vite/plugin"], "targets.test": ["libs/ui/jest.config.ts", "@nx/jest/plugin"], "targets.test.options": ["libs/ui/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata": ["libs/ui/jest.config.ts", "@nx/jest/plugin"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.inputs": ["nx.json", "nx/target-defaults"], "targets.test.outputs": ["nx.json", "nx/target-defaults"], "targets.test.executor": ["libs/ui/jest.config.ts", "@nx/jest/plugin"], "targets.test.options.cwd": ["libs/ui/jest.config.ts", "@nx/jest/plugin"], "targets.test.options.env": ["libs/ui/jest.config.ts", "@nx/jest/plugin"], "targets.test.options.command": ["libs/ui/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.technologies": ["libs/ui/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.technologies.0": ["libs/ui/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.description": ["libs/ui/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.help": ["libs/ui/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.help.command": ["libs/ui/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.help.example": ["libs/ui/jest.config.ts", "@nx/jest/plugin"], "name": ["libs/ui/package.json", "nx/core/package-json"], "tags": ["libs/ui/package.json", "nx/core/package-json"], "tags.npm:public": ["libs/ui/package.json", "nx/core/package-json"], "metadata.targetGroups": ["libs/ui/package.json", "nx/core/package-json"], "metadata.js": ["libs/ui/package.json", "nx/core/package-json"], "metadata.js.packageName": ["libs/ui/package.json", "nx/core/package-json"], "metadata.js.packageExports": ["libs/ui/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["libs/ui/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["libs/ui/package.json", "nx/core/package-json"], "targets.nx-release-publish": ["libs/ui/package.json", "nx/core/package-json"], "targets.nx-release-publish.executor": ["libs/ui/package.json", "nx/core/package-json"], "targets.nx-release-publish.dependsOn": ["libs/ui/package.json", "nx/core/package-json"], "targets.nx-release-publish.options": ["libs/ui/package.json", "nx/core/package-json"], "targets.typecheck.parallelism": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.lint.parallelism": ["nx.json", "nx/target-defaults"], "targets.test.dependsOn": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"]}, "libs/utils": {"root": ["libs/utils/package.json", "nx/core/package-json"], "projectType": ["libs/utils/tsconfig.spec.json", "@nx/js/typescript"], "targets": ["libs/utils/tsconfig.json", "@nx/js/typescript"], "targets.typecheck": ["libs/utils/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.dependsOn": ["libs/utils/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.options": ["libs/utils/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.cache": ["nx.json", "nx/target-defaults"], "targets.typecheck.inputs": ["nx.json", "nx/target-defaults"], "targets.typecheck.outputs": ["libs/utils/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.syncGenerators": ["libs/utils/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata": ["libs/utils/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.executor": ["libs/utils/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.options.cwd": ["libs/utils/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.options.command": ["libs/utils/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.technologies": ["libs/utils/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.technologies.0": ["libs/utils/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.description": ["libs/utils/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.help": ["libs/utils/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.help.command": ["libs/utils/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.help.example": ["libs/utils/tsconfig.json", "@nx/js/typescript"], "targets.watch-deps": ["libs/utils/tsconfig.lib.json", "@nx/js/typescript"], "targets.watch-deps.continuous": ["libs/utils/tsconfig.lib.json", "@nx/js/typescript"], "targets.watch-deps.dependsOn": ["libs/utils/tsconfig.lib.json", "@nx/js/typescript"], "targets.watch-deps.executor": ["libs/utils/tsconfig.lib.json", "@nx/js/typescript"], "targets.watch-deps.options": ["libs/utils/tsconfig.lib.json", "@nx/js/typescript"], "targets.watch-deps.options.command": ["libs/utils/tsconfig.lib.json", "@nx/js/typescript"], "targets.lint": ["libs/utils/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.cache": ["nx.json", "nx/target-defaults"], "targets.lint.options": ["libs/utils/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.inputs": ["nx.json", "nx/target-defaults"], "targets.lint.outputs": ["libs/utils/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata": ["libs/utils/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.executor": ["libs/utils/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.options.cwd": ["libs/utils/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.options.command": ["libs/utils/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.technologies": ["libs/utils/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.technologies.0": ["libs/utils/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.description": ["libs/utils/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help": ["libs/utils/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help.command": ["libs/utils/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help.example": ["libs/utils/eslint.config.mjs", "@nx/eslint/plugin"], "targets.test": ["libs/utils/jest.config.ts", "@nx/jest/plugin"], "targets.test.options": ["libs/utils/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata": ["libs/utils/jest.config.ts", "@nx/jest/plugin"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.inputs": ["nx.json", "nx/target-defaults"], "targets.test.outputs": ["nx.json", "nx/target-defaults"], "targets.test.executor": ["libs/utils/jest.config.ts", "@nx/jest/plugin"], "targets.test.options.cwd": ["libs/utils/jest.config.ts", "@nx/jest/plugin"], "targets.test.options.env": ["libs/utils/jest.config.ts", "@nx/jest/plugin"], "targets.test.options.command": ["libs/utils/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.technologies": ["libs/utils/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.technologies.0": ["libs/utils/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.description": ["libs/utils/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.help": ["libs/utils/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.help.command": ["libs/utils/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.help.example": ["libs/utils/jest.config.ts", "@nx/jest/plugin"], "name": ["libs/utils/package.json", "nx/core/package-json"], "sourceRoot": ["libs/utils/package.json", "nx/core/package-json"], "tags": ["libs/utils/package.json", "nx/core/package-json"], "tags.npm:private": ["libs/utils/package.json", "nx/core/package-json"], "metadata.targetGroups": ["libs/utils/package.json", "nx/core/package-json"], "metadata.js": ["libs/utils/package.json", "nx/core/package-json"], "metadata.js.packageName": ["libs/utils/package.json", "nx/core/package-json"], "metadata.js.packageExports": ["libs/utils/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["libs/utils/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["libs/utils/package.json", "nx/core/package-json"], "targets.build": ["libs/utils/package.json", "nx/core/package-json"], "targets.build.executor": ["libs/utils/package.json", "nx/core/package-json"], "targets.build.outputs": ["libs/utils/package.json", "nx/core/package-json"], "targets.build.options": ["libs/utils/package.json", "nx/core/package-json"], "targets.build.options.outputPath": ["libs/utils/package.json", "nx/core/package-json"], "targets.build.options.main": ["libs/utils/package.json", "nx/core/package-json"], "targets.build.options.tsConfig": ["libs/utils/package.json", "nx/core/package-json"], "targets.build.options.skipTypeCheck": ["libs/utils/package.json", "nx/core/package-json"], "targets.build.options.stripLeadingPaths": ["libs/utils/package.json", "nx/core/package-json"], "targets.typecheck.parallelism": ["nx.json", "nx/target-defaults"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.lint.parallelism": ["nx.json", "nx/target-defaults"], "targets.test.dependsOn": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"]}}