# **Coding Standards**

## **Critical Fullstack Rules**

* **Type Sharing:** Always define shared TypeScript types and interfaces in packages/types and import them from there. Never duplicate type definitions across applications.  
* **API Calls:** Never make direct HTTP calls (fetch or axios.create instances) from components or pages/screens. Always use the dedicated service layer (e.g., propertyService.ts, userService.ts) from packages/utils or app-specific services/ folders to encapsulate API interactions.  
* **Environment Variables:** Access environment variables only through a centralized configuration object or service within each application. Never access process.env (Node.js) or Expo.Constants.manifest.extra (Expo) directly within application logic to ensure consistency and testability.  
* **Error Handling:** All API routes on the backend must use the standard error handler defined in apps/backend/src/middleware/error.middleware.ts (to be created). Frontend API calls must utilize the global error handling defined in packages/utils/src/apiClient.ts.  
* **State Updates:** Never mutate state directly in React/React Native. Always use proper state management patterns (e.g., setState for useState, immutability helpers for Zustand actions) to ensure predictable state changes and prevent bugs.  
* **Firebase SDK Usage:**  
  * **Client SDKs:** Use Firebase Client SDKs (Auth, Firestore, Storage) directly in frontend applications for real-time data synchronization and client-side operations.  
  * **Admin SDK:** Use Firebase Admin SDK only in the backend Node.js/Express application for privileged operations (e.g., setting user roles, modifying sensitive data, triggering Cloud Messaging).  
* **UUID Generation:** For unique client-side IDs (e.g., before saving a new document to Firestore), use a consistent UUID generation library (e.g., uuid npm package).

## **Naming Conventions**

| Element | Frontend | Backend | Example |
| :---- | :---- | :---- | :---- |
| Components | PascalCase | \- | UserProfile.tsx |
| Hooks | camelCase with 'use' prefix | \- | useAuth.ts |
| Pages/Screens | PascalCase | \- | LoginPage.tsx, HomeScreen.tsx |
| Services | camelCase ending with Service | camelCase ending with Service | userService.ts, propertyService.ts |
| API Routes | \- | kebab-case | /api/user-profile |
| Database Tables (Collections) | \- | snake\_case (Firestore collections are typically camelCase or kebab-case by convention, but mapping to snake\_case in code if interacting with a relational database or for consistency with existing backend patterns) | user\_profiles (for logical naming, Firestore collections typically users, properties) |
| Types/Interfaces | PascalCase | PascalCase | User, Property |
