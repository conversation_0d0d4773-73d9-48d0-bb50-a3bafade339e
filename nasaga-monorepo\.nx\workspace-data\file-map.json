{"version": "6.0", "nxVersion": "21.3.9", "pathMappings": {}, "nxJsonPlugins": [{"name": "@nx/js/typescript", "options": {"typecheck": {"targetName": "typecheck"}, "build": {"targetName": "build", "configName": "tsconfig.lib.json", "buildDepsName": "build-deps", "watchDepsName": "watch-deps"}}}, {"name": "@nx/react/router-plugin", "options": {"buildTargetName": "build", "devTargetName": "dev", "startTargetName": "start", "watchDepsTargetName": "watch-deps", "buildDepsTargetName": "build-deps", "typecheckTargetName": "typecheck"}}, {"name": "@nx/eslint/plugin", "options": {"targetName": "lint"}}, {"name": "@nx/vite/plugin", "options": {"buildTargetName": "build", "testTargetName": "test", "serveTargetName": "serve", "devTargetName": "dev", "previewTargetName": "preview", "serveStaticTargetName": "serve-static", "typecheckTargetName": "typecheck", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}, {"name": "@nx/playwright/plugin", "options": {"targetName": "e2e"}}, {"name": "@nx/jest/plugin", "options": {"targetName": "test"}}, {"name": "@nx/expo/plugin", "options": {"startTargetName": "start", "buildTargetName": "build", "prebuildTargetName": "prebuild", "serveTargetName": "serve", "installTargetName": "install", "exportTargetName": "export", "submitTargetName": "submit", "runIosTargetName": "run-ios", "runAndroidTargetName": "run-android", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}, {"name": "@nx/webpack/plugin", "options": {"buildTargetName": "build", "serveTargetName": "serve", "previewTargetName": "preview", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}], "fileMap": {"nonProjectFiles": [{"file": "firestore.rules", "hash": "11866757861816226498"}, {"file": "static/favicon.ico", "hash": "9303420814833116677"}, {"file": "jest.preset.js", "hash": "9430166341120122740"}, {"file": ".github/workflows/deploy-preview.yml", "hash": "1869459357890926512"}, {"file": "nx.json", "hash": "13466886889099338533"}, {"file": "jest.config.ts", "hash": "6870352021923392442"}, {"file": ".firebaserc", "hash": "2650048471544542890"}, {"file": "static/styles.css", "hash": "5135390112669314880"}, {"file": ".giti<PERSON>re", "hash": "871900924519603864"}, {"file": "README.md", "hash": "11875773801060702759"}, {"file": "package-lock.json", "hash": "11234970907174843910"}, {"file": "error.log", "hash": "6007351649531648271"}, {"file": ".husky/commit-msg", "hash": "3535959559828207167"}, {"file": "commitlint.config.js", "hash": "12943710095308013050"}, {"file": "packages/.gitkeep", "hash": "3244421341483603138"}, {"file": "package.json", "hash": "15984622711012848219"}, {"file": "eslint.config.mjs", "hash": "4618192569345483032"}, {"file": ".vscode/extensions.json", "hash": "11309362834336059819"}, {"file": "static/styles.js", "hash": "7569025066195547382"}, {"file": ".github/workflows/pr-checks.yml", "hash": "12388881209682220421"}, {"file": "static/main.js", "hash": "10543383890141206284"}, {"file": ".prettier<PERSON>", "hash": "16267754514737964994"}, {"file": ".husky/pre-commit", "hash": "11200534014804190460"}, {"file": "static/environment.js", "hash": "10609292184959405647"}, {"file": "dependency-graph.html", "hash": "12560290355241016586"}, {"file": "static/runtime.js", "hash": "2835949357856416383"}, {"file": "tools/scripts/eas-build-post-install.mjs", "hash": "18390423807559123090"}, {"file": "tsconfig.json", "hash": "14053782038406953560"}, {"file": ".github/workflows/build-mobile.yml", "hash": "7349376261686727473"}, {"file": ".prettieri<PERSON>re", "hash": "14688272390704683536"}, {"file": "tsconfig.base.json", "hash": "8900268571896142108"}, {"file": "babel.config.json", "hash": "5546405625092116747"}, {"file": "firebase.json", "hash": "16215763529438189218"}, {"file": ".github/workflows/deploy-production.yml", "hash": "1203573503380319279"}], "projectFileMap": {"@nasaga-monorepo/models": [{"file": "libs/models/.spec.swcrc", "hash": "18201067170784284381"}, {"file": "libs/models/.swcrc", "hash": "3236768094882387443"}, {"file": "libs/models/README.md", "hash": "17799089924132604789"}, {"file": "libs/models/eslint.config.mjs", "hash": "4313095032450400554", "deps": [["npm:jsonc-eslint-parser", "dynamic"]]}, {"file": "libs/models/jest.config.ts", "hash": "4141506226773631544"}, {"file": "libs/models/package.json", "hash": "14785378359001241608", "deps": ["npm:@swc/helpers"]}, {"file": "libs/models/src/index.ts", "hash": "2363507496233999047"}, {"file": "libs/models/src/lib/models.spec.ts", "hash": "12148721337196203520"}, {"file": "libs/models/src/lib/models.ts", "hash": "13522761443588667083"}, {"file": "libs/models/tsconfig.json", "hash": "12834038490315927699"}, {"file": "libs/models/tsconfig.lib.json", "hash": "17233988772328281602"}, {"file": "libs/models/tsconfig.spec.json", "hash": "12971962995897902409"}], "@nasaga-monorepo/mobile-buyer": [{"file": "apps/mobile-buyer/.babelrc.js", "hash": "231774045518377938"}, {"file": "apps/mobile-buyer/app.json", "hash": "6319097293810123944"}, {"file": "apps/mobile-buyer/assets/adaptive-icon.png", "hash": "16102135492459408350"}, {"file": "apps/mobile-buyer/assets/favicon.png", "hash": "10556792184362654649"}, {"file": "apps/mobile-buyer/assets/icon.png", "hash": "14252482080276080187"}, {"file": "apps/mobile-buyer/assets/splash.png", "hash": "16493559885079032523"}, {"file": "apps/mobile-buyer/eas.json", "hash": "6330161018993400891"}, {"file": "apps/mobile-buyer/eslint.config.mjs", "hash": "4549399322512926610", "deps": ["npm:@nx/eslint-plugin"]}, {"file": "apps/mobile-buyer/index.js", "hash": "1184010334168630581", "deps": ["npm:expo"]}, {"file": "apps/mobile-buyer/jest.config.ts", "hash": "5151918667861967766", "deps": [["npm:jest-expo", "dynamic"]]}, {"file": "apps/mobile-buyer/metro.config.js", "hash": "2922723032447638085", "deps": ["npm:@nx/expo", "npm:@expo/metro-config", "npm:metro-config", "npm:react-native-svg-transformer"]}, {"file": "apps/mobile-buyer/package.json", "hash": "9348583059420496359"}, {"file": "apps/mobile-buyer/src/app/App.spec.tsx", "hash": "5072730789784802033", "deps": ["npm:react", "npm:@testing-library/react-native"]}, {"file": "apps/mobile-buyer/src/app/App.tsx", "hash": "14325773300223367297", "deps": ["npm:react", "npm:react-native", "npm:react-native-svg"]}, {"file": "apps/mobile-buyer/src/test-setup.ts", "hash": "9919311210714806624", "deps": ["npm:@testing-library/jest-native"]}, {"file": "apps/mobile-buyer/tsconfig.app.json", "hash": "8967954828260004517"}, {"file": "apps/mobile-buyer/tsconfig.json", "hash": "9214820933160915425"}, {"file": "apps/mobile-buyer/tsconfig.spec.json", "hash": "10834278139151176693"}], "@nasaga-monorepo/auth": [{"file": "libs/auth/.spec.swcrc", "hash": "18201067170784284381"}, {"file": "libs/auth/.swcrc", "hash": "3236768094882387443"}, {"file": "libs/auth/README.md", "hash": "5126697487225917289"}, {"file": "libs/auth/eslint.config.mjs", "hash": "4313095032450400554", "deps": [["npm:jsonc-eslint-parser", "dynamic"]]}, {"file": "libs/auth/jest.config.ts", "hash": "2065412015392748672"}, {"file": "libs/auth/package.json", "hash": "1311247968395505931", "deps": ["npm:@swc/helpers"]}, {"file": "libs/auth/src/index.ts", "hash": "13483878758413506474"}, {"file": "libs/auth/src/lib/auth.spec.ts", "hash": "10761452752491445597"}, {"file": "libs/auth/src/lib/auth.ts", "hash": "6890438946885032332"}, {"file": "libs/auth/tsconfig.json", "hash": "12834038490315927699"}, {"file": "libs/auth/tsconfig.lib.json", "hash": "17233988772328281602"}, {"file": "libs/auth/tsconfig.spec.json", "hash": "12971962995897902409"}], "@nasaga-monorepo/utils": [{"file": "libs/utils/.spec.swcrc", "hash": "18201067170784284381"}, {"file": "libs/utils/.swcrc", "hash": "3236768094882387443"}, {"file": "libs/utils/README.md", "hash": "3816604565008484386"}, {"file": "libs/utils/eslint.config.mjs", "hash": "4313095032450400554", "deps": [["npm:jsonc-eslint-parser", "dynamic"]]}, {"file": "libs/utils/jest.config.ts", "hash": "14554055299194173366"}, {"file": "libs/utils/package.json", "hash": "8170807899817773011", "deps": ["npm:@swc/helpers"]}, {"file": "libs/utils/src/index.ts", "hash": "1424520165591673030"}, {"file": "libs/utils/src/lib/utils.spec.ts", "hash": "17453479995195199863"}, {"file": "libs/utils/src/lib/utils.ts", "hash": "17287710670217841263"}, {"file": "libs/utils/tsconfig.json", "hash": "12834038490315927699"}, {"file": "libs/utils/tsconfig.lib.json", "hash": "17233988772328281602"}, {"file": "libs/utils/tsconfig.spec.json", "hash": "12971962995897902409"}], "@nasaga-monorepo/ui": [{"file": "libs/ui/.babelrc", "hash": "2946678488919325452"}, {"file": "libs/ui/README.md", "hash": "14894241336626855831"}, {"file": "libs/ui/eslint.config.mjs", "hash": "8789163670399984295", "deps": ["npm:@nx/eslint-plugin"]}, {"file": "libs/ui/jest.config.ts", "hash": "17429197388015648977"}, {"file": "libs/ui/package.json", "hash": "1567830145813575936"}, {"file": "libs/ui/src/index.ts", "hash": "5977848407072712979"}, {"file": "libs/ui/src/lib/ui.spec.tsx", "hash": "7677546453323585860", "deps": ["npm:@testing-library/react"]}, {"file": "libs/ui/src/lib/ui.tsx", "hash": "17549761452083750797"}, {"file": "libs/ui/tsconfig.json", "hash": "1138660533399177677"}, {"file": "libs/ui/tsconfig.lib.json", "hash": "15708145348497858799"}, {"file": "libs/ui/tsconfig.spec.json", "hash": "200392398545240393"}, {"file": "libs/ui/vite.config.ts", "hash": "13183280991152883617", "deps": ["npm:vite", "npm:@vitejs/plugin-react", "npm:vite-plugin-dts"]}], "@nasaga-monorepo/mobile-agent": [{"file": "apps/mobile-agent/.babelrc.js", "hash": "231774045518377938"}, {"file": "apps/mobile-agent/app.json", "hash": "16930853411394718298"}, {"file": "apps/mobile-agent/assets/adaptive-icon.png", "hash": "16102135492459408350"}, {"file": "apps/mobile-agent/assets/favicon.png", "hash": "10556792184362654649"}, {"file": "apps/mobile-agent/assets/icon.png", "hash": "14252482080276080187"}, {"file": "apps/mobile-agent/assets/splash.png", "hash": "16493559885079032523"}, {"file": "apps/mobile-agent/eas.json", "hash": "6330161018993400891"}, {"file": "apps/mobile-agent/eslint.config.mjs", "hash": "4549399322512926610", "deps": ["npm:@nx/eslint-plugin"]}, {"file": "apps/mobile-agent/index.js", "hash": "1184010334168630581", "deps": ["npm:expo"]}, {"file": "apps/mobile-agent/jest.config.ts", "hash": "10254722369530945867", "deps": [["npm:jest-expo", "dynamic"]]}, {"file": "apps/mobile-agent/metro.config.js", "hash": "13288203599039829993", "deps": ["npm:@nx/expo", "npm:@expo/metro-config", "npm:metro-config", "npm:react-native-svg-transformer"]}, {"file": "apps/mobile-agent/package.json", "hash": "9699701234321643546"}, {"file": "apps/mobile-agent/src/app/App.spec.tsx", "hash": "5072730789784802033", "deps": ["npm:react", "npm:@testing-library/react-native"]}, {"file": "apps/mobile-agent/src/app/App.tsx", "hash": "7987569310274201435", "deps": ["npm:react", "npm:react-native", "npm:react-native-svg"]}, {"file": "apps/mobile-agent/src/test-setup.ts", "hash": "9919311210714806624", "deps": ["npm:@testing-library/jest-native"]}, {"file": "apps/mobile-agent/tsconfig.app.json", "hash": "8967954828260004517"}, {"file": "apps/mobile-agent/tsconfig.json", "hash": "9214820933160915425"}, {"file": "apps/mobile-agent/tsconfig.spec.json", "hash": "10834278139151176693"}], "@nasaga-monorepo/web-superadmin": [{"file": "apps/web-superadmin/eslint.config.mjs", "hash": "8789163670399984295", "deps": ["npm:@nx/eslint-plugin"]}, {"file": "apps/web-superadmin/index.html", "hash": "15720753138111289419"}, {"file": "apps/web-superadmin/jest.config.ts", "hash": "13489988045630039488"}, {"file": "apps/web-superadmin/package.json", "hash": "12738662649613754691"}, {"file": "apps/web-superadmin/postcss.config.js", "hash": "2759402739052110475"}, {"file": "apps/web-superadmin/public/favicon.ico", "hash": "9303420814833116677"}, {"file": "apps/web-superadmin/src/app/app.spec.tsx", "hash": "13545115907696129348", "deps": ["npm:@testing-library/react", "npm:react-router-dom"]}, {"file": "apps/web-superadmin/src/app/app.tsx", "hash": "10445582466461229034", "deps": ["npm:react-router-dom"]}, {"file": "apps/web-superadmin/src/app/nx-welcome.tsx", "hash": "2348102959109701629"}, {"file": "apps/web-superadmin/src/assets/.gitkeep", "hash": "3244421341483603138"}, {"file": "apps/web-superadmin/src/main.tsx", "hash": "6494962159756833686", "deps": ["npm:react", "npm:react-router-dom", "npm:react-dom"]}, {"file": "apps/web-superadmin/src/styles.css", "hash": "9540575453073076294"}, {"file": "apps/web-superadmin/tailwind.config.js", "hash": "13090734457927965885", "deps": ["npm:@nx/react"]}, {"file": "apps/web-superadmin/tsconfig.app.json", "hash": "2856619297212668936"}, {"file": "apps/web-superadmin/tsconfig.json", "hash": "9240132813632071828"}, {"file": "apps/web-superadmin/tsconfig.spec.json", "hash": "6057203337721981610"}, {"file": "apps/web-superadmin/vite.config.ts", "hash": "12210543790492325396", "deps": ["npm:vite", "npm:@vitejs/plugin-react"]}], "@nasaga-monorepo/web-admin": [{"file": "apps/web-admin/eslint.config.mjs", "hash": "8789163670399984295", "deps": ["npm:@nx/eslint-plugin"]}, {"file": "apps/web-admin/index.html", "hash": "12302201515018741901"}, {"file": "apps/web-admin/jest.config.ts", "hash": "13909869933152599526"}, {"file": "apps/web-admin/package.json", "hash": "2986456316504286600"}, {"file": "apps/web-admin/postcss.config.js", "hash": "2759402739052110475"}, {"file": "apps/web-admin/public/favicon.ico", "hash": "9303420814833116677"}, {"file": "apps/web-admin/src/app/app.spec.tsx", "hash": "404361484111038727", "deps": ["npm:@testing-library/react", "npm:react-router-dom"]}, {"file": "apps/web-admin/src/app/app.tsx", "hash": "15480920462313836672", "deps": ["npm:react-router-dom"]}, {"file": "apps/web-admin/src/app/nx-welcome.tsx", "hash": "2348102959109701629"}, {"file": "apps/web-admin/src/assets/.gitkeep", "hash": "3244421341483603138"}, {"file": "apps/web-admin/src/main.tsx", "hash": "6494962159756833686", "deps": ["npm:react", "npm:react-router-dom", "npm:react-dom"]}, {"file": "apps/web-admin/src/styles.css", "hash": "9540575453073076294"}, {"file": "apps/web-admin/tailwind.config.js", "hash": "13090734457927965885", "deps": ["npm:@nx/react"]}, {"file": "apps/web-admin/tsconfig.app.json", "hash": "2856619297212668936"}, {"file": "apps/web-admin/tsconfig.json", "hash": "9240132813632071828"}, {"file": "apps/web-admin/tsconfig.spec.json", "hash": "6057203337721981610"}, {"file": "apps/web-admin/vite.config.ts", "hash": "992336228519174157", "deps": ["npm:vite", "npm:@vitejs/plugin-react"]}], "@nasaga-monorepo/backend": [{"file": "apps/backend/eslint.config.mjs", "hash": "2624537849564014525"}, {"file": "apps/backend/jest.config.ts", "hash": "7902690112075687983"}, {"file": "apps/backend/package.json", "hash": "1684317683025338031"}, {"file": "apps/backend/src/assets/.gitkeep", "hash": "3244421341483603138"}, {"file": "apps/backend/src/main.ts", "hash": "17773914587814559191", "deps": ["npm:express"]}, {"file": "apps/backend/tsconfig.app.json", "hash": "14806075101840049041"}, {"file": "apps/backend/tsconfig.json", "hash": "9214820933160915425"}, {"file": "apps/backend/tsconfig.spec.json", "hash": "5626006774251950194"}, {"file": "apps/backend/webpack.config.js", "hash": "2522962333249277604", "deps": ["npm:@nx/webpack"]}], "@nasaga-monorepo/web-admin-e2e": [{"file": "apps/web-admin-e2e/eslint.config.mjs", "hash": "1172734138943286526", "deps": ["npm:eslint-plugin-playwright"]}, {"file": "apps/web-admin-e2e/package.json", "hash": "3823168466721510281"}, {"file": "apps/web-admin-e2e/playwright.config.ts", "hash": "10522285377186878164", "deps": ["npm:@playwright/test", "npm:@nx/playwright", "npm:@nx/devkit"]}, {"file": "apps/web-admin-e2e/src/example.spec.ts", "hash": "16019101014373124625", "deps": ["npm:@playwright/test"]}, {"file": "apps/web-admin-e2e/tsconfig.json", "hash": "15001458170383955177"}], "@nasaga-monorepo/backend-e2e": [{"file": "apps/backend-e2e/.spec.swcrc", "hash": "18201067170784284381"}, {"file": "apps/backend-e2e/eslint.config.mjs", "hash": "2624537849564014525"}, {"file": "apps/backend-e2e/jest.config.ts", "hash": "10190363813946867136"}, {"file": "apps/backend-e2e/package.json", "hash": "15588068118106126061"}, {"file": "apps/backend-e2e/src/backend/backend.spec.ts", "hash": "4579662685003779048", "deps": ["npm:axios"]}, {"file": "apps/backend-e2e/src/support/global-setup.ts", "hash": "10817858795115919263", "deps": ["npm:@nx/node"]}, {"file": "apps/backend-e2e/src/support/global-teardown.ts", "hash": "14210317125869451873", "deps": ["npm:@nx/node"]}, {"file": "apps/backend-e2e/src/support/test-setup.ts", "hash": "15521797170623961900", "deps": ["npm:axios"]}, {"file": "apps/backend-e2e/tsconfig.json", "hash": "3237992238728296076"}]}}}