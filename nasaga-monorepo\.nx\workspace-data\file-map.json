{"version": "6.0", "nxVersion": "21.3.9", "pathMappings": {}, "nxJsonPlugins": [{"name": "@nx/js/typescript", "options": {"typecheck": {"targetName": "typecheck"}, "build": {"targetName": "build", "configName": "tsconfig.lib.json", "buildDepsName": "build-deps", "watchDepsName": "watch-deps"}}}, {"name": "@nx/react/router-plugin", "options": {"buildTargetName": "build", "devTargetName": "dev", "startTargetName": "start", "watchDepsTargetName": "watch-deps", "buildDepsTargetName": "build-deps", "typecheckTargetName": "typecheck"}}, {"name": "@nx/eslint/plugin", "options": {"targetName": "lint"}}, {"name": "@nx/vite/plugin", "options": {"buildTargetName": "build", "testTargetName": "test", "serveTargetName": "serve", "devTargetName": "dev", "previewTargetName": "preview", "serveStaticTargetName": "serve-static", "typecheckTargetName": "typecheck", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}, {"name": "@nx/playwright/plugin", "options": {"targetName": "e2e"}}, {"name": "@nx/jest/plugin", "options": {"targetName": "test"}}, {"name": "@nx/expo/plugin", "options": {"startTargetName": "start", "buildTargetName": "build", "prebuildTargetName": "prebuild", "serveTargetName": "serve", "installTargetName": "install", "exportTargetName": "export", "submitTargetName": "submit", "runIosTargetName": "run-ios", "runAndroidTargetName": "run-android", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}, {"name": "@nx/webpack/plugin", "options": {"buildTargetName": "build", "serveTargetName": "serve", "previewTargetName": "preview", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}], "fileMap": {"nonProjectFiles": [{"file": "packages/.gitkeep", "hash": "3244421341483603138"}, {"file": ".vscode/extensions.json", "hash": "11309362834336059819"}, {"file": ".env.example", "hash": "7263407432259372232"}, {"file": "static/environment.js", "hash": "9358601390604326462"}, {"file": "README.md", "hash": "11875773801060702759"}, {"file": "firestore.rules", "hash": "8954973522117864638"}, {"file": "dependency-graph.html", "hash": "12560290355241016586"}, {"file": "FIREBASE_SETUP.md", "hash": "3145005192926725435"}, {"file": "firebase.json", "hash": "35928283617109"}, {"file": ".firebaserc", "hash": "10634520258383757459"}, {"file": "firestore.indexes.json", "hash": "3183401598432412470"}, {"file": "static/main.js", "hash": "10543383890141206284"}, {"file": ".husky/pre-commit", "hash": "11200534014804190460"}, {"file": "static/favicon.ico", "hash": "9303420814833116677"}, {"file": ".prettier<PERSON>", "hash": "16267754514737964994"}, {"file": ".giti<PERSON>re", "hash": "871900924519603864"}, {"file": "tsconfig.base.json", "hash": "8900268571896142108"}, {"file": ".github/workflows/deploy-preview.yml", "hash": "1869459357890926512"}, {"file": "error.log", "hash": "6007351649531648271"}, {"file": "jest.config.ts", "hash": "6870352021923392442"}, {"file": ".github/workflows/build-mobile.yml", "hash": "7349376261686727473"}, {"file": "static/runtime.js", "hash": "2835949357856416383"}, {"file": "static/styles.js", "hash": "7569025066195547382"}, {"file": "tools/scripts/eas-build-post-install.mjs", "hash": "18390423807559123090"}, {"file": "tsconfig.json", "hash": "14053782038406953560"}, {"file": "package.json", "hash": "9640786034654794702"}, {"file": ".github/workflows/deploy-production.yml", "hash": "1203573503380319279"}, {"file": "package-lock.json", "hash": "5634292891989588674"}, {"file": "static/styles.css", "hash": "5135390112669314880"}, {"file": "nx.json", "hash": "13466886889099338533"}, {"file": "jest.preset.js", "hash": "9430166341120122740"}, {"file": "commitlint.config.js", "hash": "12943710095308013050"}, {"file": ".prettieri<PERSON>re", "hash": "14688272390704683536"}, {"file": "babel.config.json", "hash": "5546405625092116747"}, {"file": ".github/workflows/pr-checks.yml", "hash": "12388881209682220421"}, {"file": "docs/firestore-schema.md", "hash": "17641818467836240866"}, {"file": ".husky/commit-msg", "hash": "3535959559828207167"}, {"file": "eslint.config.mjs", "hash": "4618192569345483032"}], "projectFileMap": {"@nasaga-monorepo/auth": [{"file": "libs/auth/.spec.swcrc", "hash": "18201067170784284381"}, {"file": "libs/auth/.swcrc", "hash": "3236768094882387443"}, {"file": "libs/auth/README.md", "hash": "5126697487225917289"}, {"file": "libs/auth/eslint.config.mjs", "hash": "4313095032450400554", "deps": [["npm:jsonc-eslint-parser", "dynamic"]]}, {"file": "libs/auth/jest.config.ts", "hash": "2065412015392748672"}, {"file": "libs/auth/package.json", "hash": "1311247968395505931", "deps": ["npm:@swc/helpers"]}, {"file": "libs/auth/src/index.ts", "hash": "5346526289404459118"}, {"file": "libs/auth/src/lib/auth-context.tsx", "hash": "3528848964678371709", "deps": ["npm:react", "@nasaga-monorepo/models"]}, {"file": "libs/auth/src/lib/auth-guards.tsx", "hash": "17860994940153968947", "deps": ["npm:react", "@nasaga-monorepo/models"]}, {"file": "libs/auth/src/lib/auth-service.ts", "hash": "4803345388759145211", "deps": ["npm:firebase", "@nasaga-monorepo/utils", "@nasaga-monorepo/models"]}, {"file": "libs/auth/src/lib/auth.spec.ts", "hash": "10761452752491445597"}, {"file": "libs/auth/src/lib/auth.ts", "hash": "9364765015501482286", "deps": ["npm:firebase", "@nasaga-monorepo/utils"]}, {"file": "libs/auth/tsconfig.json", "hash": "3956941568341465959"}, {"file": "libs/auth/tsconfig.lib.json", "hash": "7298881631146884418"}, {"file": "libs/auth/tsconfig.spec.json", "hash": "12971962995897902409"}], "@nasaga-monorepo/backend": [{"file": "apps/backend/.env.local", "hash": "3936040627793490154"}, {"file": "apps/backend/deploy.bat", "hash": "12361411146514290020"}, {"file": "apps/backend/deploy.sh", "hash": "16865713528858987919"}, {"file": "apps/backend/eslint.config.mjs", "hash": "2624537849564014525"}, {"file": "apps/backend/functions-package.json", "hash": "8963729494505203073"}, {"file": "apps/backend/jest.config.ts", "hash": "7902690112075687983"}, {"file": "apps/backend/package.json", "hash": "1684317683025338031"}, {"file": "apps/backend/src/app.ts", "hash": "7428398704912704449", "deps": ["npm:express", "npm:cors"]}, {"file": "apps/backend/src/assets/.gitkeep", "hash": "3244421341483603138"}, {"file": "apps/backend/src/index.ts", "hash": "1637404582420190689"}, {"file": "apps/backend/src/main.ts", "hash": "4385859931611631328", "deps": ["@nasaga-monorepo/utils", "npm:firebase-functions", "npm:cors", "npm:express", "npm:helmet"]}, {"file": "apps/backend/src/middleware/auth-middleware.ts", "hash": "6804548610664759024", "deps": ["npm:express", "@nasaga-monorepo/utils", "@nasaga-monorepo/models"]}, {"file": "apps/backend/src/middleware/error-handler.ts", "hash": "18284654213620467648", "deps": ["npm:express", "@nasaga-monorepo/models"]}, {"file": "apps/backend/src/middleware/request-logger.ts", "hash": "5619021544006397293", "deps": ["npm:express"]}, {"file": "apps/backend/src/routes/admin.ts", "hash": "15641838221783442221", "deps": ["npm:express", "@nasaga-monorepo/models"]}, {"file": "apps/backend/src/routes/auth.ts", "hash": "13014153283799664592", "deps": ["npm:express", "@nasaga-monorepo/utils", "@nasaga-monorepo/models"]}, {"file": "apps/backend/src/routes/properties.ts", "hash": "8816210089845885529", "deps": ["npm:express", "@nasaga-monorepo/models"]}, {"file": "apps/backend/src/routes/users.ts", "hash": "5311020785938491665", "deps": ["npm:express", "@nasaga-monorepo/models"]}, {"file": "apps/backend/tsconfig.app.json", "hash": "15702670538018423620"}, {"file": "apps/backend/tsconfig.json", "hash": "18291125838922499647"}, {"file": "apps/backend/tsconfig.spec.json", "hash": "5626006774251950194"}, {"file": "apps/backend/webpack.config.js", "hash": "2522962333249277604", "deps": ["npm:@nx/webpack"]}], "@nasaga-monorepo/web-superadmin": [{"file": "apps/web-superadmin/.env.local", "hash": "5643806573820533401"}, {"file": "apps/web-superadmin/README.md", "hash": "18054857488987995503"}, {"file": "apps/web-superadmin/e2e/example.spec.ts", "hash": "3218945747263133343", "deps": ["npm:@playwright/test"]}, {"file": "apps/web-superadmin/eslint.config.mjs", "hash": "6305481014735016224", "deps": ["npm:eslint-plugin-playwright", "npm:@nx/eslint-plugin"]}, {"file": "apps/web-superadmin/index.html", "hash": "15720753138111289419"}, {"file": "apps/web-superadmin/jest.config.ts", "hash": "13489988045630039488"}, {"file": "apps/web-superadmin/package.json", "hash": "12738662649613754691"}, {"file": "apps/web-superadmin/playwright.config.ts", "hash": "8921253346550831996", "deps": ["npm:@playwright/test", "npm:@nx/playwright", "npm:@nx/devkit"]}, {"file": "apps/web-superadmin/postcss.config.js", "hash": "2759402739052110475"}, {"file": "apps/web-superadmin/public/favicon.ico", "hash": "9303420814833116677"}, {"file": "apps/web-superadmin/src/app/app.spec.tsx", "hash": "13545115907696129348", "deps": ["npm:@testing-library/react", "npm:react-router-dom"]}, {"file": "apps/web-superadmin/src/app/app.tsx", "hash": "3434903818946924759", "deps": ["npm:react-router-dom", "@nasaga-monorepo/auth"]}, {"file": "apps/web-superadmin/src/app/components/dashboard-layout.tsx", "hash": "3756854970696774087", "deps": ["npm:react", "@nasaga-monorepo/auth", "npm:react-router-dom"]}, {"file": "apps/web-superadmin/src/app/components/role-filter.tsx", "hash": "9378121807245145032", "deps": ["npm:react", "@nasaga-monorepo/models"]}, {"file": "apps/web-superadmin/src/app/components/user-table.tsx", "hash": "11025373475362089161", "deps": ["npm:react", "@nasaga-monorepo/models", "npm:react-router-dom"]}, {"file": "apps/web-superadmin/src/app/nx-welcome.tsx", "hash": "2348102959109701629"}, {"file": "apps/web-superadmin/src/app/pages/login.tsx", "hash": "1256005147326920872", "deps": ["npm:react", "npm:react-router-dom", "@nasaga-monorepo/auth", "@nasaga/ui"]}, {"file": "apps/web-superadmin/src/app/pages/users-dashboard.tsx", "hash": "14965192078387366132", "deps": ["npm:react", "@nasaga-monorepo/models", "@nasaga-monorepo/auth"]}, {"file": "apps/web-superadmin/src/assets/.gitkeep", "hash": "3244421341483603138"}, {"file": "apps/web-superadmin/src/main.tsx", "hash": "6494962159756833686", "deps": ["npm:react", "npm:react-router-dom", "npm:react-dom"]}, {"file": "apps/web-superadmin/src/styles.css", "hash": "9540575453073076294"}, {"file": "apps/web-superadmin/tailwind.config.js", "hash": "13090734457927965885", "deps": ["npm:@nx/react"]}, {"file": "apps/web-superadmin/tsconfig.app.json", "hash": "12390187870132128919"}, {"file": "apps/web-superadmin/tsconfig.e2e.json", "hash": "16681535235805080818"}, {"file": "apps/web-superadmin/tsconfig.json", "hash": "1662147713237246032"}, {"file": "apps/web-superadmin/tsconfig.spec.json", "hash": "6057203337721981610"}, {"file": "apps/web-superadmin/vite.config.ts", "hash": "12210543790492325396", "deps": ["npm:vite", "npm:@vitejs/plugin-react"]}], "@nasaga-monorepo/web-admin-e2e": [{"file": "apps/web-admin-e2e/eslint.config.mjs", "hash": "1172734138943286526", "deps": ["npm:eslint-plugin-playwright"]}, {"file": "apps/web-admin-e2e/package.json", "hash": "3823168466721510281"}, {"file": "apps/web-admin-e2e/playwright.config.ts", "hash": "10522285377186878164", "deps": ["npm:@playwright/test", "npm:@nx/playwright", "npm:@nx/devkit"]}, {"file": "apps/web-admin-e2e/src/example.spec.ts", "hash": "16019101014373124625", "deps": ["npm:@playwright/test"]}, {"file": "apps/web-admin-e2e/tsconfig.json", "hash": "15001458170383955177"}], "@nasaga-monorepo/models": [{"file": "libs/models/.spec.swcrc", "hash": "18201067170784284381"}, {"file": "libs/models/.swcrc", "hash": "3236768094882387443"}, {"file": "libs/models/README.md", "hash": "17799089924132604789"}, {"file": "libs/models/eslint.config.mjs", "hash": "4313095032450400554", "deps": [["npm:jsonc-eslint-parser", "dynamic"]]}, {"file": "libs/models/jest.config.ts", "hash": "4141506226773631544"}, {"file": "libs/models/package.json", "hash": "14785378359001241608", "deps": ["npm:@swc/helpers"]}, {"file": "libs/models/src/index.ts", "hash": "657228069244557759"}, {"file": "libs/models/src/lib/api.types.ts", "hash": "6929527690011190665"}, {"file": "libs/models/src/lib/models.spec.ts", "hash": "12148721337196203520"}, {"file": "libs/models/src/lib/models.ts", "hash": "13522761443588667083"}, {"file": "libs/models/src/lib/property.types.ts", "hash": "7778055176218256503", "deps": ["npm:firebase"]}, {"file": "libs/models/src/lib/user.types.ts", "hash": "7789073650473123612", "deps": ["npm:firebase"]}, {"file": "libs/models/tsconfig.json", "hash": "12834038490315927699"}, {"file": "libs/models/tsconfig.lib.json", "hash": "17233988772328281602"}, {"file": "libs/models/tsconfig.spec.json", "hash": "12971962995897902409"}], "@nasaga-monorepo/web-admin": [{"file": "apps/web-admin/.env.local", "hash": "17331395282670578495"}, {"file": "apps/web-admin/eslint.config.mjs", "hash": "8789163670399984295", "deps": ["npm:@nx/eslint-plugin"]}, {"file": "apps/web-admin/index.html", "hash": "12302201515018741901"}, {"file": "apps/web-admin/jest.config.ts", "hash": "13909869933152599526"}, {"file": "apps/web-admin/package.json", "hash": "2986456316504286600"}, {"file": "apps/web-admin/postcss.config.js", "hash": "2759402739052110475"}, {"file": "apps/web-admin/public/favicon.ico", "hash": "9303420814833116677"}, {"file": "apps/web-admin/src/app/app.spec.tsx", "hash": "404361484111038727", "deps": ["npm:@testing-library/react", "npm:react-router-dom"]}, {"file": "apps/web-admin/src/app/app.tsx", "hash": "6571329419038046542", "deps": ["@nasaga-monorepo/auth", "npm:react-router-dom"]}, {"file": "apps/web-admin/src/app/components/ApproveRejectModal.tsx", "hash": "15319662460652935006", "deps": ["npm:react", "@nasaga-monorepo/models", "@nasaga-monorepo/auth"]}, {"file": "apps/web-admin/src/app/components/PropertyList.tsx", "hash": "12473013309937668538", "deps": ["npm:react", "@nasaga-monorepo/models"]}, {"file": "apps/web-admin/src/app/nx-welcome.tsx", "hash": "2348102959109701629"}, {"file": "apps/web-admin/src/app/pages/dashboard.tsx", "hash": "15719306329089967655", "deps": ["npm:react", "@nasaga-monorepo/auth"]}, {"file": "apps/web-admin/src/app/pages/login.tsx", "hash": "15077720037953649791", "deps": ["npm:react", "npm:react-router-dom", "@nasaga/ui"]}, {"file": "apps/web-admin/src/app/pages/properties.tsx", "hash": "11402023004595308692", "deps": ["npm:react", "@nasaga-monorepo/auth", "@nasaga-monorepo/models"]}, {"file": "apps/web-admin/src/app/pages/register.tsx", "hash": "5848580939392109349", "deps": ["npm:react", "npm:react-router-dom", "@nasaga/ui"]}, {"file": "apps/web-admin/src/app/services/properties.service.ts", "hash": "14363688908522655322", "deps": ["@nasaga-monorepo/utils", "npm:firebase", "@nasaga-monorepo/models"]}, {"file": "apps/web-admin/src/assets/.gitkeep", "hash": "3244421341483603138"}, {"file": "apps/web-admin/src/main.tsx", "hash": "6494962159756833686", "deps": ["npm:react", "npm:react-router-dom", "npm:react-dom"]}, {"file": "apps/web-admin/src/styles.css", "hash": "9540575453073076294"}, {"file": "apps/web-admin/tailwind.config.js", "hash": "13090734457927965885", "deps": ["npm:@nx/react"]}, {"file": "apps/web-admin/tsconfig.app.json", "hash": "6026206714735933347"}, {"file": "apps/web-admin/tsconfig.json", "hash": "9627008286706670343"}, {"file": "apps/web-admin/tsconfig.spec.json", "hash": "6057203337721981610"}, {"file": "apps/web-admin/vite.config.ts", "hash": "992336228519174157", "deps": ["npm:vite", "npm:@vitejs/plugin-react"]}], "@nasaga/ui": [{"file": "libs/ui/.babelrc", "hash": "2946678488919325452"}, {"file": "libs/ui/README.md", "hash": "14894241336626855831"}, {"file": "libs/ui/eslint.config.mjs", "hash": "8789163670399984295", "deps": ["npm:@nx/eslint-plugin"]}, {"file": "libs/ui/jest.config.ts", "hash": "17429197388015648977"}, {"file": "libs/ui/package.json", "hash": "16216521943772232159"}, {"file": "libs/ui/src/index.ts", "hash": "4595898056948482357"}, {"file": "libs/ui/src/lib/features/auth/components/form-button.tsx", "hash": "3237340216396635639", "deps": ["npm:react"]}, {"file": "libs/ui/src/lib/features/auth/components/input-field.tsx", "hash": "3244371832688405803", "deps": ["npm:react"]}, {"file": "libs/ui/src/lib/features/auth/components/login-form.tsx", "hash": "9863583249458764400", "deps": ["npm:react", "npm:react-hook-form", "npm:@hookform/resolvers"]}, {"file": "libs/ui/src/lib/features/auth/components/registration-form.tsx", "hash": "11045932707173668788", "deps": ["npm:react", "npm:react-hook-form", "npm:@hookform/resolvers"]}, {"file": "libs/ui/src/lib/features/auth/components/select-field.tsx", "hash": "10984111138161707646", "deps": ["npm:react"]}, {"file": "libs/ui/src/lib/features/auth/hooks/use-auth-redirect.ts", "hash": "8883733812080863738", "deps": ["npm:react", "@nasaga-monorepo/auth", "@nasaga-monorepo/models"]}, {"file": "libs/ui/src/lib/features/auth/pages/login-page.tsx", "hash": "8640227789067952342", "deps": ["npm:react", "@nasaga-monorepo/auth"]}, {"file": "libs/ui/src/lib/features/auth/pages/registration-page.tsx", "hash": "3057509966042994700", "deps": ["npm:react", "@nasaga-monorepo/auth"]}, {"file": "libs/ui/src/lib/features/auth/schemas/auth-schemas.ts", "hash": "12921814303092986526", "deps": ["npm:yup"]}, {"file": "libs/ui/src/lib/features/auth/tests/login-form.test.tsx", "hash": "842871516850081932", "deps": ["npm:react", "npm:@testing-library/react", "npm:@testing-library/user-event"]}, {"file": "libs/ui/src/lib/features/auth/tests/registration-form.test.tsx", "hash": "13316838866470511763", "deps": ["npm:react", "npm:@testing-library/react", "npm:@testing-library/user-event"]}, {"file": "libs/ui/src/lib/layout/DashboardLayout.tsx", "hash": "4960067012382792236", "deps": ["npm:react", "@nasaga-monorepo/models"]}, {"file": "libs/ui/src/lib/navigation/SideNav.tsx", "hash": "3073539536642656835", "deps": ["npm:react", "@nasaga-monorepo/models"]}, {"file": "libs/ui/src/lib/navigation/configs.ts", "hash": "3553228875822063628"}, {"file": "libs/ui/src/lib/navigation/types.ts", "hash": "7721859497230069195", "deps": ["@nasaga-monorepo/models"]}, {"file": "libs/ui/src/lib/providers/NasagaThemeProvider.tsx", "hash": "10606302839840033420", "deps": ["npm:react"]}, {"file": "libs/ui/src/lib/theme.ts", "hash": "10202434132719993347"}, {"file": "libs/ui/src/lib/ui.spec.tsx", "hash": "7677546453323585860", "deps": ["npm:@testing-library/react"]}, {"file": "libs/ui/src/lib/ui.tsx", "hash": "17549761452083750797"}, {"file": "libs/ui/tsconfig.json", "hash": "3143512756470678497"}, {"file": "libs/ui/tsconfig.lib.json", "hash": "16960883786419930543"}, {"file": "libs/ui/tsconfig.spec.json", "hash": "200392398545240393"}, {"file": "libs/ui/vite.config.ts", "hash": "13183280991152883617", "deps": ["npm:vite", "npm:@vitejs/plugin-react", "npm:vite-plugin-dts"]}], "@nasaga-monorepo/backend-e2e": [{"file": "apps/backend-e2e/.spec.swcrc", "hash": "18201067170784284381"}, {"file": "apps/backend-e2e/eslint.config.mjs", "hash": "2624537849564014525"}, {"file": "apps/backend-e2e/jest.config.ts", "hash": "10190363813946867136"}, {"file": "apps/backend-e2e/package.json", "hash": "15588068118106126061"}, {"file": "apps/backend-e2e/src/backend/backend.spec.ts", "hash": "4579662685003779048", "deps": ["npm:axios"]}, {"file": "apps/backend-e2e/src/support/global-setup.ts", "hash": "10817858795115919263", "deps": ["npm:@nx/node"]}, {"file": "apps/backend-e2e/src/support/global-teardown.ts", "hash": "14210317125869451873", "deps": ["npm:@nx/node"]}, {"file": "apps/backend-e2e/src/support/test-setup.ts", "hash": "15521797170623961900", "deps": ["npm:axios"]}, {"file": "apps/backend-e2e/tsconfig.json", "hash": "3237992238728296076"}], "@nasaga-monorepo/mobile-buyer": [{"file": "apps/mobile-buyer/.babelrc.js", "hash": "231774045518377938"}, {"file": "apps/mobile-buyer/.env.local", "hash": "9106466321479956217"}, {"file": "apps/mobile-buyer/app.config.js", "hash": "16570765917665141491"}, {"file": "apps/mobile-buyer/app.json", "hash": "6319097293810123944"}, {"file": "apps/mobile-buyer/assets/adaptive-icon.png", "hash": "16102135492459408350"}, {"file": "apps/mobile-buyer/assets/favicon.png", "hash": "10556792184362654649"}, {"file": "apps/mobile-buyer/assets/icon.png", "hash": "14252482080276080187"}, {"file": "apps/mobile-buyer/assets/splash.png", "hash": "16493559885079032523"}, {"file": "apps/mobile-buyer/eas.json", "hash": "6330161018993400891"}, {"file": "apps/mobile-buyer/eslint.config.mjs", "hash": "4549399322512926610", "deps": ["npm:@nx/eslint-plugin"]}, {"file": "apps/mobile-buyer/index.js", "hash": "1184010334168630581", "deps": ["npm:expo"]}, {"file": "apps/mobile-buyer/jest.config.ts", "hash": "5151918667861967766", "deps": [["npm:jest-expo", "dynamic"]]}, {"file": "apps/mobile-buyer/metro.config.js", "hash": "2922723032447638085", "deps": ["npm:@nx/expo", "npm:@expo/metro-config", "npm:metro-config", "npm:react-native-svg-transformer"]}, {"file": "apps/mobile-buyer/package.json", "hash": "9348583059420496359"}, {"file": "apps/mobile-buyer/src/app/App.spec.tsx", "hash": "5072730789784802033", "deps": ["npm:react", "npm:@testing-library/react-native"]}, {"file": "apps/mobile-buyer/src/app/App.tsx", "hash": "14325773300223367297", "deps": ["npm:react", "npm:react-native", "npm:react-native-svg"]}, {"file": "apps/mobile-buyer/src/test-setup.ts", "hash": "9919311210714806624", "deps": ["npm:@testing-library/jest-native"]}, {"file": "apps/mobile-buyer/tsconfig.app.json", "hash": "8967954828260004517"}, {"file": "apps/mobile-buyer/tsconfig.json", "hash": "9214820933160915425"}, {"file": "apps/mobile-buyer/tsconfig.spec.json", "hash": "10834278139151176693"}], "@nasaga-monorepo/mobile-agent": [{"file": "apps/mobile-agent/.babelrc.js", "hash": "231774045518377938"}, {"file": "apps/mobile-agent/.env.local", "hash": "14114435848335314488"}, {"file": "apps/mobile-agent/app.config.js", "hash": "11142650596492088978"}, {"file": "apps/mobile-agent/app.json", "hash": "16930853411394718298"}, {"file": "apps/mobile-agent/assets/adaptive-icon.png", "hash": "16102135492459408350"}, {"file": "apps/mobile-agent/assets/favicon.png", "hash": "10556792184362654649"}, {"file": "apps/mobile-agent/assets/icon.png", "hash": "14252482080276080187"}, {"file": "apps/mobile-agent/assets/splash.png", "hash": "16493559885079032523"}, {"file": "apps/mobile-agent/eas.json", "hash": "6330161018993400891"}, {"file": "apps/mobile-agent/eslint.config.mjs", "hash": "4549399322512926610", "deps": ["npm:@nx/eslint-plugin"]}, {"file": "apps/mobile-agent/index.js", "hash": "1184010334168630581", "deps": ["npm:expo"]}, {"file": "apps/mobile-agent/jest.config.ts", "hash": "10254722369530945867", "deps": [["npm:jest-expo", "dynamic"]]}, {"file": "apps/mobile-agent/metro.config.js", "hash": "13288203599039829993", "deps": ["npm:@nx/expo", "npm:@expo/metro-config", "npm:metro-config", "npm:react-native-svg-transformer"]}, {"file": "apps/mobile-agent/package.json", "hash": "9699701234321643546"}, {"file": "apps/mobile-agent/src/app/App.spec.tsx", "hash": "5072730789784802033", "deps": ["npm:react", "npm:@testing-library/react-native"]}, {"file": "apps/mobile-agent/src/app/App.tsx", "hash": "7987569310274201435", "deps": ["npm:react", "npm:react-native", "npm:react-native-svg"]}, {"file": "apps/mobile-agent/src/app/screens/dashboard-screen.tsx", "hash": "3406577949847011726", "deps": ["npm:react", "npm:react-native", "@nasaga-monorepo/auth"]}, {"file": "apps/mobile-agent/src/app/screens/login-screen.tsx", "hash": "8558074678093640629", "deps": ["npm:react", "@nasaga/ui"]}, {"file": "apps/mobile-agent/src/app/screens/register-screen.tsx", "hash": "2903389580739104294", "deps": ["npm:react", "@nasaga/ui"]}, {"file": "apps/mobile-agent/src/test-setup.ts", "hash": "9919311210714806624", "deps": ["npm:@testing-library/jest-native"]}, {"file": "apps/mobile-agent/tsconfig.app.json", "hash": "12655416099839703627"}, {"file": "apps/mobile-agent/tsconfig.json", "hash": "15963222867769846524"}, {"file": "apps/mobile-agent/tsconfig.spec.json", "hash": "10834278139151176693"}], "@nasaga-monorepo/utils": [{"file": "libs/utils/.spec.swcrc", "hash": "18201067170784284381"}, {"file": "libs/utils/.swcrc", "hash": "3236768094882387443"}, {"file": "libs/utils/README.md", "hash": "3816604565008484386"}, {"file": "libs/utils/eslint.config.mjs", "hash": "4313095032450400554", "deps": [["npm:jsonc-eslint-parser", "dynamic"]]}, {"file": "libs/utils/jest.config.ts", "hash": "14554055299194173366"}, {"file": "libs/utils/package.json", "hash": "8170807899817773011", "deps": ["npm:@swc/helpers"]}, {"file": "libs/utils/src/index.ts", "hash": "17593572675488777602"}, {"file": "libs/utils/src/lib/firebase-admin.ts", "hash": "15379251573092775700", "deps": ["npm:firebase-admin"]}, {"file": "libs/utils/src/lib/firebase-config.ts", "hash": "15530240562421268084", "deps": ["npm:firebase"]}, {"file": "libs/utils/src/lib/utils.spec.ts", "hash": "17453479995195199863"}, {"file": "libs/utils/src/lib/utils.ts", "hash": "17287710670217841263"}, {"file": "libs/utils/tsconfig.json", "hash": "12834038490315927699"}, {"file": "libs/utils/tsconfig.lib.json", "hash": "17233988772328281602"}, {"file": "libs/utils/tsconfig.spec.json", "hash": "12971962995897902409"}]}}}