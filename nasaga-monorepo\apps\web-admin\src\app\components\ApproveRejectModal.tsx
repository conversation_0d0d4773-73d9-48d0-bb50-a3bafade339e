import React, { useState } from 'react';
import { Property, PropertyStatusUpdate } from '@nasaga-monorepo/models';
import { useAuth } from '@nasaga-monorepo/auth';
import { propertiesService } from '../services/properties.service';

interface ApproveRejectModalProps {
  isOpen: boolean;
  onClose: () => void;
  property: Property | null;
  onStatusUpdate: (property: Property) => void;
}

export const ApproveRejectModal: React.FC<ApproveRejectModalProps> = ({
  isOpen,
  onClose,
  property,
  onStatusUpdate
}) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  if (!isOpen || !property) return null;

  const handleApprove = async () => {
    if (!user?.uid) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const result = await propertiesService.approveProperty(property.propertyId, user.uid);
      
      if (result.success && result.data) {
        onStatusUpdate(result.data);
        onClose();
      } else {
        setError(result.error || 'Failed to approve property');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleReject = async () => {
    if (!user?.uid) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const result = await propertiesService.rejectProperty(property.propertyId, user.uid);
      
      if (result.success && result.data) {
        onStatusUpdate(result.data);
        onClose();
      } else {
        setError(result.error || 'Failed to reject property');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(price);
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'N/A';
    
    // Handle Firebase Timestamp
    if (timestamp.toDate) {
      return timestamp.toDate().toLocaleDateString();
    }
    
    // Handle regular Date object
    if (timestamp instanceof Date) {
      return timestamp.toLocaleDateString();
    }
    
    // Handle string dates
    return new Date(timestamp).toLocaleDateString();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">
              Review Property Listing
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-2xl font-bold"
              disabled={loading}
            >
              ×
            </button>
          </div>
        </div>

        {/* Property Details */}
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Basic Info */}
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Title</label>
                <p className="text-lg font-semibold text-gray-900">{property.title}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Price</label>
                <p className="text-xl font-bold text-green-600">{formatPrice(property.price)}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Property Type</label>
                <p className="text-gray-900">{property.propertyType}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Status</label>
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  property.status === 'pending' 
                    ? 'bg-yellow-100 text-yellow-800'
                    : property.status === 'approved'
                    ? 'bg-green-100 text-green-800'
                    : property.status === 'active'
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {property.status.toUpperCase()}
                </span>
              </div>
            </div>

            {/* Property Details */}
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Bedrooms / Bathrooms</label>
                <p className="text-gray-900">{property.bedrooms} bed, {property.bathrooms} bath</p>
              </div>
              
              {property.squareFootage && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Square Footage</label>
                  <p className="text-gray-900">{property.squareFootage.toLocaleString()} sq ft</p>
                </div>
              )}
              
              <div>
                <label className="text-sm font-medium text-gray-500">Location</label>
                <p className="text-gray-900">
                  {property.address}<br />
                  {property.city}, {property.state} {property.zipCode}
                </p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Listed Date</label>
                <p className="text-gray-900">{formatDate(property.createdAt)}</p>
              </div>
            </div>
          </div>

          {/* Description */}
          <div className="mt-6">
            <label className="text-sm font-medium text-gray-500">Description</label>
            <p className="mt-2 text-gray-900 whitespace-pre-wrap">{property.description}</p>
          </div>

          {/* Images */}
          {property.imageUrls && property.imageUrls.length > 0 && (
            <div className="mt-6">
              <label className="text-sm font-medium text-gray-500">Images ({property.imageUrls.length})</label>
              <div className="mt-2 grid grid-cols-2 md:grid-cols-3 gap-4">
                {property.imageUrls.slice(0, 6).map((url, index) => (
                  <div key={index} className="relative">
                    <img
                      src={url}
                      alt={`Property ${index + 1}`}
                      className="w-full h-24 object-cover rounded-lg border border-gray-200"
                    />
                  </div>
                ))}
                {property.imageUrls.length > 6 && (
                  <div className="w-full h-24 bg-gray-100 rounded-lg border border-gray-200 flex items-center justify-center">
                    <span className="text-sm text-gray-500">
                      +{property.imageUrls.length - 6} more
                    </span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {error}
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="p-6 border-t border-gray-200 bg-gray-50 flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
            disabled={loading}
          >
            Cancel
          </button>
          
          {property.status === 'pending' && (
            <>
              <button
                onClick={handleReject}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 flex items-center"
                disabled={loading}
              >
                {loading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                ) : null}
                Reject
              </button>
              
              <button
                onClick={handleApprove}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 flex items-center"
                disabled={loading}
              >
                {loading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                ) : null}
                Approve
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  );
};
