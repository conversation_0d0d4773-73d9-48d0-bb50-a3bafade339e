{"13913308424185932016": {"targets": {"build": {"command": "webpack-cli build", "options": {"cwd": "apps/backend", "args": ["--node-env=production"]}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["webpack-cli"]}], "outputs": ["{workspaceRoot}/apps\\backend\\dist"], "metadata": {"technologies": ["webpack"], "description": "Runs Webpack build", "help": {"command": "npx webpack-cli build --help", "example": {"options": {"json": "stats.json"}, "args": ["--profile"]}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "webpack-cli serve", "options": {"cwd": "apps/backend", "args": ["--node-env=development"]}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "webpack-cli serve", "options": {"cwd": "apps/backend", "args": ["--node-env=production"]}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server in production mode", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve-static": {"continuous": true, "dependsOn": ["build"], "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/backend --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/backend"}}, "metadata": {}}, "6119263285643407332": {"targets": {"build": {"command": "webpack-cli build", "options": {"cwd": "apps/backend", "args": ["--node-env=production"]}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["webpack-cli"]}], "outputs": ["{workspaceRoot}/apps\\backend\\dist"], "metadata": {"technologies": ["webpack"], "description": "Runs Webpack build", "help": {"command": "npx webpack-cli build --help", "example": {"options": {"json": "stats.json"}, "args": ["--profile"]}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "webpack-cli serve", "options": {"cwd": "apps/backend", "args": ["--node-env=development"]}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "webpack-cli serve", "options": {"cwd": "apps/backend", "args": ["--node-env=production"]}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server in production mode", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve-static": {"continuous": true, "dependsOn": ["build"], "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/backend --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/backend"}}, "metadata": {}}, "5946823342064857556": {"targets": {"build": {"command": "webpack-cli build", "options": {"cwd": "apps/backend", "args": ["--node-env=production"]}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["webpack-cli"]}], "outputs": ["{workspaceRoot}/apps\\backend\\dist"], "metadata": {"technologies": ["webpack"], "description": "Runs Webpack build", "help": {"command": "npx webpack-cli build --help", "example": {"options": {"json": "stats.json"}, "args": ["--profile"]}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "webpack-cli serve", "options": {"cwd": "apps/backend", "args": ["--node-env=development"]}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "webpack-cli serve", "options": {"cwd": "apps/backend", "args": ["--node-env=production"]}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server in production mode", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve-static": {"continuous": true, "dependsOn": ["build"], "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/backend --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/backend"}}, "metadata": {}}, "5885106154373777319": {"targets": {"build": {"command": "webpack-cli build", "options": {"cwd": "apps/backend", "args": ["--node-env=production"]}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["webpack-cli"]}], "outputs": ["{workspaceRoot}/apps\\backend\\dist"], "metadata": {"technologies": ["webpack"], "description": "Runs Webpack build", "help": {"command": "npx webpack-cli build --help", "example": {"options": {"json": "stats.json"}, "args": ["--profile"]}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "webpack-cli serve", "options": {"cwd": "apps/backend", "args": ["--node-env=development"]}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "webpack-cli serve", "options": {"cwd": "apps/backend", "args": ["--node-env=production"]}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server in production mode", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve-static": {"continuous": true, "dependsOn": ["build"], "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/backend --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/backend"}}, "metadata": {}}, "10749896418841002039": {"targets": {"build": {"command": "webpack-cli build", "options": {"cwd": "apps/backend", "args": ["--node-env=production"]}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["webpack-cli"]}], "outputs": ["{workspaceRoot}/apps\\backend\\dist"], "metadata": {"technologies": ["webpack"], "description": "Runs Webpack build", "help": {"command": "npx webpack-cli build --help", "example": {"options": {"json": "stats.json"}, "args": ["--profile"]}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "webpack-cli serve", "options": {"cwd": "apps/backend", "args": ["--node-env=development"]}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "webpack-cli serve", "options": {"cwd": "apps/backend", "args": ["--node-env=production"]}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server in production mode", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve-static": {"continuous": true, "dependsOn": ["build"], "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/backend --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/backend"}}, "metadata": {}}, "13135521915533000577": {"targets": {"build": {"command": "webpack-cli build", "options": {"cwd": "apps/backend", "args": ["--node-env=production"]}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["webpack-cli"]}], "outputs": ["{workspaceRoot}/apps\\backend\\dist"], "metadata": {"technologies": ["webpack"], "description": "Runs Webpack build", "help": {"command": "npx webpack-cli build --help", "example": {"options": {"json": "stats.json"}, "args": ["--profile"]}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "webpack-cli serve", "options": {"cwd": "apps/backend", "args": ["--node-env=development"]}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "webpack-cli serve", "options": {"cwd": "apps/backend", "args": ["--node-env=production"]}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server in production mode", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve-static": {"continuous": true, "dependsOn": ["build"], "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/backend --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/backend"}}, "metadata": {}}, "7070326202909553285": {"targets": {"build": {"command": "webpack-cli build", "options": {"cwd": "apps/backend", "args": ["--node-env=production"]}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["webpack-cli"]}], "outputs": ["{workspaceRoot}/apps\\backend\\dist"], "metadata": {"technologies": ["webpack"], "description": "Runs Webpack build", "help": {"command": "npx webpack-cli build --help", "example": {"options": {"json": "stats.json"}, "args": ["--profile"]}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "webpack-cli serve", "options": {"cwd": "apps/backend", "args": ["--node-env=development"]}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "webpack-cli serve", "options": {"cwd": "apps/backend", "args": ["--node-env=production"]}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server in production mode", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve-static": {"continuous": true, "dependsOn": ["build"], "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/backend --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/backend"}}, "metadata": {}}}