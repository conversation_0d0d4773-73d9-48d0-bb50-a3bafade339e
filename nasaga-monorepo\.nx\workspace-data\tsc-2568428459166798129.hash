{"7128465733856323954_apps/web-admin/tsconfig.app.json": {"targets": {}}, "8342738689157242492_apps/web-admin/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-admin"}, "cache": true, "inputs": ["production", {"dependentTasksOutputFiles": "**/*.d.ts"}, {"externalDependencies": ["typescript"]}], "outputs": [], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "18047592745060583863_apps/web-admin/tsconfig.spec.json": {"targets": {}}, "11250524700075914126_apps/web-admin-e2e/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-admin-e2e"}, "cache": true, "inputs": ["{projectRoot}/package.json", "{workspaceRoot}/tsconfig.base.json", "{projectRoot}/tsconfig.json", "{projectRoot}/**/*.ts", "{projectRoot}/**/*.js", "{projectRoot}/playwright.config.ts", "{projectRoot}/src/**/*.spec.ts", "{projectRoot}/src/**/*.spec.js", "{projectRoot}/src/**/*.test.ts", "{projectRoot}/src/**/*.test.js", "{projectRoot}/src/**/*.d.ts", "!{projectRoot}/out-tsc", "!{projectRoot}/test-output", "!{projectRoot}/eslint.config.js", "!{projectRoot}/eslint.config.mjs", "!{projectRoot}/eslint.config.cjs", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/out-tsc/playwright/**/*.d.ts", "{projectRoot}/out-tsc/playwright/**/*.d.ts.map", "{projectRoot}/out-tsc/playwright/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "2067099076529089093_apps/web-superadmin/tsconfig.app.json": {"targets": {}}, "17351821411698225184_apps/web-superadmin/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-superadmin"}, "cache": true, "inputs": ["production", {"dependentTasksOutputFiles": "**/*.d.ts"}, {"externalDependencies": ["typescript"]}], "outputs": [], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "5228440163915052409_apps/web-superadmin/tsconfig.spec.json": {"targets": {}}, "15800832464867158429_apps/web-admin/tsconfig.app.json": {"targets": {}}, "15210438892930668621_apps/web-admin/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-admin"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.app.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "1710963106476311644_apps/web-admin/tsconfig.spec.json": {"targets": {}}, "4823339046299282460_apps/web-admin-e2e/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-admin-e2e"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/out-tsc/playwright/**/*.d.ts", "{projectRoot}/out-tsc/playwright/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "3086645296890195375_apps/web-superadmin/tsconfig.app.json": {"targets": {}}, "2935628315619120199_apps/web-superadmin/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-superadmin"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.app.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "610970390717860065_apps/web-superadmin/tsconfig.spec.json": {"targets": {}}, "15481509928639776972_apps/mobile-agent/tsconfig.app.json": {"targets": {}}, "4707885399619223642_apps/mobile-agent/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/mobile-agent"}, "cache": true, "inputs": ["production", {"dependentTasksOutputFiles": "**/*.d.ts"}, {"externalDependencies": ["typescript"]}], "outputs": [], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "14385821869278056241_apps/mobile-agent/tsconfig.spec.json": {"targets": {}}, "10626021208465595372_apps/web-admin/tsconfig.app.json": {"targets": {}}, "14563045440423928294_apps/web-admin/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-admin"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.app.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "12801286076342561375_apps/web-admin/tsconfig.spec.json": {"targets": {}}, "4400362541653238850_apps/web-admin-e2e/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-admin-e2e"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/out-tsc/playwright/**/*.d.ts", "{projectRoot}/out-tsc/playwright/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "16977839481852994557_apps/web-superadmin/tsconfig.app.json": {"targets": {}}, "13602353638739360023_apps/web-superadmin/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-superadmin"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.app.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "10746569347752687330_apps/web-superadmin/tsconfig.spec.json": {"targets": {}}, "12369268820633792275_apps/mobile-buyer/tsconfig.app.json": {"targets": {}}, "6572412712968851250_apps/mobile-buyer/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/mobile-buyer"}, "cache": true, "inputs": ["production", {"dependentTasksOutputFiles": "**/*.d.ts"}, {"externalDependencies": ["typescript"]}], "outputs": [], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "15116992603001344688_apps/mobile-buyer/tsconfig.spec.json": {"targets": {}}, "8574557916595859072_apps/backend/tsconfig.app.json": {"targets": {}}, "10545110113547411450_apps/backend/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/backend"}, "cache": true, "inputs": ["production", {"dependentTasksOutputFiles": "**/*.d.ts"}, {"externalDependencies": ["typescript"]}], "outputs": [], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "14138382257695595621_apps/backend/tsconfig.spec.json": {"targets": {}}, "11693318498503376089_apps/backend-e2e/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/backend-e2e"}, "cache": true, "inputs": ["{projectRoot}/package.json", "{workspaceRoot}/tsconfig.base.json", "{projectRoot}/tsconfig.json", "{projectRoot}/jest.config.ts", "{projectRoot}/src/**/*.ts", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/out-tsc/@nasaga-monorepo/backend-e2e/**/*.d.ts", "{projectRoot}/out-tsc/@nasaga-monorepo/backend-e2e/**/*.d.ts.map", "{projectRoot}/out-tsc/@nasaga-monorepo/backend-e2e/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "7662626762341658471_apps/backend/tsconfig.app.json": {"targets": {}}, "17961481132370406301_apps/backend/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/backend"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.app.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "12569094257704075355_apps/backend/tsconfig.spec.json": {"targets": {}}, "11261509279521058789_apps/backend-e2e/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/backend-e2e"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/out-tsc/@nasaga-monorepo/backend-e2e/**/*.d.ts", "{projectRoot}/out-tsc/@nasaga-monorepo/backend-e2e/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "13447934106982350898_apps/mobile-agent/tsconfig.app.json": {"targets": {}}, "7576401897353117855_apps/mobile-agent/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/mobile-agent"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.app.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "8073786932049504551_apps/mobile-agent/tsconfig.spec.json": {"targets": {}}, "9523200023577148975_apps/mobile-buyer/tsconfig.app.json": {"targets": {}}, "450191317874472555_apps/mobile-buyer/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/mobile-buyer"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.app.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "6347983973537038678_apps/mobile-buyer/tsconfig.spec.json": {"targets": {}}, "6269383948308742248_apps/web-admin/tsconfig.app.json": {"targets": {}}, "8982821700609477583_apps/web-admin/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-admin"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.app.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "3989624181492795962_apps/web-admin/tsconfig.spec.json": {"targets": {}}, "12649367539402715307_apps/web-admin-e2e/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-admin-e2e"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/out-tsc/playwright/**/*.d.ts", "{projectRoot}/out-tsc/playwright/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "10786060247920070088_apps/web-superadmin/tsconfig.app.json": {"targets": {}}, "17440573582834186605_apps/web-superadmin/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-superadmin"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.app.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "8566751686814791093_apps/web-superadmin/tsconfig.spec.json": {"targets": {}}, "9826206595788265228_libs/ui/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["build", "^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "libs/ui"}, "cache": true, "inputs": ["production", {"dependentTasksOutputFiles": "**/*.d.ts"}, {"externalDependencies": ["typescript"]}], "outputs": [], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "4689191944708840519_libs/ui/tsconfig.lib.json": {"targets": {"build": {"dependsOn": ["^build"], "command": "tsc --build tsconfig.lib.json", "options": {"cwd": "libs/ui"}, "cache": true, "inputs": ["{projectRoot}/package.json", "{workspaceRoot}/tsconfig.base.json", "{projectRoot}/tsconfig.lib.json", "{projectRoot}/src/**/*.js", "{projectRoot}/src/**/*.jsx", "{projectRoot}/src/**/*.ts", "{projectRoot}/src/**/*.tsx", "!{projectRoot}/out-tsc", "!{projectRoot}/dist", "!{projectRoot}/**/*.spec.ts", "!{projectRoot}/**/*.test.ts", "!{projectRoot}/**/*.spec.tsx", "!{projectRoot}/**/*.test.tsx", "!{projectRoot}/**/*.spec.js", "!{projectRoot}/**/*.test.js", "!{projectRoot}/**/*.spec.jsx", "!{projectRoot}/**/*.test.jsx", "!{projectRoot}/jest.config.ts", "!{projectRoot}/src/**/*.spec.ts", "!{projectRoot}/src/**/*.test.ts", "!{projectRoot}/eslint.config.js", "!{projectRoot}/eslint.config.cjs", "!{projectRoot}/eslint.config.mjs", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/**/*.d.ts.map", "{projectRoot}/dist/tsconfig.lib.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Builds the project with `tsc`.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/ui --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/ui"}}}, "11196895120716770889_libs/ui/tsconfig.spec.json": {"targets": {}}, "6272698643360866140_apps/backend/tsconfig.app.json": {"targets": {}}, "11472391740358194029_apps/backend/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/backend"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.app.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "10174235371875205925_apps/backend/tsconfig.spec.json": {"targets": {}}, "13962045783857031893_apps/backend-e2e/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/backend-e2e"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/out-tsc/@nasaga-monorepo/backend-e2e/**/*.d.ts", "{projectRoot}/out-tsc/@nasaga-monorepo/backend-e2e/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "13674706105479269344_apps/mobile-agent/tsconfig.app.json": {"targets": {}}, "15122968312239031407_apps/mobile-agent/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/mobile-agent"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.app.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "8266863162398846435_apps/mobile-agent/tsconfig.spec.json": {"targets": {}}, "8851377592466635728_apps/mobile-buyer/tsconfig.app.json": {"targets": {}}, "1241927545901391023_apps/mobile-buyer/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/mobile-buyer"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.app.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "12897644561584517053_apps/mobile-buyer/tsconfig.spec.json": {"targets": {}}, "10536704411885721865_apps/web-admin/tsconfig.app.json": {"targets": {}}, "3826904983741556559_apps/web-admin/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-admin"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.app.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "1678590180468628309_apps/web-admin/tsconfig.spec.json": {"targets": {}}, "14710794003759953491_apps/web-admin-e2e/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-admin-e2e"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/out-tsc/playwright/**/*.d.ts", "{projectRoot}/out-tsc/playwright/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "7481594240783707189_apps/web-superadmin/tsconfig.app.json": {"targets": {}}, "9316174219160457117_apps/web-superadmin/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-superadmin"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.app.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "17231545643894884364_apps/web-superadmin/tsconfig.spec.json": {"targets": {}}, "3561327289466707515_libs/ui/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["build", "^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "libs/ui"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.lib.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "3503265655988791438_libs/ui/tsconfig.lib.json": {"targets": {"build": {"dependsOn": ["^build"], "command": "tsc --build tsconfig.lib.json", "options": {"cwd": "libs/ui"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/dist"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Builds the project with `tsc`.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/ui --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/ui"}}}, "5157520034526690719_libs/ui/tsconfig.spec.json": {"targets": {}}, "7555182361066021129_libs/auth/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["build", "^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "libs/auth"}, "cache": true, "inputs": ["production", {"dependentTasksOutputFiles": "**/*.d.ts"}, {"externalDependencies": ["typescript"]}], "outputs": [], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "132478485298409503_libs/auth/tsconfig.lib.json": {"targets": {"build": {"dependsOn": ["^build"], "command": "tsc --build tsconfig.lib.json", "options": {"cwd": "libs/auth"}, "cache": true, "inputs": ["{projectRoot}/package.json", "{workspaceRoot}/tsconfig.base.json", "{projectRoot}/tsconfig.lib.json", "{projectRoot}/src/**/*.ts", "!{projectRoot}/jest.config.ts", "!{projectRoot}/src/**/*.spec.ts", "!{projectRoot}/src/**/*.test.ts", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/**/*.d.ts.map", "{projectRoot}/dist/tsconfig.lib.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Builds the project with `tsc`.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/auth --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/auth"}}}, "14455406460633690138_libs/auth/tsconfig.spec.json": {"targets": {}}, "13073553020291912925_apps/backend/tsconfig.app.json": {"targets": {}}, "16152705673816920684_apps/backend/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/backend"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.app.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "6928292813836760529_apps/backend/tsconfig.spec.json": {"targets": {}}, "10287046168587494958_apps/backend-e2e/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/backend-e2e"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/out-tsc/@nasaga-monorepo/backend-e2e/**/*.d.ts", "{projectRoot}/out-tsc/@nasaga-monorepo/backend-e2e/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "4432692697981307078_apps/mobile-agent/tsconfig.app.json": {"targets": {}}, "14595854107917852146_apps/mobile-agent/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/mobile-agent"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.app.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "7851708279039060167_apps/mobile-agent/tsconfig.spec.json": {"targets": {}}, "5101985490355644560_apps/mobile-buyer/tsconfig.app.json": {"targets": {}}, "4483217656358135883_apps/mobile-buyer/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/mobile-buyer"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.app.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "4145431933344927117_apps/mobile-buyer/tsconfig.spec.json": {"targets": {}}, "6950946496078855561_apps/web-admin/tsconfig.app.json": {"targets": {}}, "4226200863860033919_apps/web-admin/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-admin"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.app.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "17652026406467776279_apps/web-admin/tsconfig.spec.json": {"targets": {}}, "11460667889045260769_apps/web-admin-e2e/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-admin-e2e"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/out-tsc/playwright/**/*.d.ts", "{projectRoot}/out-tsc/playwright/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "17770663978089268691_apps/web-superadmin/tsconfig.app.json": {"targets": {}}, "6850537580015563885_apps/web-superadmin/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-superadmin"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.app.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "15566435259079586315_apps/web-superadmin/tsconfig.spec.json": {"targets": {}}, "13701509762304177226_libs/auth/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["build", "^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "libs/auth"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.lib.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "6197472784608946732_libs/auth/tsconfig.lib.json": {"targets": {"build": {"dependsOn": ["^build"], "command": "tsc --build tsconfig.lib.json", "options": {"cwd": "libs/auth"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/dist"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Builds the project with `tsc`.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/auth --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/auth"}}}, "8691930203397857507_libs/auth/tsconfig.spec.json": {"targets": {}}, "10695571162359538053_libs/ui/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["build", "^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "libs/ui"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.lib.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "2481832307910197261_libs/ui/tsconfig.lib.json": {"targets": {"build": {"dependsOn": ["^build"], "command": "tsc --build tsconfig.lib.json", "options": {"cwd": "libs/ui"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/dist"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Builds the project with `tsc`.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/ui --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/ui"}}}, "8686773731347520617_libs/ui/tsconfig.spec.json": {"targets": {}}, "16435055115551803187_libs/models/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["build", "^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "libs/models"}, "cache": true, "inputs": ["production", {"dependentTasksOutputFiles": "**/*.d.ts"}, {"externalDependencies": ["typescript"]}], "outputs": [], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "15061778381619451417_libs/models/tsconfig.lib.json": {"targets": {"build": {"dependsOn": ["^build"], "command": "tsc --build tsconfig.lib.json", "options": {"cwd": "libs/models"}, "cache": true, "inputs": ["{projectRoot}/package.json", "{workspaceRoot}/tsconfig.base.json", "{projectRoot}/tsconfig.lib.json", "{projectRoot}/src/**/*.ts", "!{projectRoot}/jest.config.ts", "!{projectRoot}/src/**/*.spec.ts", "!{projectRoot}/src/**/*.test.ts", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/**/*.d.ts.map", "{projectRoot}/dist/tsconfig.lib.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Builds the project with `tsc`.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/models --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/models"}}}, "7179555118320329361_libs/models/tsconfig.spec.json": {"targets": {}}, "6369552528510044006_apps/backend/tsconfig.app.json": {"targets": {}}, "13384752066439430190_apps/backend/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/backend"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.app.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "10254996194020113023_apps/backend/tsconfig.spec.json": {"targets": {}}, "7463444952058546343_apps/backend-e2e/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/backend-e2e"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/out-tsc/@nasaga-monorepo/backend-e2e/**/*.d.ts", "{projectRoot}/out-tsc/@nasaga-monorepo/backend-e2e/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "14143965520551945457_apps/mobile-agent/tsconfig.app.json": {"targets": {}}, "5839324527799794601_apps/mobile-agent/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/mobile-agent"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.app.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "8569950613975621642_apps/mobile-agent/tsconfig.spec.json": {"targets": {}}, "235712551693495941_apps/mobile-buyer/tsconfig.app.json": {"targets": {}}, "16810796294432615228_apps/mobile-buyer/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/mobile-buyer"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.app.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "9737909127723025237_apps/mobile-buyer/tsconfig.spec.json": {"targets": {}}, "7540372006850465739_apps/web-admin/tsconfig.app.json": {"targets": {}}, "5810466947425961104_apps/web-admin/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-admin"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.app.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "9147094219112392339_apps/web-admin/tsconfig.spec.json": {"targets": {}}, "9772511053325910180_apps/web-admin-e2e/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-admin-e2e"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/out-tsc/playwright/**/*.d.ts", "{projectRoot}/out-tsc/playwright/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "8336394236896543567_apps/web-superadmin/tsconfig.app.json": {"targets": {}}, "4747948586651212530_apps/web-superadmin/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-superadmin"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.app.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "12267226843263104442_apps/web-superadmin/tsconfig.spec.json": {"targets": {}}, "7454990655207735608_libs/auth/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["build", "^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "libs/auth"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.lib.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "5113928300200250328_libs/auth/tsconfig.lib.json": {"targets": {"build": {"dependsOn": ["^build"], "command": "tsc --build tsconfig.lib.json", "options": {"cwd": "libs/auth"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/dist"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Builds the project with `tsc`.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/auth --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/auth"}}}, "5425717844518220919_libs/auth/tsconfig.spec.json": {"targets": {}}, "16435661237593640227_libs/models/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["build", "^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "libs/models"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.lib.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "4884650895953938498_libs/models/tsconfig.lib.json": {"targets": {"build": {"dependsOn": ["^build"], "command": "tsc --build tsconfig.lib.json", "options": {"cwd": "libs/models"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/dist"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Builds the project with `tsc`.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/models --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/models"}}}, "13683050855812736455_libs/models/tsconfig.spec.json": {"targets": {}}, "10504623006424498775_libs/ui/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["build", "^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "libs/ui"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.lib.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "15911411537613625385_libs/ui/tsconfig.lib.json": {"targets": {"build": {"dependsOn": ["^build"], "command": "tsc --build tsconfig.lib.json", "options": {"cwd": "libs/ui"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/dist"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Builds the project with `tsc`.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/ui --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/ui"}}}, "2656683597567182512_libs/ui/tsconfig.spec.json": {"targets": {}}, "8597999941842026264_libs/utils/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["build", "^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "libs/utils"}, "cache": true, "inputs": ["production", {"dependentTasksOutputFiles": "**/*.d.ts"}, {"externalDependencies": ["typescript"]}], "outputs": [], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "6196326963851290018_libs/utils/tsconfig.lib.json": {"targets": {"build": {"dependsOn": ["^build"], "command": "tsc --build tsconfig.lib.json", "options": {"cwd": "libs/utils"}, "cache": true, "inputs": ["{projectRoot}/package.json", "{workspaceRoot}/tsconfig.base.json", "{projectRoot}/tsconfig.lib.json", "{projectRoot}/src/**/*.ts", "!{projectRoot}/jest.config.ts", "!{projectRoot}/src/**/*.spec.ts", "!{projectRoot}/src/**/*.test.ts", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/**/*.d.ts.map", "{projectRoot}/dist/tsconfig.lib.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Builds the project with `tsc`.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/utils --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/utils"}}}, "11145274114142061499_libs/utils/tsconfig.spec.json": {"targets": {}}, "7841265886190623586_apps/backend/tsconfig.app.json": {"targets": {}}, "14660327713599517131_apps/backend/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/backend"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.app.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "17191686780493291357_apps/backend/tsconfig.spec.json": {"targets": {}}, "17972383079927148315_apps/backend-e2e/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/backend-e2e"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/out-tsc/@nasaga-monorepo/backend-e2e/**/*.d.ts", "{projectRoot}/out-tsc/@nasaga-monorepo/backend-e2e/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "2175316203229332301_apps/mobile-agent/tsconfig.app.json": {"targets": {}}, "1272579249458886607_apps/mobile-agent/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/mobile-agent"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.app.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "10545410373804681563_apps/mobile-agent/tsconfig.spec.json": {"targets": {}}, "14493038882979711011_apps/mobile-buyer/tsconfig.app.json": {"targets": {}}, "10036594127515755123_apps/mobile-buyer/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/mobile-buyer"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.app.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "13689009039145114946_apps/mobile-buyer/tsconfig.spec.json": {"targets": {}}, "8888227460249702444_apps/web-admin/tsconfig.app.json": {"targets": {}}, "4909194951801784841_apps/web-admin/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-admin"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.app.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "8034568796384183027_apps/web-admin/tsconfig.spec.json": {"targets": {}}, "2112370101044551814_apps/web-admin-e2e/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-admin-e2e"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/out-tsc/playwright/**/*.d.ts", "{projectRoot}/out-tsc/playwright/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "14969223537362455017_apps/web-superadmin/tsconfig.app.json": {"targets": {}}, "17197439263198165876_apps/web-superadmin/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-superadmin"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.app.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "15738517344668285464_apps/web-superadmin/tsconfig.spec.json": {"targets": {}}, "5488422975268752446_libs/auth/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["build", "^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "libs/auth"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.lib.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "3654826729519398362_libs/auth/tsconfig.lib.json": {"targets": {"build": {"dependsOn": ["^build"], "command": "tsc --build tsconfig.lib.json", "options": {"cwd": "libs/auth"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/dist"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Builds the project with `tsc`.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/auth --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/auth"}}}, "3891958301621090895_libs/auth/tsconfig.spec.json": {"targets": {}}, "13245854360565515134_libs/models/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["build", "^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "libs/models"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.lib.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "4363737560322981390_libs/models/tsconfig.lib.json": {"targets": {"build": {"dependsOn": ["^build"], "command": "tsc --build tsconfig.lib.json", "options": {"cwd": "libs/models"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/dist"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Builds the project with `tsc`.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/models --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/models"}}}, "4678130081274577440_libs/models/tsconfig.spec.json": {"targets": {}}, "16546889593946244235_libs/ui/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["build", "^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "libs/ui"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.lib.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "7554980945321096423_libs/ui/tsconfig.lib.json": {"targets": {"build": {"dependsOn": ["^build"], "command": "tsc --build tsconfig.lib.json", "options": {"cwd": "libs/ui"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/dist"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Builds the project with `tsc`.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/ui --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/ui"}}}, "14509245784461372255_libs/ui/tsconfig.spec.json": {"targets": {}}, "12042272939033602921_libs/utils/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["build", "^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "libs/utils"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.lib.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "4305498293703586723_libs/utils/tsconfig.lib.json": {"targets": {"build": {"dependsOn": ["^build"], "command": "tsc --build tsconfig.lib.json", "options": {"cwd": "libs/utils"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/dist"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Builds the project with `tsc`.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/utils --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/utils"}}}, "12368543942539428496_libs/utils/tsconfig.spec.json": {"targets": {}}}