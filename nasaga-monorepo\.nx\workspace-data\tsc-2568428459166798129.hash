{"12724049630267944764_apps/backend/tsconfig.app.json": {"targets": {}}, "527589099223796094_apps/backend/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/backend"}, "cache": true, "inputs": ["production", {"dependentTasksOutputFiles": "**/*.d.ts"}, {"externalDependencies": ["typescript"]}], "outputs": [], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "18328816757247082444_apps/backend/tsconfig.spec.json": {"targets": {}}, "15180641133882165643_apps/backend-e2e/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/backend-e2e"}, "cache": true, "inputs": ["{projectRoot}/package.json", "{workspaceRoot}/tsconfig.base.json", "{projectRoot}/tsconfig.json", "{projectRoot}/jest.config.ts", "{projectRoot}/src/**/*.ts", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/out-tsc/@nasaga-monorepo/backend-e2e/**/*.d.ts", "{projectRoot}/out-tsc/@nasaga-monorepo/backend-e2e/**/*.d.ts.map", "{projectRoot}/out-tsc/@nasaga-monorepo/backend-e2e/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "4581922222011074654_apps/mobile-agent/tsconfig.app.json": {"targets": {}}, "113285610904969570_apps/mobile-agent/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/mobile-agent"}, "cache": true, "inputs": ["production", {"dependentTasksOutputFiles": "**/*.d.ts"}, {"externalDependencies": ["typescript"]}], "outputs": [], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "13707946270618434827_apps/mobile-agent/tsconfig.spec.json": {"targets": {}}, "15501524302932482987_apps/mobile-buyer/tsconfig.app.json": {"targets": {}}, "4852637795616245057_apps/mobile-buyer/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/mobile-buyer"}, "cache": true, "inputs": ["production", {"dependentTasksOutputFiles": "**/*.d.ts"}, {"externalDependencies": ["typescript"]}], "outputs": [], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "12566315064164942962_apps/mobile-buyer/tsconfig.spec.json": {"targets": {}}, "1261598312044252946_apps/web-admin/tsconfig.app.json": {"targets": {}}, "12211934115152422954_apps/web-admin/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-admin"}, "cache": true, "inputs": ["production", {"dependentTasksOutputFiles": "**/*.d.ts"}, {"externalDependencies": ["typescript"]}], "outputs": [], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "9239277115938994081_apps/web-admin/tsconfig.spec.json": {"targets": {}}, "1310754206894401161_apps/web-admin-e2e/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-admin-e2e"}, "cache": true, "inputs": ["{projectRoot}/package.json", "{workspaceRoot}/tsconfig.base.json", "{projectRoot}/tsconfig.json", "{projectRoot}/**/*.ts", "{projectRoot}/**/*.js", "{projectRoot}/playwright.config.ts", "{projectRoot}/src/**/*.spec.ts", "{projectRoot}/src/**/*.spec.js", "{projectRoot}/src/**/*.test.ts", "{projectRoot}/src/**/*.test.js", "{projectRoot}/src/**/*.d.ts", "!{projectRoot}/out-tsc", "!{projectRoot}/test-output", "!{projectRoot}/eslint.config.js", "!{projectRoot}/eslint.config.mjs", "!{projectRoot}/eslint.config.cjs", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/out-tsc/playwright/**/*.d.ts", "{projectRoot}/out-tsc/playwright/**/*.d.ts.map", "{projectRoot}/out-tsc/playwright/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "2776668409396343006_apps/web-superadmin/tsconfig.app.json": {"targets": {}}, "4023224621824117338_apps/web-superadmin/tsconfig.e2e.json": {"targets": {}}, "7214759469029641254_apps/web-superadmin/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/web-superadmin"}, "cache": true, "inputs": ["production", {"dependentTasksOutputFiles": "**/*.d.ts"}, {"externalDependencies": ["typescript"]}], "outputs": [], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "3342784127744454434_apps/web-superadmin/tsconfig.spec.json": {"targets": {}}, "10139931800075633002_libs/auth/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["build", "^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "libs/auth"}, "cache": true, "inputs": ["production", {"dependentTasksOutputFiles": "**/*.d.ts"}, {"externalDependencies": ["typescript"]}], "outputs": [], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "3141995257961876624_libs/auth/tsconfig.lib.json": {"targets": {"build": {"dependsOn": ["^build"], "command": "tsc --build tsconfig.lib.json", "options": {"cwd": "libs/auth"}, "cache": true, "inputs": ["{projectRoot}/package.json", "{workspaceRoot}/tsconfig.base.json", "{projectRoot}/tsconfig.lib.json", "{projectRoot}/src/**/*.ts", "!{projectRoot}/jest.config.ts", "!{projectRoot}/src/**/*.spec.ts", "!{projectRoot}/src/**/*.test.ts", {"dependentTasksOutputFiles": "**/*.d.ts"}, {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/**/*.d.ts.map", "{projectRoot}/dist/tsconfig.lib.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Builds the project with `tsc`.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/auth --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/auth"}}}, "2401957696508161729_libs/auth/tsconfig.spec.json": {"targets": {}}, "16322227303407265253_libs/models/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["build", "^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "libs/models"}, "cache": true, "inputs": ["production", {"dependentTasksOutputFiles": "**/*.d.ts"}, {"externalDependencies": ["typescript"]}], "outputs": [], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "10748515320372323928_libs/models/tsconfig.lib.json": {"targets": {"build": {"dependsOn": ["^build"], "command": "tsc --build tsconfig.lib.json", "options": {"cwd": "libs/models"}, "cache": true, "inputs": ["{projectRoot}/package.json", "{workspaceRoot}/tsconfig.base.json", "{projectRoot}/tsconfig.lib.json", "{projectRoot}/src/**/*.ts", "!{projectRoot}/jest.config.ts", "!{projectRoot}/src/**/*.spec.ts", "!{projectRoot}/src/**/*.test.ts", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/**/*.d.ts.map", "{projectRoot}/dist/tsconfig.lib.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Builds the project with `tsc`.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/models --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/models"}}}, "13551485430055169101_libs/models/tsconfig.spec.json": {"targets": {}}, "8064180781935156538_libs/ui/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["build", "^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "libs/ui"}, "cache": true, "inputs": ["production", {"dependentTasksOutputFiles": "**/*.d.ts"}, {"externalDependencies": ["typescript"]}], "outputs": [], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "5019255525242294545_libs/ui/tsconfig.lib.json": {"targets": {"build": {"dependsOn": ["^build"], "command": "tsc --build tsconfig.lib.json", "options": {"cwd": "libs/ui"}, "cache": true, "inputs": ["{projectRoot}/package.json", "{workspaceRoot}/tsconfig.base.json", "{projectRoot}/tsconfig.lib.json", "{projectRoot}/src/**/*.js", "{projectRoot}/src/**/*.jsx", "{projectRoot}/src/**/*.ts", "{projectRoot}/src/**/*.tsx", "!{projectRoot}/out-tsc", "!{projectRoot}/dist", "!{projectRoot}/**/*.spec.ts", "!{projectRoot}/**/*.test.ts", "!{projectRoot}/**/*.spec.tsx", "!{projectRoot}/**/*.test.tsx", "!{projectRoot}/**/*.spec.js", "!{projectRoot}/**/*.test.js", "!{projectRoot}/**/*.spec.jsx", "!{projectRoot}/**/*.test.jsx", "!{projectRoot}/jest.config.ts", "!{projectRoot}/src/**/*.spec.ts", "!{projectRoot}/src/**/*.test.ts", "!{projectRoot}/eslint.config.js", "!{projectRoot}/eslint.config.cjs", "!{projectRoot}/eslint.config.mjs", {"dependentTasksOutputFiles": "**/*.d.ts"}, {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/**/*.d.ts.map", "{projectRoot}/dist/tsconfig.lib.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Builds the project with `tsc`.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/ui --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/ui"}}}, "4922951177603546201_libs/ui/tsconfig.spec.json": {"targets": {}}, "14532549459026347144_libs/utils/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["build", "^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "libs/utils"}, "cache": true, "inputs": ["production", {"dependentTasksOutputFiles": "**/*.d.ts"}, {"externalDependencies": ["typescript"]}], "outputs": [], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "15969045577082603_libs/utils/tsconfig.lib.json": {"targets": {"build": {"dependsOn": ["^build"], "command": "tsc --build tsconfig.lib.json", "options": {"cwd": "libs/utils"}, "cache": true, "inputs": ["{projectRoot}/package.json", "{workspaceRoot}/tsconfig.base.json", "{projectRoot}/tsconfig.lib.json", "{projectRoot}/src/**/*.ts", "!{projectRoot}/jest.config.ts", "!{projectRoot}/src/**/*.spec.ts", "!{projectRoot}/src/**/*.test.ts", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/**/*.d.ts.map", "{projectRoot}/dist/tsconfig.lib.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Builds the project with `tsc`.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nasaga-monorepo/utils --includeDependentProjects -- npx nx build-deps @nasaga-monorepo/utils"}}}, "7646761214510003397_libs/utils/tsconfig.spec.json": {"targets": {}}, "9258777198626959152_libs/auth/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["build", "^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "libs/auth"}, "cache": true, "inputs": ["production", {"dependentTasksOutputFiles": "**/*.d.ts"}, {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "1974792816518736003_libs/models/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["build", "^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "libs/models"}, "cache": true, "inputs": ["production", {"dependentTasksOutputFiles": "**/*.d.ts"}, {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "3710818151297407646_libs/ui/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["build", "^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "libs/ui"}, "cache": true, "inputs": ["production", {"dependentTasksOutputFiles": "**/*.d.ts"}, {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "5503070052164700993_libs/utils/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["build", "^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "libs/utils"}, "cache": true, "inputs": ["production", {"dependentTasksOutputFiles": "**/*.d.ts"}, {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}}