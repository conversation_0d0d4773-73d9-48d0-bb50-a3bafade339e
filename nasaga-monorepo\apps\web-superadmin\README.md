# Super Admin Portal - Web Application

## Overview
This is the Super Admin dashboard for the Nasaga Real Estate Management Platform. It provides high-level administrative access for managing users and system operations.

## Features Implemented

### Pages
- `/login` - Super Admin login page with custom branding
- `/dashboard/users` - User management dashboard

### Components
- **UserTable** - Displays all users in a sortable table format
- **RoleFilter** - Allows filtering users by role (All, Super Admin, Admin, Agent, Buyer)
- **DashboardLayout** - Main layout with navigation and logout functionality

### Security
- All routes are protected with `withSuperAdminGuard('superAdmin')` HOC
- Automatic redirects for unauthorized access
- Secure logout functionality

### E2E Tests
- Playwright tests configured in `/e2e` directory
- Tests cover login flow, dashboard navigation, and user management features
- Some tests are skipped pending proper Firebase auth mocking implementation

## Project Structure

```
apps/web-superadmin/
├── src/
│   ├── app/
│   │   ├── components/
│   │   │   ├── dashboard-layout.tsx    # Main dashboard layout
│   │   │   ├── role-filter.tsx         # User role filtering component
│   │   │   └── user-table.tsx          # User data table component
│   │   ├── pages/
│   │   │   ├── login.tsx               # Super admin login page
│   │   │   └── users-dashboard.tsx     # User management dashboard
│   │   └── app.tsx                     # Main app with routing
│   └── main.tsx                        # Application entry point
├── e2e/
│   └── example.spec.ts                 # Playwright e2e tests
├── playwright.config.ts               # Playwright configuration
└── README.md                          # This file
```

## Routes

| Route | Component | Protection | Description |
|-------|-----------|------------|-------------|
| `/` | Redirect to `/login` | Public | Root redirect |
| `/login` | LoginPage | Guest only | Super admin login |
| `/dashboard` | Redirect to `/dashboard/users` | Super admin | Dashboard redirect |
| `/dashboard/users` | UsersDashboard | Super admin | User management |

## Authentication & Authorization

The application uses the shared `@nasaga-monorepo/auth` library with Firebase authentication. All dashboard routes are protected with the `withSuperAdminGuard` HOC which ensures only users with the `superAdmin` role can access the interface.

## Known Issues

### Build Issue - Path with Spaces
There is currently a build issue with SWC compilation failing due to the space in the directory path (`nasaga limted`). This is a known issue with SWC on Windows.

**Error**: `Cannot find module 'C:\Users\<USER>\Desktop\nasaga'`

**Workaround**: Move the project to a directory path without spaces, or use the development server directly:

```bash
npx nx serve web-superadmin
```

### Development
To run the development server:

```bash
npx nx serve web-superadmin
```

### Testing
To run e2e tests:

```bash
npx nx e2e web-superadmin
```

## Future Enhancements

1. **User Details Page** - Implement `/dashboard/users/:id` for detailed user management
2. **User Creation/Editing** - Add forms for creating and editing users
3. **System Analytics** - Add dashboard with system metrics and analytics
4. **Settings Page** - System configuration interface
5. **Audit Logs** - Track administrative actions
6. **Bulk Operations** - Mass user operations (import/export, bulk role changes)

## Dependencies

- `@nasaga-monorepo/auth` - Authentication and authorization utilities
- `@nasaga-monorepo/models` - Type definitions and data models
- `@nasaga-monorepo/ui` - Shared UI components
- `react-router-dom` - Client-side routing
- `tailwindcss` - Styling framework

## Notes

- The application uses dummy data for user listing. In production, this should be replaced with actual Firestore queries.
- Authentication mocking needs to be implemented for comprehensive e2e testing.
- The UI follows the existing design system established in the shared UI library.
