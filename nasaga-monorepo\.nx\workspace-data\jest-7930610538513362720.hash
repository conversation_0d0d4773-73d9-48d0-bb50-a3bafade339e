{"13743440210270389230": {"targets": {"test": {"command": "jest", "options": {"cwd": "apps/backend", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{projectRoot}\\test-output\\jest\\coverage"]}}}, "861888202209322097": {"targets": {"test": {"command": "jest", "options": {"cwd": "apps/mobile-agent", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["jest", "jest-expo"]}], "outputs": ["{workspaceRoot}\\coverage\\apps\\mobile-agent"]}}}, "15777541343521244407": {"targets": {"test": {"command": "jest", "options": {"cwd": "apps/mobile-buyer", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["jest", "jest-expo"]}], "outputs": ["{workspaceRoot}\\coverage\\apps\\mobile-buyer"]}}}, "9765991815132303902": {"targets": {"test": {"command": "jest", "options": {"cwd": "apps/web-admin", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{projectRoot}\\test-output\\jest\\coverage"]}}}, "15281869526468459129": {"targets": {"test": {"command": "jest", "options": {"cwd": "apps/web-superadmin", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{projectRoot}\\test-output\\jest\\coverage"]}}}, "2700098316744457335": {"targets": {"test": {"command": "jest", "options": {"cwd": "libs/auth", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{projectRoot}\\test-output\\jest\\coverage"]}}}, "11468265531591010069": {"targets": {"test": {"command": "jest", "options": {"cwd": "libs/models", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{projectRoot}\\test-output\\jest\\coverage"]}}}, "16286947233042166494": {"targets": {"test": {"command": "jest", "options": {"cwd": "libs/ui", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{projectRoot}\\test-output\\jest\\coverage"]}}}, "8959951587138451654": {"targets": {"test": {"command": "jest", "options": {"cwd": "libs/utils", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{projectRoot}\\test-output\\jest\\coverage"]}}}, "5974193401465269665": {"targets": {"test": {"command": "jest", "options": {"cwd": "apps/web-superadmin", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{projectRoot}\\test-output\\jest\\coverage"]}}}, "4155130424493520680": {"targets": {"test": {"command": "jest", "options": {"cwd": "apps/web-superadmin", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{projectRoot}\\test-output\\jest\\coverage"]}}}, "16284684019636700430": {"targets": {"test": {"command": "jest", "options": {"cwd": "apps/web-superadmin", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{projectRoot}\\test-output\\jest\\coverage"]}}}, "12069106233659839446": {"targets": {"test": {"command": "jest", "options": {"cwd": "apps/web-admin", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{projectRoot}\\test-output\\jest\\coverage"]}}}, "5215773452547419099": {"targets": {"test": {"command": "jest", "options": {"cwd": "apps/web-admin", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{projectRoot}\\test-output\\jest\\coverage"]}}}, "11873517660363577345": {"targets": {"test": {"command": "jest", "options": {"cwd": "apps/web-admin", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{projectRoot}\\test-output\\jest\\coverage"]}}}, "18000130972229157868": {"targets": {"test": {"command": "jest", "options": {"cwd": "apps/web-admin", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{projectRoot}\\test-output\\jest\\coverage"]}}}, "6005441767220464009": {"targets": {"test": {"command": "jest", "options": {"cwd": "apps/web-admin", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{projectRoot}\\test-output\\jest\\coverage"]}}}}