{"1700976211143895523": {"targets": {"test": {"command": "jest", "options": {"cwd": "apps/web-admin", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{projectRoot}\\test-output\\jest\\coverage"]}}}, "10035690436165147830": {"targets": {"test": {"command": "jest", "options": {"cwd": "apps/web-superadmin", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{projectRoot}\\test-output\\jest\\coverage"]}}}, "2833129540851713190": {"targets": {"test": {"command": "jest", "options": {"cwd": "apps/mobile-agent", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["jest", "jest-expo"]}], "outputs": ["{workspaceRoot}\\coverage\\apps\\mobile-agent"]}}}, "13902410095162713468": {"targets": {"test": {"command": "jest", "options": {"cwd": "apps/mobile-buyer", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["jest", "jest-expo"]}], "outputs": ["{workspaceRoot}\\coverage\\apps\\mobile-buyer"]}}}, "14407417013506523546": {"targets": {"test": {"command": "jest", "options": {"cwd": "apps/backend", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{projectRoot}\\test-output\\jest\\coverage"]}}}, "8492450968933216563": {"targets": {"test": {"command": "jest", "options": {"cwd": "libs/ui", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{projectRoot}\\test-output\\jest\\coverage"]}}}, "14273147882644337586": {"targets": {"test": {"command": "jest", "options": {"cwd": "libs/auth", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{projectRoot}\\test-output\\jest\\coverage"]}}}, "4057324878904987581": {"targets": {"test": {"command": "jest", "options": {"cwd": "libs/models", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{projectRoot}\\test-output\\jest\\coverage"]}}}, "2211460997411293535": {"targets": {"test": {"command": "jest", "options": {"cwd": "libs/utils", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}\\jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{projectRoot}\\test-output\\jest\\coverage"]}}}}