# Epic 3: Buyer Property Discovery & Interaction

**Epic Goal**: Provide a seamless and feature-rich experience for buyers to discover, explore, and engage with property listings through their dedicated mobile application, enabling efficient search and informed decision-making.

## Story 3.1 Buyer Mobile App Setup & Property Browse

**As a** Buyer,  
**I want** to securely access the Buyer Mobile App and browse available property listings,  
**so that** I can begin my property search.

### Acceptance Criteria

1.  **1**: The Buyer Mobile App is successfully built with React Native/Expo and connects to Firebase.  
2.  **2**: A secure login and registration flow (using Firebase Auth) is implemented for buyers.  
3.  **3**: Buyers are presented with a list of active and approved property listings upon login.  
4.  **4**: Property listings are displayed with key information (e.g., image, title, price, location).  
5.  **5**: Buyer app access is restricted to authenticated users with the 'buyer' role.

## Story 3.2 Property Search, Filter, and Detail View

**As a** Buyer,  
**I want** to search, filter, and view detailed information for properties,  
**so that** I can efficiently find properties that match my specific criteria.

### Acceptance Criteria

1.  **1**: Buyers can search for properties by keywords (e.g., address, city, property type).  
2.  **2**: Buyers can filter properties by criteria such as price range, number of bedrooms/bathrooms, and property type.  
3.  **3**: Selecting a property from the list displays a comprehensive detail page including all listing information, images, and agent contact.  
4.  **4**: Property data is fetched from Firestore in near real-time.  
5.  **5**: Buyers can tap on the agent's contact details to initiate a phone call or email outside the app.

## Story 3.3 Favorite Properties

**As a** Buyer,  
**I want** to "favorite" (save) properties,  
**so that** I can easily keep track of listings that interest me.

### Acceptance Criteria

1.  **1**: Buyers can tap an icon (e.g., heart) on a property listing to favorite it.  
2.  **2**: Favorited properties are saved to the buyer's profile in Firestore.  
3.  **3**: Buyers can view a dedicated list of all their favorited properties.  
4.  **4**: Buyers can remove properties from their favorites list.  
5.  **5**: Favoriting/unfavoriting updates immediately for the user.
