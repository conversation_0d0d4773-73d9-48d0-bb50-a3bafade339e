# **High Level Architecture**

## **Technical Summary**

The Nasaga Real Estate Management Platform will employ a hybrid full-stack architecture leveraging **Firebase services (Firestore, Authentication, Storage, Cloud Messaging)** for core BaaS functionalities and a **Node.js/Express backend API** for custom business logic and integrations. All applications (Admin Web, Super Admin Web, Agent Mobile, Buyer Mobile, Backend API) will reside within a **monorepo managed by Nx**, enabling extensive code sharing and optimized CI/CD. Frontend applications will be built with **React (web) and React Native with Expo (mobile)**, ensuring a consistent and performant user experience across platforms. This architecture prioritizes scalability, real-time data flow, and maintainability to support growth and efficient development.

## **Platform and Infrastructure Choice**

* **Platform:** Google Cloud Platform (GCP) via Firebase  
* **Key Services:**  
  * Firebase Authentication: For secure, role-based user authentication.  
  * Firebase Firestore: For flexible, scalable, real-time NoSQL database for structured data.  
  * Firebase Storage: For secure and scalable storage of property images and documents.  
  * Firebase Cloud Messaging (FCM): For real-time push notifications.  
  * Google Cloud Functions (Implicit with Firebase functions): For server-side backend logic and custom API endpoints.  
* **Deployment Host and Regions:** Firebase (automatic hosting and scaling), specific regions can be configured within Firebase/GCP settings as needed for data locality and performance, but default Firebase deployment regions will be used initially.

## **Repository Structure**

As established in the PRD and reinforced by the research report, a monorepo managed by Nx is the chosen structure.

* **Structure:** Monorepo  
* **Monorepo Tool:** Nx  
* **Package Organization:** The monorepo will follow a standard Nx structure to maximize code sharing and maintain clear separation of concerns.  
  * apps/: Will contain individual deployable applications.  
    * admin-web/: React web application for Admin dashboard.  
    * super-admin-web/: React web application for Super Admin dashboard.  
    * agent-mobile/: React Native with Expo mobile application for Agents.  
    * buyer-mobile/: React Native with Expo mobile application for Buyers.  
    * backend/: Node.js/Express application for custom API endpoints and server-side logic.  
  * packages/: Will contain shared libraries.  
    * ui/: Reusable UI components compatible with both React and React Native (using react-native-web).  
    * utils/: Shared utility functions (e.g., API clients, formatters, Firebase SDK wrappers).  
    * types/: Shared TypeScript types and interfaces (e.g., User, Property interfaces).

## **High Level Architecture Diagram**

`graph TD`  
    `actor User`  
    `User -- Web Browser --> AdminWeb[Admin Web Dashboard (React/TS)]`  
    `User -- Web Browser --> SuperAdminWeb[Super Admin Web Dashboard (React/TS)]`  
    `User -- Mobile Device --> AgentMobile[Agent Mobile App (RN/Expo)]`  
    `User -- Mobile Device --> BuyerMobile[Buyer Mobile App (RN/Expo)]`

    `AdminWeb --> BackendAPI`  
    `SuperAdminWeb --> BackendAPI`  
    `AgentMobile --> BackendAPI`  
    `BuyerMobile --> BackendAPI`

    `BackendAPI[Backend API (Node.js/Express)] -- Admin SDK --> FirebaseAuth(Firebase Authentication)`  
    `BackendAPI -- Admin SDK --> Firestore(Firebase Firestore DB)`  
    `BackendAPI -- Admin SDK --> FirebaseStorage(Firebase Storage)`  
    `BackendAPI -- Admin SDK --> FirebaseCM(Firebase Cloud Messaging)`

    `FirebaseAuth -- SDK --> AdminWeb`  
    `FirebaseAuth -- SDK --> SuperAdminWeb`  
    `FirebaseAuth -- SDK --> AgentMobile`  
    `FirebaseAuth -- SDK --> BuyerMobile`

    `Firestore -- SDK --> AgentMobile`  
    `Firestore -- SDK --> BuyerMobile`  
    `FirebaseStorage -- SDK --> AgentMobile`

    `FirebaseCM -- SDK --> AgentMobile`  
    `FirebaseCM -- SDK --> BuyerMobile`

    `subgraph Firebase Ecosystem`  
        `FirebaseAuth`  
        `Firestore`  
        `FirebaseStorage`  
        `FirebaseCM`  
    `end`

    `subgraph Nasaga Platform Applications`  
        `AdminWeb`  
        `SuperAdminWeb`  
        `AgentMobile`  
        `BuyerMobile`  
        `BackendAPI`  
    `end`

    `NasagaPlatformApplications --> FirebaseEcosystem`

## **Architectural Patterns**

Here are the key high-level patterns that will guide the architecture. For each, I'm providing a recommendation with rationale, aligning with your PRD and the monorepo tooling research.

1. **Overall Architectural Style:** **Hybrid Serverless & Backend-for-Frontend (BFF)**  
   * *Rationale:* Leveraging Firebase services (Authentication, Firestore, Storage, Cloud Messaging) provides a serverless backend-as-a-service foundation, minimizing operational overhead and ensuring scalability. The Node.js/Express API will act as a Backend-for-Frontend (BFF) layer where needed, encapsulating complex logic, performing data transformations, and integrating with Firebase Admin SDK for privileged operations, tailored to the specific needs of the web dashboards and mobile apps. This aligns with the PRD's "hybrid approach" for service architecture.  
2. **Code Organization Pattern:** **Monorepo with Domain/Feature Slicing**  
   * *Rationale:* As specified in the PRD, an Nx monorepo will house all applications and shared libraries. Within this, code will be organized by domain or feature (e.g., users, properties, auth) using Nx's library system to enforce modularity, promote code sharing, and enable clear ownership. This supports maintainability and developer experience.  
3. **Data Persistence Pattern:** **Document-Oriented Database (Firebase Firestore)**  
   * *Rationale:* Firebase Firestore is chosen for its real-time synchronization capabilities, scalability, and seamless integration with Firebase services. Its document-oriented nature is flexible for evolving property and user data structures.  
4. **Communication Patterns:** **Real-time Database Sync (Firestore) & RESTful API**  
   * *Rationale:* For real-time updates (e.g., property status changes, notifications), direct integration with Firestore's real-time capabilities will be utilized where appropriate. For custom business logic, secure operations (e.g., user role management by admins), and complex queries not suitable for direct client-to-Firestore access, a **RESTful API** exposed by the Node.js/Express backend will serve as the primary communication method.
