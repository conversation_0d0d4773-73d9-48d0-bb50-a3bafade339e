import React, { useState, useMemo } from 'react';
import { User, UserRole } from '@nasaga-monorepo/models';
import { UserTable } from '../components/user-table';
import { RoleFilter } from '../components/role-filter';
import { withSuperAdminGuard } from '@nasaga-monorepo/auth';

// TODO: Replace with actual data fetching from Firestore
const dummyUsers: User[] = [
  {
    uid: '1',
    displayName: 'Super Admin',
    email: '<EMAIL>',
    role: 'superAdmin',
    createdAt: { seconds: 1672531200, nanoseconds: 0 } as any,
  },
  {
    uid: '2',
    displayName: 'Regular Admin',
    email: '<EMAIL>',
    role: 'admin',
    createdAt: { seconds: 1672531200, nanoseconds: 0 } as any,
  },
  {
    uid: '3',
    displayName: '<PERSON> (Agent)',
    email: '<EMAIL>',
    role: 'agent',
    createdAt: { seconds: 1672531200, nanoseconds: 0 } as any,
  },
  {
    uid: '4',
    displayName: '<PERSON> (Buyer)',
    email: '<EMAIL>',
    role: 'buyer',
    createdAt: { seconds: 1672531200, nanoseconds: 0 } as any,
  },
];

const UsersDashboard: React.FC = () => {
  const [selectedRole, setSelectedRole] = useState<UserRole | 'all'>('all');

  const filteredUsers = useMemo(() => {
    if (selectedRole === 'all') {
      return dummyUsers;
    }
    return dummyUsers.filter((user) => user.role === selectedRole);
  }, [selectedRole]);

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">User Management</h1>
        <p className="mt-1 text-sm text-gray-600">
          View, filter, and manage all users in the Nasaga platform.
        </p>
      </div>

      {/* Role Filter and Search Bar */}
      <div className="flex flex-col md:flex-row gap-4 items-center justify-between p-4 bg-white rounded-lg shadow">
        <RoleFilter selectedRole={selectedRole} onRoleChange={setSelectedRole} />
        <div className="relative w-full md:w-auto">
          <input
            type="text"
            placeholder="Search users by name or email..."
            className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 transition-shadow"
          />
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <svg className="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
            </svg>
          </div>
        </div>
      </div>

      {/* User Table */}
      <UserTable users={filteredUsers} />
    </div>
  );
};

export default withSuperAdminGuard(UsersDashboard);
