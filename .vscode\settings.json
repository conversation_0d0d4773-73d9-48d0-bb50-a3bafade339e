{"editor.tabSize": 2, "editor.insertSpaces": true, "editor.detectIndentation": false, "editor.formatOnSave": true, "editor.formatOnPaste": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "editor.rulers": [100], "files.eol": "\n", "files.insertFinalNewline": true, "files.trimTrailingWhitespace": true, "files.trimFinalNewlines": true, "typescript.preferences.importModuleSpecifier": "relative", "typescript.suggest.autoImports": true, "typescript.preferences.includePackageJsonAutoImports": "on", "emmet.includeLanguages": {"typescript": "typescript", "typescriptreact": "typescriptreact"}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/build": true, "**/.git": true, "**/ios/build": true, "**/android/build": true, "**/android/.gradle": true}, "files.watcherExclude": {"**/node_modules/**": true, "**/dist/**": true, "**/build/**": true, "**/ios/build/**": true, "**/android/build/**": true, "**/android/.gradle/**": true}, "files.associations": {"*.tsx": "typescriptreact", "*.ts": "typescript"}, "typescript.updateImportsOnFileMove.enabled": "always", "javascript.updateImportsOnFileMove.enabled": "always", "eslint.workingDirectories": ["."], "prettier.requireConfig": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}