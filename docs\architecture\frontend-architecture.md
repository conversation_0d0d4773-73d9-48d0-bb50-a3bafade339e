# **Frontend Architecture**

## **Component Architecture**

### **Component Organization**

`apps/`  
`├── admin-web/`  
`│   └── src/`  
`│       ├── components/     # Application-specific UI components`  
`│       ├── pages/          # Page-level components/routes`  
`│       ├── layouts/        # Common layouts (e.g., dashboard layout)`  
`│       ├── services/       # API client services specific to admin-web`  
`│       ├── contexts/       # React Context providers for local state`  
`│       └── utils/          # Admin-web specific utilities`  
`├── super-admin-web/`  
`│   └── src/`  
`│       ├── components/     # Application-specific UI components`  
`│       ├── pages/          # Page-level components/routes`  
`│       ├── layouts/        # Common layouts`  
`│       ├── services/       # API client services specific to super-admin-web`  
`│       ├── contexts/       # React Context providers`  
`│       └── utils/          # Super-admin-web specific utilities`  
`├── agent-mobile/`  
`│   └── src/`  
`│       ├── components/     # Application-specific UI components`  
`│       ├── screens/        # Screen-level components/routes`  
`│       ├── navigation/     # React Navigation setup`  
`│       ├── services/       # API client services specific to agent-mobile`  
`│       ├── hooks/          # Custom React Native hooks`  
`│       ├── contexts/       # React Context providers`  
`│       └── utils/          # Agent-mobile specific utilities`  
`├── buyer-mobile/`  
`│   └── src/`  
`│       ├── components/     # Application-specific UI components`  
`│       ├── screens/        # Screen-level components/routes`  
`│       ├── navigation/     # React Navigation setup`  
`│       ├── services/       # API client services specific to buyer-mobile`  
`│       ├── hooks/          # Custom React Native hooks`  
`│       ├── contexts/       # React Context providers`  
`│       └── utils/          # Buyer-mobile specific utilities`  
`packages/`  
`└── ui/                     # Shared UI components`  
    `└── src/`  
        `├── common/         # Generic UI components (buttons, inputs)`  
        `├── forms/          # Reusable form components`  
        `├── navigation/     # Shared navigation elements`  
        `├── layouts/        # Shared layout patterns`  
        `└── theme/          # Shared styling variables/theme setup`

### **Component Template**

`// For React Web components (e.g., packages/ui/src/common/Button/Button.tsx)`  
`import React from 'react';`

`interface ButtonProps {`  
  `children: React.ReactNode;`  
  `onClick: () => void;`  
  `variant?: 'primary' | 'secondary' | 'outline';`  
  `disabled?: boolean;`  
`}`

`const Button: React.FC<ButtonProps> = ({ children, onClick, variant = 'primary', disabled = false }) => {`  
  `const baseStyles = 'px-4 py-2 rounded-md font-semibold focus:outline-none focus:ring-2 focus:ring-opacity-75';`  
  `let variantStyles = '';`

  `switch (variant) {`  
    `case 'primary':`  
      `variantStyles = 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500';`  
      `break;`  
    `case 'secondary':`  
      `variantStyles = 'bg-gray-200 text-gray-800 hover:bg-gray-300 focus:ring-gray-400';`  
      `break;`  
    `case 'outline':`  
      `variantStyles = 'border border-blue-600 text-blue-600 hover:bg-blue-50 focus:ring-blue-500';`  
      `break;`  
  `}`

  `return (`  
    `<button`  
      ``className={`${baseStyles} ${variantStyles} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}``  
      `onClick={onClick}`  
      `disabled={disabled}`  
    `>`  
      `{children}`  
    `</button>`  
  `);`  
`};`

`export default Button;`

`// For React Native components (e.g., packages/ui/src/common/Button/Button.native.tsx)`  
`import React from 'react';`  
`import { TouchableOpacity, Text, StyleSheet, ViewStyle, TextStyle } from 'react-native';`

`interface ButtonProps {`  
  `children: string; // Text content for native button`  
  `onPress: () => void;`  
  `variant?: 'primary' | 'secondary' | 'outline';`  
  `disabled?: boolean;`  
  `style?: ViewStyle;`  
  `textStyle?: TextStyle;`  
`}`

`const Button: React.FC<ButtonProps> = ({ children, onPress, variant = 'primary', disabled = false, style, textStyle }) => {`  
  `const getVariantStyles = (): ViewStyle => {`  
    `switch (variant) {`  
      `case 'primary': return styles.primaryButton;`  
      `case 'secondary': return styles.secondaryButton;`  
      `case 'outline': return styles.outlineButton;`  
      `default: return styles.primaryButton;`  
    `}`  
  `};`

  `const getTextVariantStyles = (): TextStyle => {`  
    `switch (variant) {`  
      `case 'primary': return styles.primaryText;`  
      `case 'secondary': return styles.secondaryText;`  
      `case 'outline': return styles.outlineText;`  
      `default: return styles.primaryText;`  
    `}`  
  `};`

  `return (`  
    `<TouchableOpacity`  
      `style={[styles.baseButton, getVariantStyles(), disabled && styles.disabledButton, style]}`  
      `onPress={onPress}`  
      `disabled={disabled}`  
    `>`  
      `<Text style={[styles.baseText, getTextVariantStyles(), textStyle]}>{children}</Text>`  
    `</TouchableOpacity>`  
  `);`  
`};`

`const styles = StyleSheet.create({`  
  `baseButton: {`  
    `paddingVertical: 12,`  
    `paddingHorizontal: 20,`  
    `borderRadius: 8,`  
    `alignItems: 'center',`  
    `justifyContent: 'center',`  
  `},`  
  `baseText: {`  
    `fontWeight: '600',`  
  `},`  
  `primaryButton: {`  
    `backgroundColor: '#3B82F6', // blue-600`  
  `},`  
  `primaryText: {`  
    `color: '#FFFFFF',`  
  `},`  
  `secondaryButton: {`  
    `backgroundColor: '#E5E7EB', // gray-200`  
  `},`  
  `secondaryText: {`  
    `color: '#374151', // gray-800`  
  `},`  
  `outlineButton: {`  
    `borderColor: '#3B82F6',`  
    `borderWidth: 1,`  
  `},`  
  `outlineText: {`  
    `color: '#3B82F6',`  
  `},`  
  `disabledButton: {`  
    `opacity: 0.5,`  
  `},`  
`});`

`export default Button;`

### **Naming Conventions**

* **Components**: PascalCase for component files and names (e.g., UserProfile.tsx, Button.tsx).  
* **Hooks**: camelCase prefixed with use for custom hooks (e.g., useAuth.ts, usePropertyForm.ts).  
* **Pages/Screens**: PascalCase for page/screen components (e.g., LoginPage.tsx, HomeScreen.tsx).  
* **Services**: camelCase ending with Service for API client services (e.g., userService.ts, propertyService.ts).  
* **State Management**: Files related to state management (e.g., Redux slices, Zustand stores) will follow a consistent pattern within their respective directories (e.g., userStore.ts, propertySlice.ts).  
* **Styles**: If using CSS modules, kebab-case for file names (e.g., button.module.css). If using Tailwind CSS, utility classes are directly in JSX/TSX.  
* **Types/Interfaces**: PascalCase for TypeScript interfaces (e.g., IUser, IProperty or User, Property if not prefixed). Shared types will reside in packages/types.

## **State Management Architecture**

### **Store Structure**

`// Shared types for state, located in packages/types`  
`// packages/types/src/state.d.ts`  
`export interface AuthState {`  
  `isAuthenticated: boolean;`  
  `user: User | null;`  
  `loading: boolean;`  
  `error: string | null;`  
`}`

`export interface PropertiesState {`  
  `properties: Property[];`  
  `loading: boolean;`  
  `error: string | null;`  
  `filters: any; // Define more specific filter interface later`  
`}`

`// Example for a Zustand store within an app (e.g., apps/admin-web/src/stores/useAuthStore.ts)`  
`// Or for React Context (e.g., apps/admin-web/src/contexts/AuthContext.tsx)`

`// apps/web-app-name/src/stores/authStore.ts (using Zustand)`  
`import { create } from 'zustand';`  
`import { AuthState } from '@nasaga/types'; // Import shared type`

`interface AuthStore extends AuthState {`  
  `login: (user: User) => void;`  
  `logout: () => void;`  
  `setLoading: (loading: boolean) => void;`  
  `setError: (error: string | null) => void;`  
`}`

`export const useAuthStore = create<AuthStore>((set) => ({`  
  `isAuthenticated: false,`  
  `user: null,`  
  `loading: false,`  
  `error: null,`  
  `login: (user) => set({ isAuthenticated: true, user, loading: false, error: null }),`  
  `logout: () => set({ isAuthenticated: false, user: null }),`  
  `setLoading: (loading) => set({ loading }),`  
  `setError: (error) => set({ error, loading: false }),`  
`}));`

`// apps/mobile-app-name/src/stores/propertiesStore.ts (using Zustand or similar)`  
`import { create } from 'zustand';`  
`import { Property } from '@nasaga/types'; // Import shared type`

`interface PropertiesStore {`  
  `properties: Property[];`  
  `loading: boolean;`  
  `error: string | null;`  
  `setProperties: (properties: Property[]) => void;`  
  `addProperty: (property: Property) => void;`  
  `updateProperty: (property: Property) => void;`  
  `removeProperty: (propertyId: string) => void;`  
  `setLoading: (loading: boolean) => void;`  
  `setError: (error: string | null) => void;`  
`}`

`export const usePropertiesStore = create<PropertiesStore>((set) => ({`  
  `properties: [],`  
  `loading: false,`  
  `error: null,`  
  `setProperties: (properties) => set({ properties }),`  
  `addProperty: (property) => set((state) => ({ properties: [...state.properties, property] })),`  
  `updateProperty: (updatedProperty) => set((state) => ({`  
    `properties: state.properties.map((p) => p.propertyId === updatedProperty.propertyId ? updatedProperty : p),`  
  `})),`  
  `removeProperty: (propertyId) => set((state) => ({`  
    `properties: state.properties.filter((p) => p.propertyId !== propertyId),`  
  `})),`  
  `setLoading: (loading) => set({ loading }),`  
  `setError: (error) => set({ error }),`  
`}));`

### **State Management Patterns**

* **Global Application State (Auth, Theme)**: For truly global, less frequently updated state like authentication status or theme preferences, **React Context API** will be used. This is suitable for data consumed by many components but updated by few.  
* **Domain/Feature-Specific State**: For managing the state of specific domains (e.g., properties, users within an Admin panel) or features, **Zustand** is recommended. Its hook-based API provides a clean way to manage complex state, derived state, and asynchronous operations, promoting modularity.  
* **Component Local State**: useState and useReducer hooks will be used for state confined to a single component or a small, isolated subtree.  
* **Data Fetching State**: For managing the lifecycle of data fetching (loading, error, data), libraries like **React Query (TanStack Query)** could be considered for advanced caching and synchronization patterns, especially for frequently changing data or complex invalidation scenarios. For an MVP, basic useEffect with local state or Zustand actions will suffice.

## **API Integration**

### **API Client Setup**

`// packages/utils/src/apiClient.ts (Shared API client for web and mobile)`  
`import axios from 'axios';`  
`import { getAuth } from 'firebase/auth';`

`const apiClient = axios.create({`  
  `baseURL: 'https://api.nasaga.com/v1', // Base URL for the Node.js/Express API`  
  `headers: {`  
    `'Content-Type': 'application/json',`  
  `},`  
`});`

`// Request interceptor to add Firebase ID token for authenticated requests`  
`apiClient.interceptors.request.use(async (config) => {`  
  `const auth = getAuth();`  
  `const user = auth.currentUser;`  
  `if (user) {`  
    `const token = await user.getIdToken();`  
    ``config.headers.Authorization = `Bearer ${token}`;``  
  `}`  
  `return config;`  
`}, (error) => {`  
  `return Promise.reject(error);`  
`});`

`// Response interceptor for global error handling`  
`apiClient.interceptors.response.use(`  
  `(response) => response,`  
  `(error) => {`  
    `// Example of handling common API errors`  
    `if (error.response) {`  
      `if (error.response.status === 401) {`  
        `// Handle unauthorized: e.g., redirect to login`  
        `console.error('Unauthorized access. Please log in.');`  
        `// Optionally, trigger a global logout action`  
      `} else if (error.response.status === 403) {`  
        `console.error('Forbidden: You do not have permission to perform this action.');`  
      `} else {`  
        ``console.error(`API Error: ${error.response.status} - ${error.response.data.message || error.message}`);``  
      `}`  
    `} else if (error.request) {`  
      `console.error('No response received from API. Network error or server down.');`  
    `} else {`  
      `console.error('Error setting up API request:', error.message);`  
    `}`  
    `return Promise.reject(error);`  
  `}`  
`);`

`export default apiClient;`

### **Service Example**

`// apps/admin-web/src/services/userService.ts (Example for web app)`  
`import apiClient from '@nasaga/utils/apiClient';`  
`import { User, UserRole } from '@nasaga/types';`

`export const userService = {`  
  `// Example for Super Admin to update user role`  
  `updateUserRole: async (uid: string, role: UserRole): Promise<User> => {`  
    `try {`  
      ``const response = await apiClient.patch(`/admin/users/${uid}/role`, { role });``  
      `return response.data;`  
    `} catch (error) {`  
      `// Specific error handling for user role update`  
      `console.error('Failed to update user role:', error);`  
      `throw error;`  
    `}`  
  `},`

  `// Example to fetch all users (for Admin/Super Admin dashboard)`  
  `getAllUsers: async (): Promise<User[]> => {`  
    `try {`  
      `// This might be directly from Firestore SDK in some cases,`  
      `// but if complex filtering/pagination/security is needed, it goes through backend`  
      `const response = await apiClient.get('/admin/users'); // Assuming this endpoint exists`  
      `return response.data;`  
    `} catch (error) {`  
      `console.error('Failed to fetch users:', error);`  
      `throw error;`  
    `}`  
  `},`  
`};`

`// apps/agent-mobile/src/services/propertyService.ts (Example for mobile app)`  
`import apiClient from '@nasaga/utils/apiClient';`  
`import { Property, PropertyStatus } from '@nasaga/types';`

`export const propertyService = {`  
  `createProperty: async (propertyData: Partial<Property>): Promise<Property> => {`  
    `try {`  
      `const response = await apiClient.post('/properties', propertyData);`  
      `return response.data;`  
    `} catch (error) {`  
      `console.error('Failed to create property:', error);`  
      `throw error;`  
    `}`  
  `},`

  `updatePropertyStatus: async (propertyId: string, status: PropertyStatus): Promise<Property> => {`  
    `try {`  
      ``const response = await apiClient.patch(`/properties/${propertyId}/status`, { status });``  
      `return response.data;`  
    `} catch (error) {`  
      `console.error('Failed to update property status:', error);`  
      `throw error;`  
    `}`  
  `},`

  `// Agent's own properties can be fetched via Firestore SDK with security rules`  
  `// For more complex queries, may go through backend API.`  
  `getAgentProperties: async (agentUid: string): Promise<Property[]> => {`  
    `// Example of direct Firestore SDK usage for real-time lists`  
    `// This part would typically be handled within React hooks (e.g., useFirestoreCollection)`  
    `// For this architectural document, assume it can be a backend call if preferred.`  
    `// For now, representing an API client interface.`  
    `try {`  
        ``const response = await apiClient.get(`/properties?agentUid=${agentUid}`);``  
        `return response.data;`  
    `} catch (error) {`  
        `console.error('Failed to fetch agent properties:', error);`  
        `throw error;`  
    `}`  
  `},`  
`};`

## **Routing Architecture**

### **Route Configuration**

`// apps/admin-web/src/router/index.tsx (Example for React Web)`  
`import React from 'react';`  
`import { BrowserRouter as Router, Routes, Route, Navigate, Outlet } from 'react-router-dom';`  
`import { useAuthStore } from '@nasaga/admin-web/src/stores/authStore'; // Assuming a local auth store`  
`import LoginPage from '@nasaga/admin-web/src/pages/Auth/LoginPage';`  
`import DashboardPage from '@nasaga/admin-web/src/pages/Dashboard/DashboardPage';`  
`import UserManagementPage from '@nasaga/admin-web/src/pages/UserManagement/UserManagementPage';`  
`import PropertyApprovalPage from '@nasaga/admin-web/src/pages/PropertyApproval/PropertyApprovalPage';`  
`import { UserRole } from '@nasaga/types';`

`// A component to protect routes based on authentication and role`  
`interface ProtectedRouteProps {`  
  `allowedRoles: UserRole[];`  
`}`

`const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ allowedRoles }) => {`  
  `const { isAuthenticated, user } = useAuthStore();`

  `if (!isAuthenticated) {`  
    `return <Navigate to="/login" replace />;`  
  `}`

  `if (!user || !allowedRoles.includes(user.role)) {`  
    `// Optionally, redirect to an unauthorized page or dashboard`  
    `return <Navigate to="/unauthorized" replace />;`  
  `}`

  `return <Outlet />;`  
`};`

`const AppRouter: React.FC = () => {`  
  `return (`  
    `<Router>`  
      `<Routes>`  
        `<Route path="/login" element={<LoginPage />} />`  
        `<Route path="/unauthorized" element={<div>Unauthorized Access</div>} /> {/* Placeholder */}`

        `{/* Admin Routes */}`  
        `<Route element={<ProtectedRoute allowedRoles={['admin', 'superAdmin']} />}>`  
          `<Route path="/dashboard" element={<DashboardPage />} />`  
          `<Route path="/properties-approval" element={<PropertyApprovalPage />} />`  
        `</Route>`

        `{/* Super Admin Specific Routes (inherits admin access) */}`  
        `<Route element={<ProtectedRoute allowedRoles={['superAdmin']} />}>`  
          `<Route path="/users" element={<UserManagementPage />} />`  
        `</Route>`

        `<Route path="*" element={<Navigate to="/dashboard" />} /> {/* Default redirect */}`  
      `</Routes>`  
    `</Router>`  
  `);`  
`};`

`export default AppRouter;`

`// apps/agent-mobile/src/navigation/AppNavigator.tsx (Example for React Native)`  
`import React from 'react';`  
`import { NavigationContainer } from '@react-navigation/native';`  
`import { createNativeStackNavigator } from '@react-navigation/native-stack';`  
`import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';`  
`import { useAuthStore } from '@nasaga/agent-mobile/src/stores/authStore'; // Assuming a local auth store`  
`import LoginPage from '@nasaga/agent-mobile/src/screens/Auth/LoginPage';`  
`import ProfileScreen from '@nasaga/agent-mobile/src/screens/Profile/ProfileScreen';`  
`import PropertyListScreen from '@nasaga/agent-mobile/src/screens/Property/PropertyListScreen';`  
`import CreatePropertyScreen from '@nasaga/agent-mobile/src/screens/Property/CreatePropertyScreen';`  
`import PropertyDetailScreen from '@nasaga/agent-mobile/src/screens/Property/PropertyDetailScreen';`  
`import { UserRole } from '@nasaga/types';`

`const AuthStack = createNativeStackNavigator();`  
`const AppStack = createNativeStackNavigator();`  
`const Tab = createBottomTabNavigator();`

`// Main tabs for authenticated Agent users`  
`const AgentTabs = () => {`  
  `return (`  
    `<Tab.Navigator>`  
      `<Tab.Screen name="MyProperties" component={PropertyListScreen} />`  
      `<Tab.Screen name="CreateProperty" component={CreatePropertyScreen} />`  
      `<Tab.Screen name="Profile" component={ProfileScreen} />`  
    `</Tab.Navigator>`  
  `);`  
`};`

`const AppNavigator: React.FC = () => {`  
  `const { isAuthenticated, user } = useAuthStore();`

  `if (!isAuthenticated) {`  
    `return (`  
      `<NavigationContainer>`  
        `<AuthStack.Navigator screenOptions={{ headerShown: false }}>`  
          `<AuthStack.Screen name="Login" component={LoginPage} />`  
        `</AuthStack.Navigator>`  
      `</NavigationContainer>`  
    `);`  
  `}`

  `// Ensure role is 'agent' for Agent app`  
  `if (user?.role !== 'agent') {`  
    `// Optionally, show an unauthorized screen or log out`  
    `return (`  
      `<NavigationContainer>`  
        `<AuthStack.Navigator screenOptions={{ headerShown: false }}>`  
          `<AuthStack.Screen name="Unauthorized" component={() => <LoginPage />} /> {/* Redirect to login or unauthorized message */}`  
        `</AuthStack.Navigator>`  
      `</NavigationContainer>`  
    `);`  
  `}`

  `return (`  
    `<NavigationContainer>`  
      `<AppStack.Navigator screenOptions={{ headerShown: false }}>`  
        `<AppStack.Screen name="AgentMain" component={AgentTabs} />`  
        `<AppStack.Screen name="PropertyDetail" component={PropertyDetailScreen} />`  
        `{/* Add other screens that are part of the navigation flow but not in tabs */}`  
      `</AppStack.Navigator>`  
    `</NavigationContainer>`  
  `);`  
`};`

`export default AppNavigator;`

## **Styling Guidelines**

### **Styling Approach**

**Tailwind CSS** will be the primary utility-first CSS framework for both React web dashboards and, where possible, integrated into React Native applications (e.g., via tailwindcss-react-native or similar compatible solutions for React Native's StyleSheet API). This approach promotes rapid UI development, consistency, and efficient styling. For React Native, direct StyleSheet API will be used for styles that cannot be elegantly expressed with Tailwind utilities or for performance-critical native styles.

### **Global Theme Variables**

`/* packages/ui/src/theme/colors.css (for Tailwind CSS in web apps) */`  
`/* This will be imported into a CSS file that Tailwind processes */`  
`@tailwind base;`  
`@tailwind components;`  
`@tailwind utilities;`

`:root {`  
  `--color-primary-50: #EEF2FF;`  
  `--color-primary-100: #E0E7FF;`  
  `--color-primary-200: #C7D2FE;`  
  `--color-primary-300: #A5B4FC;`  
  `--color-primary-400: #818CF8;`  
  `--color-primary-500: #6366F1; /* Primary brand color */`  
  `--color-primary-600: #4F46E5;`  
  `--color-primary-700: #4338CA;`  
  `--color-primary-800: #3730A3;`  
  `--color-primary-900: #312E81;`

  `--color-secondary-50: #F0FDF4;`  
  `--color-secondary-100: #DCFCE7;`  
  `--color-secondary-200: #BBF7D0;`  
  `--color-secondary-300: #86EFAC;`  
  `--color-secondary-400: #4ADE80;`  
  `--color-secondary-500: #22C55E; /* Secondary accent color (e.g., success) */`  
  `--color-secondary-600: #16A34A;`  
  `--color-secondary-700: #15803D;`  
  `--color-secondary-800: #166534;`  
  `--color-secondary-900: #14532D;`

  `/* Neutral colors for text, backgrounds, borders */`  
  `--color-gray-50: #F9FAFB;`  
  `--color-gray-100: #F3F4F6;`  
  `--color-gray-200: #E5E7EB;`  
  `--color-gray-300: #D1D5DB;`  
  `--color-gray-400: #9CA3AF;`  
  `--color-gray-500: #6B7280;`  
  `--color-gray-600: #4B5563;`  
  `--color-gray-700: #374151;`  
  `--color-gray-800: #1F2937;`  
  `--color-gray-900: #111827;`

  `/* Semantic colors */`  
  `--color-success: var(--color-secondary-500);`  
  `--color-warning: #F59E0B; /* amber-500 */`  
  `--color-error: #EF4444; /* red-500 */`  
`}`

`/* You would then configure tailwind.config.js to extend these colors */`  
```` ```typescript ````  
`// packages/ui/src/theme/index.ts (for React Native apps and shared JS/TS access)`  
`import { TextStyle, ViewStyle } from 'react-native';`

`interface Colors {`  
  `primary: string;`  
  `secondary: string;`  
  `accent: string;`  
  `success: string;`  
  `warning: string;`  
  `error: string;`  
  `background: string;`  
  `text: string;`  
  `textMuted: string;`  
`}`

`interface Spacing {`  
  `xs: number;`  
  `sm: number;`  
  `md: number;`  
  `lg: number;`  
  `xl: number;`  
`}`

`interface Typography {`  
  `h1: TextStyle;`  
  `h2: TextStyle;`  
  `body: TextStyle;`  
  `small: TextStyle;`  
`}`

`interface Theme {`  
  `colors: Colors;`  
  `spacing: Spacing;`  
  `typography: Typography;`  
  `// Add more properties like borderRadius, shadows etc.`  
`}`

`export const lightTheme: Theme = {`  
  `colors: {`  
    `primary: '#6366F1', // Tailwind blue-500`  
    `secondary: '#22C55E', // Tailwind green-500`  
    `accent: '#818CF8', // Tailwind blue-400`  
    `success: '#22C55E',`  
    `warning: '#F59E0B',`  
    `error: '#EF4444',`  
    `background: '#FFFFFF',`  
    `text: '#111827', // Tailwind gray-900`  
    `textMuted: '#6B7280', // Tailwind gray-500`  
  `},`  
  `spacing: {`  
    `xs: 4,`  
    `sm: 8,`  
    `md: 16,`  
    `lg: 24,`  
    `xl: 32,`  
  `},`  
  `typography: {`  
    `h1: { fontSize: 32, fontWeight: 'bold', color: '#111827' },`  
    `h2: { fontSize: 24, fontWeight: 'bold', color: '#111827' },`  
    `body: { fontSize: 16, color: '#111827' },`  
    `small: { fontSize: 12, color: '#6B7280' },`  
  `},`  
`};`

`// You can export a darkTheme similarly`

## **Testing Requirements**

### **Component Test Template**

`// For React Web components (e.g., apps/admin-web/src/components/Button/Button.test.tsx)`  
`import React from 'react';`  
`import { render, screen, fireEvent } from '@testing-library/react';`  
`import Button from './Button'; // Assuming local import for testing`

`describe('Button', () => {`  
  `it('renders correctly with children', () => {`  
    `render(<Button onClick={() => {}}>Click Me</Button>);`  
    `expect(screen.getByText(/Click Me/i)).toBeInTheDocument();`  
  `});`

  `it('calls onClick when clicked', () => {`  
    `const handleClick = jest.fn();`  
    `render(<Button onClick={handleClick}>Test Button</Button>);`  
    `fireEvent.click(screen.getByText(/Test Button/i));`  
    `expect(handleClick).toHaveBeenCalledTimes(1);`  
  `});`

  `it('is disabled when the disabled prop is true', () => {`  
    `const handleClick = jest.fn();`  
    `render(<Button onClick={handleClick} disabled>Disabled Button</Button>);`  
    `expect(screen.getByText(/Disabled Button/i)).toBeDisabled();`  
    `fireEvent.click(screen.getByText(/Disabled Button/i));`  
    `expect(handleClick).not.toHaveBeenCalled();`  
  `});`  
`});`

`// For React Native components (e.g., apps/agent-mobile/src/components/Button/Button.test.tsx)`  
`import React from 'react';`  
`import { render, fireEvent } from '@testing-library/react-native';`  
`import Button from './Button'; // Assuming local import for testing`

`describe('Button', () => {`  
  `it('renders correctly with text content', () => {`  
    `const { getByText } = render(<Button onPress={() => {}}>Press Me</Button>);`  
    `expect(getByText(/Press Me/i)).toBeTruthy();`  
  `});`

  `it('calls onPress when pressed', () => {`  
    `const handlePress = jest.fn();`  
    `const { getByText } = render(<Button onPress={handlePress}>Press Me</Button>);`  
    `fireEvent.press(getByText(/Press Me/i));`  
    `expect(handlePress).toHaveBeenCalledTimes(1);`  
  `});`

  `it('is disabled when the disabled prop is true', () => {`  
    `const handlePress = jest.fn();`  
    `const { getByText } = render(<Button onPress={handlePress} disabled>Disabled Button</Button>);`  
    `expect(getByText(/Disabled Button/i).props.accessibilityState.disabled).toBe(true);`  
    `fireEvent.press(getByText(/Disabled Button/i));`  
    `expect(handlePress).not.toHaveBeenCalled();`  
  `});`  
`});`

### **Testing Best Practices**

* **Unit Tests**: Test individual components, hooks, and utility functions in isolation. Focus on the smallest testable units to ensure their correctness.  
* **Integration Tests**: Test interactions between multiple components, or between components and services. For frontend, this includes testing data fetching from API services and state updates.  
* **End-to-End (E2E) Tests**: Use Cypress (for web) and/or Playwright (for web, with potential for mobile browser-based E2E if a web-based mobile testing solution emerges) for critical user flows across the entire application, simulating real user interactions in a browser or mobile emulator. Nx provides generators for setting up Cypress/Playwright.  
* **Coverage Goals**: Aim for a reasonable code coverage target (e.g., 80% line coverage) to ensure sufficient testing, focusing on critical paths.  
* **Test Structure**: Follow the Arrange-Act-Assert (AAA) pattern for clear and readable tests.  
* **Mock External Dependencies**: Mock API calls, Firebase SDKs, routing, and global state management to ensure tests are fast, isolated, and deterministic.  
* **Accessibility Testing**: Integrate automated accessibility checks (e.g., jest-axe for React components, or Lighthouse/Pa11y in E2E tests) as part of the CI pipeline.
